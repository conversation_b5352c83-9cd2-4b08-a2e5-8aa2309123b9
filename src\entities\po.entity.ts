import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>n, ManyToOne, OneToMany } from 'typeorm'
import {
  AuctionEntity,
  BudgetReceiptEntity,
  CompanyEntity,
  CurrencyEntity,
  InboundEntity,
  OfferEntity,
  PaymentMethodEntity,
  PoProductPriceListEntity,
  PrEntity,
  RecommendedPurchaseEntity,
} from '.'
import { BaseEntity } from './base.entity'
import { BidEntity } from './bid.entity'
import { BillEntity } from './bill.entity'
import { ComplaintEntity } from './complaint.entity'
import { ContractEntity } from './contract.entity'
import { ExternalMaterialGroupEntity } from './externalMaterialGroup.entity'
import { IncotermEntity } from './incoterm.entity'
import { PaymentPoEntity } from './paymentPo.entity'
import { PaymentProgressEntity } from './paymentProgress.entity'
import { POHistoryEntity } from './poHistory.entity'
import { PoHistoryStatusExecutionEntity } from './poHistoryStatusExecution.entity'
import { PoItemEntity } from './poItem.entity'
import { PoLeadTimeEntity } from './poLeadTime.entity'
import { POMemberEntity } from './poMember.entity'
import { POProductEntity } from './poProduct.entity'
import { ShipmentPoEntity } from './shipmentPo.entity'
import { SupplierEntity } from './supplier.entity'

/** PO */
@Entity({ name: 'po' })
export class POEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 250,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  title: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.pos)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  companyId: string
  @ManyToOne(() => CompanyEntity, (p) => p.pos)
  @JoinColumn({ name: 'companyId', referencedColumnName: 'id' })
  company: Promise<CompanyEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  bidId: string
  @ManyToOne(() => BidEntity, (p) => p.pos)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid: Promise<BidEntity>

  /** Danh sách PrId */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  prIds: string

  /** PO theo đấu giá */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  auctionId: string

  @ManyToOne(() => AuctionEntity, (p) => p.pos)
  @JoinColumn({ name: 'auctionId', referencedColumnName: 'id' })
  auction: Promise<AuctionEntity>

  /** PO theo yêu cầu báo giá */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  offerId: string
  @ManyToOne(() => OfferEntity, (p) => p.pos)
  @JoinColumn({ name: 'offerId', referencedColumnName: 'id' })
  offer: Promise<OfferEntity>

  /** PO theo Hợp đồng thì có chọn thêm tiến độ thanh toán theo HĐ */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  paymentPlanId: string

  @ManyToOne(() => PaymentProgressEntity, (p) => p.pos)
  @JoinColumn({ name: 'paymentPlanId', referencedColumnName: 'id' })
  paymentPlan: Promise<PaymentProgressEntity>

  /** externalMaterialGroupId */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  externalMaterialGroupId: string
  @ManyToOne(() => ExternalMaterialGroupEntity, (p) => p.pos)
  @JoinColumn({ name: 'externalMaterialGroupId', referencedColumnName: 'id' })
  externalMaterialGroup: Promise<ExternalMaterialGroupEntity>

  /** PO theo Hợp đồng thì có chọn thêm tiến độ thanh toán theo HĐ */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  incotermId: string
  @ManyToOne(() => IncotermEntity, (p) => p.pos)
  @JoinColumn({ name: 'incotermId', referencedColumnName: 'id' })
  incoterm: Promise<IncotermEntity>

  /** Id Đơn vị tiền tệ */
  @Column({ type: 'varchar', nullable: true })
  currencyId: string
  @ManyToOne(() => CurrencyEntity, (p) => p.pos)
  @JoinColumn({ name: 'currencyId', referencedColumnName: 'id' })
  currency: Promise<CurrencyEntity>

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  @Column({
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  budgetStatus: string

  //trạng thái đơn hàng
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  orderStatus: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  operator: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  standard: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  /** Hình thức, enum ContractTypePo */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  paymentPlanType: string

  /** Nguồn tham chiếu, enum ReferenceSourcePo */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  referenceSourceType: string

  /** Số chứng từ tham chiếu */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  referenceSourceNumbers: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  createdBy: string

  /** Ghi chú từ chối */
  @Column({
    type: 'varchar',
    length: 1000,
    nullable: true,
  })
  reason: string

  /** Ngày cần giao */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  deliveryDate: Date

  /** Nơi nhận hàng */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  receivingDelivery: string

  /** Tiến độ thanh toán */
  // @OneToMany(() => PaymentProgressEntity, (p) => p.po)
  // paymentPlan: Promise<PaymentProgressEntity[]>

  @OneToMany(() => POProductEntity, (p) => p.po)
  products: Promise<POProductEntity[]>

  @OneToMany(() => POMemberEntity, (p) => p.po)
  members: Promise<POMemberEntity[]>

  @OneToMany(() => POHistoryEntity, (p) => p.po)
  poHistorys: Promise<POHistoryEntity[]>

  /** DS inbounds */
  @OneToMany(() => InboundEntity, (p) => p.po)
  inbounds: Promise<InboundEntity[]>

  /** Là PO con */
  @Column({
    nullable: true,
    default: false,
  })
  isChild: boolean

  @Column({
    type: 'varchar',
    nullable: true,
  })
  parentId: string
  // 1 PO có thể tách thành nhiều PO con
  @ManyToOne(() => POEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: POEntity
  @OneToMany(() => POEntity, (p) => p.parent)
  childs: Promise<POEntity[]>

  /** Hóa đơn */
  @OneToMany(() => BillEntity, (p) => p.po)
  bills: Promise<BillEntity[]>

  /** Danh sách phiếu điều chỉnh ngân sách */
  @OneToMany(() => BudgetReceiptEntity, (p) => p.po)
  budgetReceipts: Promise<BudgetReceiptEntity[]>

  /** Danh sách shipment của po */
  @OneToMany(() => ShipmentPoEntity, (p) => p.po)
  shipmentPos: Promise<ShipmentPoEntity[]>

  /** Danh sách khiếu nại */
  @OneToMany(() => ComplaintEntity, (p) => p.po)
  complaints: Promise<ComplaintEntity[]>

  @OneToMany(() => PaymentPoEntity, (p) => p.po)
  paymentPos: Promise<PaymentPoEntity[]>

  /** Lịch sử trạng thái thực hiện PO */
  @OneToMany(() => PoHistoryStatusExecutionEntity, (p) => p.po)
  poHistoryStatusExecutions: Promise<PoHistoryStatusExecutionEntity[]>

  @OneToMany(() => PoProductPriceListEntity, (p) => p.po)
  poProductPriceList: Promise<PoProductPriceListEntity[]>

  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  lstPrId: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  recommendedPurchaseId: string
  @ManyToOne(() => RecommendedPurchaseEntity, (p) => p.po)
  @JoinColumn({ name: 'recommendedPurchaseId', referencedColumnName: 'id' })
  recommendedPurchase: Promise<RecommendedPurchaseEntity>

  @OneToMany(() => PoLeadTimeEntity, (p) => p.po)
  poLeadTimes: Promise<PoLeadTimeEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  paymentMethodId: string

  @ManyToOne(() => PaymentMethodEntity, (p) => p.pos)
  @JoinColumn({ name: 'paymentMethodId', referencedColumnName: 'id' })
  paymentMethod: Promise<PaymentMethodEntity>

  /** Danh sách PrId */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  prId: string

  @ManyToOne(() => PrEntity, (p) => p.pos)
  @JoinColumn({ name: 'prId', referencedColumnName: 'id' })
  pr: Promise<PrEntity>

  /** PO theo Hợp đồng */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  contractId: string

  @ManyToOne(() => ContractEntity, (p) => p.pos)
  @JoinColumn({ name: 'contractId', referencedColumnName: 'id' })
  contract: Promise<ContractEntity>

  @OneToMany(() => PoItemEntity, (p) => p.po)
  poItems: Promise<PoItemEntity[]>

  @Column({
    nullable: true,
    type: 'varchar',
  })
  totalPO: string

  @Column({
    nullable: true,
    type: 'varchar',
  })
  plantId: string
}
