import { MigrationInterface, QueryRunner } from "typeorm";

export class AddPlantPo1753256848440 implements MigrationInterface {
    name = 'AddPlantPo1753256848440'

    public async up(queryRunner: QueryRunner): Promise<void> {
       
        await queryRunner.query(`ALTER TABLE "po" ADD "plantId" varchar(255)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "po" DROP COLUMN "plantId"`);
      
    }

}
