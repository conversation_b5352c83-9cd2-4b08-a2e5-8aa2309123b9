import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm'
import { POEntity, PrEntity } from '.'
import { BaseEntity } from './base.entity'

@Entity('po_pr')
export class PoPrEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: true,
  })
  poId: string

//   @ManyToOne(() => POEntity, (p) => p.poPr)
//   @JoinColumn({ name: 'poId', referencedColumnName: 'id' })
//   po: Promise<POEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  prIds: string

//   @ManyToOne(() => PrEntity, (p) => p.poPr)
//   @JoinColumn({ name: 'prIds', referencedColumnName: 'id' })
//   pr: Promise<PrEntity>


  // @Column({
  //   type: 'varchar',
  //   nullable: true,
  // })
  // prIds: string

  // danh sách item
  // @OneToMany(() => POProductEntity, (p) => p.poPr)
  // products: Promise<POProductEntity[]>
}
