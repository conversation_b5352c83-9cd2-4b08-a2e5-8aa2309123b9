/** totalPrice AS [Amount/Thành tiền], 
      origin AS [Origin/Xuất xứ], 
      manufacturer AS [Manufacturer/Nhà máy],
      toleranceRange AS [Tolerance/Dung sai] */

export const TemplateContract = {
  PAYMENT_SCHEDULE: `
    SELECT
      pp.name as [Tên Tiến độ],
      pp.[percent] as [Tiến độ thực hiện],
      pm.name as [<PERSON><PERSON><PERSON><PERSON> thức thanh toán],
      pp.[time] as [Th<PERSON>i gian thanh toán],
      pp.description as [<PERSON>hi chú]
    FROM payment_progress pp
    LEFT JOIN payment_method pm ON pp.paymentMethodId = pm.id
    WHERE pp.contractId = ?
  `,

  LATEST_DELIVERY_TIME: `
  SELECT
    lotNumber AS [Lot],
    latestDeliveryDate AS [Thời gian giao hàng trễ nhất]
  FROM contract_item
  WHERE contractId = ?

  `,
  ITEM_INFORMATION: `
  SELECT 
      totalPriceAfterTax AS [<PERSON><PERSON> hàng], 
      shortText AS [H<PERSON>ng hóa], 
      technicalSpec AS [Tiê<PERSON> chuẩn], 
      unitName AS [ĐVT], 
      quantity AS [Số lượng], 
      price AS [Đơn giá], 
      totalPrice AS [Thành tiền], 
      origin AS [Xuất xứ], 
      manufacturer AS [Nhà máy],
      toleranceRange AS [Dung sai]
     
  FROM contract_item 
  WHERE contractId = ?
  `,

  CONTRACT_INFO: `
    SELECT
  c.code AS CONTRACT_NUMBER,
  c.foreignContractCode AS FOREIGN_CONTRACT_CODE,
  c.name AS CONTRACT_NAME,
  c.nameEN AS CONTRACT_NAME_EN,
  c.contractDate AS CONTRACT_DATE,
  c.effectiveDate AS EFFECTIVE_DATE,
  c.expiredDate AS EXPIRED_DATE,
  com.name AS NAME_BUYER,
  c.addressBuyer AS ADDRESS_BUYER,
  c.telBuyer AS TEL_BUYER,
  c.faxBuyer AS FAX_BUYER,
  s.name AS NAME_SELLER,
  c.addressSeller AS ADDRESS_SELLER,
  c.emailSeller AS EMAIL_SELLER,
  c.telSeller AS TEL_SELLER,
  c.representativeSeller AS REPRESENTATIVE_SELLER,
  c.contractAmountBeforeTax AS CONTRACT_AMOUNT_BEFORE_TAX,
  c.contractAmountBeforeTaxText AS CONTRACT_AMOUNT_BEFORE_TAX_TEXT,
  c.contractValueAfterTax AS CONTRACT_VALUE_AFTER_TAX,
  c.contractAmountAfterTaxText AS CONTRACT_AMOUNT_AFTER_TAX_TEXT,
  cu.name AS CURRENCY_NAME,
  pt.name AS PAYMENT_TERM_NAME,
  c.incotermVersion AS INCOTERM_VERSION,
  c.incotermLocation AS INCOTERM_LOCATION,
  c.incotermLocationEn AS INCOTERM_LOCATION_EN,
  c.quality AS QUALITY,
  c.qualityEn AS QUALITY_EN,
  c.packaging AS PACKAGING,
  c.packagEn AS PACKAG_EN,
  c.marking AS MARKING,
  c.markingEn AS MARKING_EN,
  c.portOfLoading AS PORT_OF_LOADING,
  c.portOfDischargeEn AS PORT_OF_DISCHARGE_EN,
  c.partialDeliveryName AS PARTIAL_DELIVERY,
  c.partialDeliveryEnName AS PARTIAL_DELIVERY_EN,
  c.transshipmentName AS TRANSSHIPMENT,
  c.transshipmentEnName AS TRANSSHIPMENT_EN,
  c.documentSendingDays AS DOCUMENT_SENDING_DAYS,
  c.billOfLading AS BILL_OF_LADING,
  c.billOfLadingEn AS BILL_OF_LADING_EN,
  c.onBehalfOf AS ON_BEHALF_OF,
  c.onBehalfOfEn AS ON_BEHALF_OF_EN,
  c.signedInvoiceCount AS SIGNED_INVOICE_COUNT,
  c.packingListDetailCount AS PACKING_LIST_DETAIL_COUNT,
  c.qualityCertificateCount AS QUALITY_CERTIFICATE_COUNT,
  c.showLotName AS IS_SHOW_LOT_NUMBER,
  c.showLotEnName AS SHOW_LOT_NUMBER_EN,
  c.certificateOfOrigin AS CERTIFICATE_OF_ORIGIN,
  c.certificateOfOriginEn AS CERTIFICATE_OF_ORIGIN_EN,
  c.authorizedLocation AS AUTHORIZED_LOCATION,
  c.originalCopyCount AS ORIGINAL_COPY_COUNT,
  c.copyCount AS COPY_COUNT,
  c.insuranceAgent AS INSURANCE_AGENT,
  c.documentDeliveryTime AS DOCUMENT_DELIVERY_TIME,
  c.claimFilingTime AS CLAIM_FILING_TIME,
  c.sellerClaimResponseTime AS SELLER_CLAIM_RESPONSE_TIME,
  c.quantityClaimTime AS QUANTITY_CLAIM_TIME,
  c.warrantyTerms AS WARRANTY_TERMS,
  c.warrantyTermsEn AS WARRANTY_TERMS_EN,
  c.forceMajeureNoticeTime AS FORCE_MAJEURE_NOTICE_TIME,
  c.disputeResolutionPlace AS DISPUTE_RESOLUTION_PLACE,
  c.disputeResolutionLocationEn AS DISPUTE_RESOLUTION_LOCATION_EN,
  c.penaltyValue AS PENALTY_VALUE,
  c.penaltyPaymentTime AS PENALTY_PAYMENT_TIME,
  c.otherTerms AS OTHER_TERMS,
  c.otherTermsEn AS OTHER_TERMS_EN,
  c.deliveryPoint AS DELIVERY_POINT,
  c.deliveryPointEn AS DELIVERY_POINT_EN,
  c.destination AS DESTINATION,
  c.destinationEn AS DESTINATION_EN,
  c.freightPaymentTermName AS FREIGHT_PAYMENT_TERM,
  c.freightPaymentTermEnName AS FREIGHT_PAYMENT_TERM_EN

FROM contract c
LEFT JOIN currency cu ON cu.id = c.currencyId
LEFT JOIN payment_term pt ON pt.id = c.paymentTermId
LEFT JOIN company com ON com.id = c.companyId
LEFT JOIN supplier s ON s.id = c.supplierId
WHERE c.id = ?;

    `,
}
export const ContractAppendix = {
  PAYMENT_SCHEDULE: `
    SELECT
      pp.name as [Tên Tiến độ],
      pp.[percent] as [Tiến độ thực hiện],
      pm.name as [Phương thức thanh toán],
      pp.[time] as [Thời gian thanh toán],
      pp.description as [Ghi chú]
    FROM payment_progress pp
    LEFT JOIN payment_method pm ON pp.paymentMethodId = pm.id
    WHERE pp.contractId = ?
  `,
  CONTRACT_APPENDIX: `
  SELECT
  ca.code AS APPENDIX_NUMBER,
  ca.foreignCode AS APPENDIX_FOREIGN_CODE,
  ca.name AS APPENDIX_NAME,
  ca.nameEN AS APPENDIX_NAME_EN,
  ca.contractAppendixDate AS APPENDIX_DATE,
  ca.createdAt AS APPENDIX_CREATE_DATE,
  ca.effectiveDate AS EFFECTIVE_DATE,
  ca.expiredDate AS EXPIRED_DATE,
  ca.contractAmountBeforeTax AS CONTRACT_AMOUNT_BEFORE_TAX,
  ca.contractAmountBeforeTaxText AS CONTRACT_AMOUNT_BEFORE_TAX_TEXT,
  ca.contractValueAfterTax AS CONTRACT_VALUE_AFTER_TAX,
  ca.contractAmountAfterTaxText AS CONTRACT_AMOUNT_AFTER_TAX_TEXT,
  ca.contractValueBeforeTaxInVND AS CONTRACT_VALUE_BEFORE_TAX_VND,
  ca.contractValueAfterTaxInVND AS CONTRACT_VALUE_AFTER_TAX_VND,


  ca.bankNumber AS BANK_NUMBER,
  ca.bankUsername AS BANK_USERNAME,
  ca.bankName AS BANK_NAME,
  ca.bankBranchName AS BANK_BRANCH_NAME,
  ca.swiftCode AS SWIFT_CODE,
  ca.iban AS IBAN,


  ca.incotermVersion AS INCOTERM_VERSION,
  ca.incotermLocation AS INCOTERM_LOCATION,


  com.name AS NAME_BUYER,
  ct.addressBuyer AS ADDRESS_BUYER,
  ct.telBuyer AS TEL_BUYER,
  ct.faxBuyer AS FAX_BUYER,

 
  s.name AS NAME_SELLER,
  ct.addressSeller AS ADDRESS_SELLER,
  ct.emailSeller AS EMAIL_SELLER,
  ct.telSeller AS TEL_SELLER,
  ct.representativeSeller AS REPRESENTATIVE_SELLER,


  ct.name AS NAME_CONTRACT,


  ct.exchangeRate AS EXCHANGE_RATE,
  cu.name AS CURRENCY_NAME,


  pt.name AS PAYMENT_TERM_NAME,


  po.name AS PURCHASING_ORG_NAME,
 
  pg.name AS PURCHASING_GROUP_NAME,

  it.name AS INCOTERM_NAME


FROM contract_appendix ca
LEFT JOIN contract ct ON ca.contractId = ct.id
LEFT JOIN currency cu ON ct.currencyId = cu.id
LEFT JOIN payment_term pt ON ca.paymentTermId = pt.id
LEFT JOIN incoterm it ON ca.incotermId = it.id
LEFT JOIN purchasing_org po ON ct.purchasingOrgId = po.id
LEFT JOIN purchasing_group pg ON ct.purchasingGroupId = pg.id
LEFT JOIN company com ON ca.companyId = com.id
LEFT JOIN supplier s ON ct.supplierId = s.id
WHERE ca.id = ?
`,
  ITEM_INFORMATION: `
  SELECT 
      totalPriceAfterTax AS [Lô hàng], 
      shortText AS [Hàng hóa], 
      technicalSpec AS [Tiêu chuẩn], 
      unitName AS [ĐVT], 
      quantity AS [Số lượng], 
      price AS [Đơn giá], 
      totalPrice AS [Thành tiền], 
      origin AS [Xuất xứ], 
      manufacturer AS [Nhà máy],
      toleranceRange AS [Dung sai]
  FROM contract_item 
  WHERE contractId = ?
  `,
}
export const TemplateContractInspection = {
  CONTRACT_INSPECTION: `
    SELECT
  ci.code AS INSPECTION_CODE,
  ci.name AS INSPECTION_NAME,
  ci.nameEN AS INSPECTION_NAME_EN,
  ci.createdAt AS INSPECTION_DATE,
  ci.address AS INSPECTION_ADDRESS,
  ci.time AS INSPECTION_TIME,
  ci.status AS INSPECTION_STATUS,
  ci.object AS INSPECTION_OBJECT,
  ci.resultInspection AS INSPECTION_RESULT,
  ci.note AS INSPECTION_NOTE,
  ci.inspectionType AS INSPECTION_TYPE,
  ci.fileAttach AS INSPECTION_FILE_ATTACH,
  
  ct.name AS CONTRACT_NAME,
  ct.code AS CONTRACT_CODE,
  ct.foreignContractCode AS FOREIGN_CONTRACT_CODE,
  ct.createdAt AS CONTRACT_DATE,
  
  com.name AS NAME_BUYER,
  ct.addressBuyer AS ADDRESS_BUYER,
  ct.telBuyer AS TEL_BUYER,
  ct.faxBuyer AS FAX_BUYER,

  s.name AS NAME_SELLER,
  ct.addressSeller AS ADDRESS_SELLER,
  ct.emailSeller AS EMAIL_SELLER,
  ct.telSeller AS TEL_SELLER,
  ct.representativeSeller AS REPRESENTATIVE_SELLER,


  s.name AS SUPPLIER_NAME

FROM contract_inspection ci
LEFT JOIN contract ct ON ci.contractId = ct.id
LEFT JOIN supplier s ON ct.supplierId = s.id
LEFT JOIN company com ON com.id = ct.companyId
WHERE ci.id = ?
`,
}
export const TemplatePO = {
  INFO: `
   SELECT
    p.code AS PO_CODE,
    p.createdAt AS PO_DATE,
    p.standard AS PO_STANDARD,
    p.receivingDelivery AS PO_RECEIVING_DELIVERY, 
    p.paymentMethodId AS PO_PAYMENT_PLAN_TYPE, 
    p.description AS PO_DESCRIPTION,

    i.name AS INCOTERM_NAME,

    s.code AS SUPPLIER_CODE,
    s.name AS SUPPLIER_NAME,
    s.address AS SUPPLIER_ADDRESS,
    s.phone AS SUPPLIER_PHONE,
    s.email AS SUPPLIER_EMAIL,
    s.fax AS SUPPLIER_FAX,

    sb.bankNumber AS BANK_NUMBER,
    sb.bankUsername AS BANK_USERNAME,
    sb.swiftCode AS SWIFT_CODE,
    sb.iban AS IBAN,

    c.code AS CONTRACT_CODE,

    e.name AS EMPLOYEE_NAME,
    e.phone AS EMPLOYEE_PHONE,
    ep.name AS MANAGER_NAME,
    ep.phone AS MANAGER_PHONE

  FROM po p
  LEFT JOIN supplier s ON s.id = p.supplierId
  LEFT JOIN supplier_bank sb ON sb.supplierId = s.id
  LEFT JOIN incoterm i ON i.id = p.incotermId
  LEFT JOIN po_member pm ON pm.poId = p.id
  LEFT JOIN employee e ON e.id = p.createdBy
  LEFT JOIN employee ep ON e.id = pm.employeeId
  LEFT JOIN contract c ON c.id = p.contractId

  WHERE p.id = ?
`,

  PO: `
SELECT
  pi.materialCode AS [Mã vật tư],
  pi.materialName AS [Tên vật tư],
  pi.price AS [Đơn giá],

  pi.unitName AS [ĐVT],

  pi.quantity AS [Số lượng],
  pi.totalPrice AS [Thành tiền],
  
  po.deliveryDate AS [Ngày đề nghị giao hàng]

FROM po_item pi
LEFT JOIN po ON po.id = pi.poId
WHERE pi.poId = ?
`,
}

export const AllTemplates = {
  TemplateContract,
  ContractAppendix,
  TemplateContractInspection,
  TemplatePO,
}
