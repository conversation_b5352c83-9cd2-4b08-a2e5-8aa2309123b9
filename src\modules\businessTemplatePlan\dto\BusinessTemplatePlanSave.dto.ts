import { IsString, <PERSON>Optional, IsArray, ValidateNested, IsNumber } from 'class-validator'
import { Type } from 'class-transformer'

export class BusinessPlanCostConfigDto {
  @IsOptional()
  @IsString()
  id?: string

  costId: string

  type: string

  value: number

  valueVanilla: number

  code?: string
}

export class BusinessPlanCurrencyExchangeDto {
  @IsOptional()
  id?: string

  fromCurrency: string

  fromCurrencyId: string

  toCurrency: string

  toCurrencyId: string

  exchangeRate: number

  exchangeRateInverse: number
}

export class BusinessPlanCostValueDto {
  @IsOptional()
  @IsString()
  id?: string

  @IsString()
  name: string

  totalPrice: number

  @IsOptional()
  lstRfqConfig?: any // Có thể định nghĩa kiểu cụ thể hơn nếu biết structure
}

export class BusinessPlanDto {
  @IsOptional()
  @IsString()
  id?: string

  code?: string

  name?: string

  @IsOptional()
  @IsNumber()
  loanTerm?: number

  @IsOptional()
  @IsString()
  incotermId?: string

  @IsOptional()
  @IsString()
  paymentTermId?: string

  @IsOptional()
  @IsString()
  description?: string

  @IsOptional()
  @IsString()
  referenceType?: string

  @IsOptional()
  @IsString()
  shipmentPlanId?: string

  @IsOptional()
  @IsString()
  shipmentPlanIncotermId?: string

  @IsOptional()
  @IsString()
  shipmentPlanPriceId?: string

  @IsOptional()
  shipmentPlanPrice?: number

  @IsOptional()
  @IsString()
  shipmentFeeConditionTypeCompactId?: string

  @IsOptional()
  @IsString()
  shipmentFeeConditionTypeCompactCode?: string

  @IsOptional()
  @IsString()
  shipmentFeeConditionTypeCompactValue?: string

  @IsOptional()
  lstRfq?: any

  @IsOptional()
  @IsString()
  currentcyId?: string

  @IsOptional()
  tableConfig?: any // Có thể định nghĩa kiểu cụ thể hơn nếu biết structure

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BusinessPlanCostConfigDto)
  lstCostConfig?: BusinessPlanCostConfigDto[]

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BusinessPlanCurrencyExchangeDto)
  lstCurrencyExchage?: BusinessPlanCurrencyExchangeDto[]

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BusinessPlanCostValueDto)
  lstCostValue?: BusinessPlanCostValueDto[]

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BusinessPlanCostConfigDto)
  lstCostConfigDetail?: BusinessPlanCostConfigDto[]
}

export class BusinessTemplatePlanSaveDto {
  @IsString()
  plantId: string

  title?: string

  @IsString()
  businessTemplatePlanTypeId: string

  @IsArray()
  rfqCompactId?: string[]

  purchasingOrgId?: string

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BusinessPlanDto)
  lstBusinessPlan: BusinessPlanDto[]
}
