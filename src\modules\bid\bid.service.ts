import { Injectable, NotFoundException, MethodNotAllowedException, NotAcceptableException } from '@nestjs/common'

import { ERROR_YOU_DO_NOT_HAVE_PERMISSION, ERROR_NOT_FOUND_DATA, enumLanguage } from '../../constants'
import { EmailService } from '../email/email.service'
import {
  BidRepository,
  BidEmployeeAccessRepository,
  BidSupplierRepository,
  SupplierRepository,
  BidPrItemRepository,
  BidSupplierShipmentValueRepository,
  CompanyRepository,
  PlantRepository,
  PurchasingGroupRepository,
  MaterialRepository,
} from '../../repositories'
import * as moment from 'moment'
import { apiHelper, coreHelper } from '../../helpers'
import { enumData } from '../../constants/enumData'
import { PaginationDto, UserDto } from '../../dto'
import { Between, In, <PERSON>Null, Like, Raw, MoreThan<PERSON><PERSON><PERSON>qual, Less<PERSON>hanOrEqual, Not } from 'typeorm'
import { BidEmployeeAccessEntity, BidEntity, BidHistoryEntity } from '../../entities'
import { FlowApproveService } from '../flowApprove/flowApprove.service'
import { BidPriceSupService } from './bidPriceSup.service'
import { BidTechTradeService } from './bidTechTrade.service'
import { OrganizationalPositionService } from '../organizationalPosition/organizationalPosition.service'
import { BidRateService } from './bidRate/bidRate.service'
import { BidRateService2 } from './bidRate/bidRate2.service'
import { TranslationRepository } from '../../repositories/translation.repository'
import { OrganizationalTreeRepository } from '../../repositories/organizationalTree.repository'
import { RfqEntity } from '../../entities/rfq.entity'
import { RfqRepository, RfqDetailsRepository } from '../../repositories/rfq.repository'
import { RoleDataPermission } from '../../constants/permission'

@Injectable()
export class BidService {
  constructor(
    private readonly repo: BidRepository,
    private readonly emailService: EmailService,
    private readonly bidEmployeeAccessRepo: BidEmployeeAccessRepository,
    private readonly bidSupplierRepo: BidSupplierRepository,
    private flowService: FlowApproveService,
    private readonly bidPriceSupService: BidPriceSupService,
    private readonly companyRepository: CompanyRepository,
    private readonly materialRepository: MaterialRepository,
    private readonly supplierRepository: SupplierRepository,
    private readonly purchasingGroupRepository: PurchasingGroupRepository,
    private readonly plantRepository: PlantRepository,
    private readonly rfqDetailsRepository: RfqDetailsRepository,
    private readonly rfqRepository: RfqRepository,
    private readonly organizationalTreeRepository: OrganizationalTreeRepository,
    private readonly bidRateService: BidRateService,
    private readonly translationRepository: TranslationRepository,

    private readonly bidRateService2: BidRateService2,

    private readonly bidTechTradeService: BidTechTradeService,
    private readonly bidSupplierShipmentValueRepo: BidSupplierShipmentValueRepository,
    private organizationalPositionService: OrganizationalPositionService,

    private readonly bidPrItemRepository: BidPrItemRepository,
  ) {}

  //#region get data

  /** Lấy ds gói thầu */
  async find(user: UserDto, data: { status?: string; supplierId?: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const whereCon: any = { parentId: IsNull(), isDeleted: false }
    if (data.status) whereCon.status = data.status
    if (data.supplierId) {
      whereCon.bidSuppliers = { supplierId: data.supplierId }
    }
    const res: any = await this.repo.find({
      where: whereCon,
      relations: { bidSuppliers: { supplier: { banks: true } }, bidPr: { pr: true }, bidPrItem: true },
    })

    return res
  }

  async findWithExmat(user: UserDto, data: { exMatGrId?: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    /**Tìm ra danh sách id của gói thầu  */
    const whereCon: any = { parentId: IsNull(), exMatGroupId: data.exMatGrId, isDeleted: false }
    const res: any = await this.repo.find({
      where: whereCon,
    })
    return res
  }

  async findWithShipment(user: UserDto, data: { shipmentId?: string; biddingTypeCode?: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    /**Tìm ra danh sách id của gói thầu  */
    const whereCon: any = { parentId: IsNull(), shipmentId: data.shipmentId, biddingTypeCode: data.biddingTypeCode, isDeleted: false }
    const res: any = await this.repo.find({
      where: whereCon,
    })
    return res
  }

  async pagination(user: UserDto, data: PaginationDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    let whereCon: any = { parentId: IsNull(), isDeleted: false, isSurvey: false }

    if (data.where.serviceId) whereCon.childs = { serviceId: data.where.serviceId }
    if (data.where.status) whereCon.status = data.where.status
    if (data.where.statusTech) whereCon.statusTech = data.where.statusTech
    if (data.where.statusTrade) whereCon.statusTrade = data.where.statusTrade
    if (data.where.statusPrice) whereCon.statusPrice = data.where.statusPrice
    if (data.where.statusChooseSupplier) whereCon.statusChooseSupplier = data.where.statusChooseSupplier
    if (data.where.statusRateTech) whereCon.statusRateTech = data.where.statusRateTech
    if (data.where.statusRateTrade) whereCon.statusRateTrade = data.where.statusRateTrade
    if (data.where.statusRatePrice) whereCon.statusRatePrice = data.where.statusRatePrice

    if (data.where.type) whereCon.type = data.where.type

    if (data.where.status) whereCon.status = data.where.status
    if (data.where.statusTech) whereCon.statusTech = data.where.statusTech // yeu cau ky thuat
    if (data.where.statusPrice) whereCon.statusPrice = data.where.statusPrice // bang gia, co cau gia
    if (data.where.statusTrade) whereCon.statusTrade = data.where.statusTrade // dieu kien thuong mai
    if (data.where.statusRateTech) whereCon.statusRateTech = data.where.statusRateTech
    if (data.where.statusRateTrade) whereCon.statusRateTrade = data.where.statusRateTrade
    if (data.where.statusRatePrice) whereCon.statusRatePrice = data.where.statusRatePrice
    if (data.where.statusChooseSupplier) whereCon.statusChooseSupplier = data.where.statusChooseSupplier

    let listBidStatus = [
      enumData.BidStatus.GoiThauTam.code,
      enumData.BidStatus.ChoDuyetGoiThauTam.code,
      enumData.BidStatus.DangCauHinhGoiThau.code,
      enumData.BidStatus.DangChonNCC.code,
      enumData.BidStatus.TuChoiGoiThau.code,
      enumData.BidStatus.DangDuyetGoiThau.code,
      enumData.BidStatus.DangNhanBaoGia.code,
      enumData.BidStatus.DangCauHinhGoiThau.code,
    ]
    if (data.where.status?.length > 0) listBidStatus = data.where.status
    whereCon.status = In(listBidStatus)
    if (data.where.isSurvey) {
      whereCon.isSurvey = true
    }
    if (data.where.dateFrom && data.where.dateTo) {
      whereCon.publicDate = Between(new Date(data.where.dateFrom), new Date(data.where.dateTo))
    } else if (data.where.dateFrom) {
      whereCon.publicDate = MoreThanOrEqual(new Date(data.where.dateFrom))
    } else if (data.where.dateTo) {
      whereCon.publicDate = LessThanOrEqual(new Date(data.where.dateTo))
    }

    // Tìm theo mã số hoặc tên gói thầu
    if (data.where.name) {
      whereCon = [
        { ...whereCon, code: Like(`%${data.where.name}%`) },
        { ...whereCon, name: Like(`%${data.where.name}%`) },
      ]
    }

    if (data.where.listTargetId) whereCon.id = In(data.where.listTargetId)
    const res: any[] = await this.repo.findAndCount(
      {
        where: whereCon,
        skip: data.skip,
        take: data.take,
        order: { createdAt: 'DESC', code: 'DESC' },
        select: {
          id: true,
          createdBy: true,
          createdAt: true,
          code: true,
          biddingTypeCode: true,
          name: true,
          status: true,
          publicDate: true,
          statusTech: true,
          statusTrade: true,
          statusPrice: true,
          statusChooseSupplier: true,
          isRequestDelete: true,
          skipApprove: true,
        },
      },
      user,
      RoleDataPermission.BidNewSurvey.code,
    )
    if (res[0].length == 0) return res
    const lstId = res[0].map((c) => c.id)
    const lstEmployeeAccess: any[] = await this.bidEmployeeAccessRepo.find({
      where: { bidId: In(lstId), isDeleted: false },

      relations: { employee: true },
      select: {
        id: true,
        bidId: true,
        type: true,
        employeeId: true,
        employee: {
          id: true,
          name: true,
        },
      },
    })

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c))
    }

    const lstStatusCanEdit = [
      enumData.BidStatus.GoiThauTam.code,
      enumData.BidStatus.ChoDuyetGoiThauTam.code,
      enumData.BidStatus.DangCauHinhGoiThau.code,
      enumData.BidStatus.DangChonNCC.code,
      enumData.BidStatus.TuChoiGoiThau.code,
      enumData.BidStatus.DangDuyetGoiThau.code,
      enumData.BidStatus.DangNhanBaoGia.code,
    ]
    const lstStatusCanSetting = [
      enumData.BidStatus.DangCauHinhGoiThau.code,
      enumData.BidStatus.DangChonNCC.code,
      enumData.BidStatus.TuChoiGoiThau.code,
      enumData.BidStatus.DangDuyetGoiThau.code,
      enumData.BidStatus.DangNhanBaoGia.code,
    ]
    for (const item of res[0]) {
      item.objPermissionApprove = await this.flowService.checkCanApproveByType(user, {
        lsType: [
          enumData.FlowCode.BID.code,
          enumData.FlowCode.BIDPRICE.code,
          enumData.FlowCode.BIDTECH.code,
          enumData.FlowCode.BIDTRADE.code,
          enumData.FlowCode.BID_CHOOSE_SUPPLIER.code,
        ],
      })
      const lstAccess = lstEmployeeAccess.filter((c) => c.bidId == item.id)
      const objTech = lstAccess.find((c) => c.type === enumData.BidRuleType.Tech.code)
      item.techLst = []
      for (const employee of lstAccess) {
        if (employee.type === enumData.BidRuleType.Tech.code) {
          item.techLst.push(employee?.__employee__?.name)
        }
      }
      item.techName = item.techLst.join(', ')
      const objMpo = lstAccess.find((c) => c.type === enumData.BidRuleType.MPO.code)
      item.mpoName = objMpo?.__employee__?.name || ''

      item.isCreator = item.createdBy === user.employeeId ? true : false
      const lstAccessUser = lstAccess.filter((c) => c.employeeId === user.employeeId)
      item.isMPO = lstAccessUser.some((c) => c.type === enumData.BidRuleType.MPO.code)
      item.isMPOLeader = lstAccessUser.some((c) => c.type === enumData.BidRuleType.MPOLeader.code)
      item.isTech = lstAccessUser.some((c) => c.type === enumData.BidRuleType.Tech.code)
      item.isTechLeader = lstAccessUser.some((c) => c.type === enumData.BidRuleType.TechLeader.code)
      item.isMember = lstAccessUser.some((c) => c.type === enumData.BidRuleType.Memmber.code)
      item.isEmpOther = lstAccessUser.some((c) => c.type === enumData.BidRuleType.EmpOther.code)
      item.isOther = lstAccessUser.some((c) => c.type === enumData.BidRuleType.Other.code)

      item.statusName = dicStatus[item.status]?.name
      item.statusColor = dicStatus[item.status]?.statusColor
      item.statusBgColor = dicStatus[item.status]?.statusBgColor
      item.statusBorderColor = dicStatus[item.status]?.statusBorderColor

      // mpo có quyền như tech
      item.isTech = item.isTech || item.isMPO
      // mpoLead có quyền như techLead
      item.isTechLeader = item.isTechLeader || item.isMPOLeader

      if (lstStatusCanEdit.includes(item.status)) {
        item.isShowEditBid =
          (item.isMPO && item.status !== enumData.BidStatus.DangNhanBaoGia.code) ||
          (item.isMPOLeader && item.status == enumData.BidStatus.ChoDuyetGoiThauTam.code) ||
          (item.status == enumData.BidStatus.ChoDuyetGoiThauTam.code && item.isCreator) ||
          item.objPermissionApprove[enumData.FlowCode.BID.code]
      }
      if (lstStatusCanSetting.includes(item.status)) {
        item.isShowBidTech = item.isTech || item.isTechLeader || item.isCreator || item.objPermissionApprove[enumData.FlowCode.BIDTECH.code]
        item.isShowBidPrice = item.isMPO || item.isMPOLeader || item.isCreator || item.objPermissionApprove[enumData.FlowCode.BIDPRICE.code]
        item.isShowBidTrade = item.isMPO || item.isMPOLeader || item.isCreator || item.objPermissionApprove[enumData.FlowCode.BIDTRADE.code]
        item.isShowChoseSupplier =
          ((item.isMPO || item.isMPOLeader) &&
            item.statusTech === enumData.BidPriceStatus.DaDuyet.code &&
            (item.statusPrice === enumData.BidPriceStatus.DaTao.code || item.statusPrice === enumData.BidPriceStatus.DaTao.code) &&
            (item.statusTrade === enumData.BidTradeStatus.DaTao.code || item.statusTrade === enumData.BidTradeStatus.DaTao.code)) ||
          (item.statusTrade === enumData.BidTradeStatus.DaDuyet.code && item.statusTrade === enumData.BidTradeStatus.DaDuyet.code) ||
          item.objPermissionApprove[enumData.FlowCode.BID_CHOOSE_SUPPLIER.code]
        item.isShowBiddingFromAdminToSupplier = (item.isMPO || item.isMPOLeader) && item.status === enumData.BidStatus.DangNhanBaoGia.code
      }

      const status = item.statusChooseSupplier

      item.isShowSendMPOLeaderCheck =
        item.statusChooseSupplier === enumData.BidChooseSupplierStatus.DaChon.code &&
        item.statusChooseSupplier != enumData.BidChooseSupplierStatus.GuiDuyet.code

      item.isShowAcceptChooseSupplier = item.statusChooseSupplier === enumData.BidChooseSupplierStatus.GuiDuyet.code
      item.canAcceptSup = item.objPermissionApprove[enumData.FlowCode.BID_CHOOSE_SUPPLIER.code]

      item.isShowEditSettingRate = (item.isMPO || item.isCreator) && item.status === enumData.BidStatus.DangNhanBaoGia.code

      item.isShowSendEmail = item.isMPO || item.isMPOLeader || item.isCreator

      item.isShowCopy = item.isMPO || item.isMPOLeader || item.isCreator

      item.isShowDelete = (item.isMPO || item.isCreator) && !item.isRequestDelete && item.status === enumData.BidStatus.GoiThauTam.code

      item.isShowApproveDelete = (item.isMPOLeader || item.isCreator) && item.isRequestDelete && item.status === enumData.BidStatus.GoiThauTam.code

      if (item.statusTech === enumData.BidTechStatus.DangTao.code) {
        item.techType = 'dashed'
      }
      if (item.statusTech === enumData.BidTechStatus.TuChoi.code) {
        item.techType = 'danger'
      }
      if (item.statusTech === enumData.BidTechStatus.DaTao.code) {
        item.techType = 'warning'
      }
      if (item.statusTech === enumData.BidTechStatus.DaDuyet.code) {
        item.techType = 'success'
      }
      if (item.statusTrade === enumData.BidTradeStatus.DangTao.code) {
        item.tradeType = 'dashed'
      }
      if (item.statusTrade === enumData.BidTradeStatus.TuChoi.code) {
        item.tradeType = 'danger'
      }
      if (item.statusTrade === enumData.BidTradeStatus.DaTao.code) {
        item.tradeType = 'warning'
      }
      if (item.statusTrade === enumData.BidTradeStatus.DaDuyet.code) {
        item.tradeType = 'success'
      }

      if (item.statusPrice === enumData.BidPriceStatus.DangTao.code) {
        item.priceType = 'dashed'
      }
      if (item.statusPrice === enumData.BidPriceStatus.TuChoi.code) {
        item.priceType = 'danger'
      }
      if (item.statusPrice === enumData.BidPriceStatus.DaTao.code) {
        item.priceType = 'warning'
      }
      if (item.statusPrice === enumData.BidPriceStatus.DaDuyet.code) {
        item.priceType = 'success'
      }

      if (item.statusChooseSupplier === enumData.BidChooseSupplierStatus.DangChon.code) {
        item.chooseSupplierType = 'dashed'
      }
      if (item.statusChooseSupplier === enumData.BidChooseSupplierStatus.TuChoi.code) {
        item.chooseSupplierType = 'danger'
      }
      if (item.statusChooseSupplier === enumData.BidChooseSupplierStatus.DaChon.code) {
        item.chooseSupplierType = 'warning'
      }
      if (item.statusChooseSupplier === enumData.BidChooseSupplierStatus.DaDuyet.code) {
        item.chooseSupplierType = 'success'
      }
      // lấy danh sách duyệt
      {
        let canApproveBid = false
        const lstRoleBid = await this.flowService.getLstRole(user, {
          targetId: item.id,
          entityName: BidEntity.name,
          type: enumData.FlowCode.BID.code,
        })
        for (const role of lstRoleBid.lstApprove) {
          if (user.employeePosition.includes(role)) {
            canApproveBid = true
            break
          }
        }
      }
    }

    if (data.where.isMobile) {
      let mobileRes = [[], res[1]]

      for (let item of res[0]) {
        const { canApprove, approvalProgress } = await this.flowService.getRoleApprove(user, {
          targetId: item?.id,
          entityName: BidEntity.name,
          type: enumData.FlowCode.BID_CHOOSE_SUPPLIER.code,
        })

        mobileRes[0].push({
          id: item.id,
          bidId: item?.bidId,
          code: item.code,
          name: item.name,
          mpoName: item?.name,
          publicDate: item.publicDate,
          status: item?.status ?? '',
          statusName: 'Chờ duyệt',
          canApprove,
          approvalProgress,
        })
      }

      return mobileRes
    }

    return res
  }

  async findDetailEdit(user: UserDto, data: { id: string }) {
    const res: any = await this.repo.findOne({
      where: { id: data.id },
      relations: {
        employeeAccess: true,
        bidPrItem: true,
        bidPr: true,
        childs: { service: true, prItem: true },
        mediaFiles: true,
        prices: { material: true, materialGroup: true, externalMaterialGroup: true, uom: true, plant: true },
        bidSuppliers: { supplier: true },
      },
    })
    if (!res) throw new Error(ERROR_NOT_FOUND_DATA)
    if (res.isDeleted) throw new Error('Gói thầu đã ngưng hoạt động')

    for (let item of res?.__mediaFiles__) {
      item.url = item.fileUrl
      item.name = item.fileName
    }
    res.lstMediaFile = res?.__mediaFiles__ || []

    const lstAccess = res.__employeeAccess__ || []
    delete res.__employeeAccess__
    res.anotherRoleIds = lstAccess.filter((c) => c.type === enumData.BidRuleType.Memmber.code).map((c) => c.employeeId)
    res.otherRoleIds = lstAccess.filter((c) => c.type === enumData.BidRuleType.Other.code).map((c) => c.employeeId)

    const mpoObj = lstAccess.find((c) => c.type === enumData.BidRuleType.MPO.code)
    if (mpoObj) {
      res.mpoId = mpoObj.employeeId
      res.isMPO = mpoObj.employeeId == user.employeeId
    }

    const mpoLeaderObj = lstAccess.find((c) => c.type === enumData.BidRuleType.MPOLeader.code)
    if (mpoLeaderObj) {
      res.mpoLeadId = mpoLeaderObj.employeeId
      res.isMPOLeader = mpoLeaderObj.employeeId == user.employeeId
    }

    const techObj = lstAccess.find((c) => c.type === enumData.BidRuleType.Tech.code && c.isMember === false)
    if (techObj) {
      res.techId = techObj.employeeId
      res.isTech = techObj.employeeId == user.employeeId
    }
    res.techMemberId = []
    /* enumData.BidRuleType.Tech.code && isMember = true  */
    for (const item of lstAccess) {
      if (item.type === enumData.BidRuleType.Tech.code && item.isMember === true) {
        res.techMemberId.push(item.employeeId)
      }
    }

    const techLeaderObj = lstAccess.find((c) => c.type === enumData.BidRuleType.TechLeader.code)
    if (techLeaderObj) {
      res.techLeadId = techLeaderObj.employeeId
      res.isTechLeader = techLeaderObj.employeeId == user.employeeId
    }

    res.isShowSendMPOLeadAccept = res.status == enumData.BidStatus.GoiThauTam.code && res.isMPO
    res.isShowAcceptBid = res.status == enumData.BidStatus.ChoDuyetGoiThauTam.code
    res.isAllowPrintBid = res.isMPO || res.isMPOLeader
    {
      const lstStatusCanDeleteBid = [enumData.BidStatus.GoiThauTam.code, enumData.BidStatus.ChoDuyetGoiThauTam.code]
      res.isShowDeleteBid = lstStatusCanDeleteBid.includes(res.status) && res.isMPO
    }
    {
      const lstStatusCanEdit = [
        enumData.BidStatus.GoiThauTam.code,
        enumData.BidStatus.ChoDuyetGoiThauTam.code,
        enumData.BidStatus.DangCauHinhGoiThau.code,
        enumData.BidStatus.DangChonNCC.code,
        enumData.BidStatus.TuChoiGoiThau.code,
        enumData.BidStatus.DangDuyetGoiThau.code,
        enumData.BidStatus.DangNhanBaoGia.code,
      ]
      res.isAllowEditBid = lstStatusCanEdit.includes(res.status) && res.isMPO
    }

    res.__bidPrItem__ = res.__bidPrItem__ || []

    res.listItem = []

    for (const item of res.__bidPrItem__) {
      const unit = await item.unit
      item.materialDetail = await item.material
      item.materialCode = item.materialDetail?.code
      item.unitName = item?.__unit__?.name
      item.materialGrDetail = await item.materialGroup
      item.materialGroupName = item.materialGrDetail?.name
      if (!item.isExmatgroup) res.listItem.push(item)
    }
    delete res.__childs__

    res.businessPlanId = res.businessPlantId
    /* Tìm ra danh sách cấp duyệt và comment và người duyệt */
    res.haveProgress = false
    res.approvalProgress = await this.flowService.getListApproveDetail(user, {
      targetId: res.id,
      entityName: BidEntity.name,
      type: enumData.FlowCode.BID.code,
    })
    res.showComment = false
    /* nếu như nhân viên hiện tại chưa duyệt và nằm trong luồng duyệt thì toggle bật showComment = true */
    for (const item of res.approvalProgress) {
      if (item.approveType == user.orgPositionId && !item.approved && res.status !== enumData.BidStatus.GoiThauTam.code) {
        res.showComment = true
        break
      }
    }
    if (res.approvalProgress.length > 0) res.haveProgress = true

    for (let item of res.__prices__) {
      item.lstMaterial = [{ id: item.materialId, name: item.__material__?.name, code: item.__material__?.code }]
      item.lstPlant = [{ id: item.plantId, name: item.__plant__?.name, code: item.__plant__?.code }]
      item.lstMatGroup = [{ id: item.materialGroupId, name: item.__materialGroup__?.name, code: item.__materialGroup__?.code }]
      item.externalMaterialGroupName = item.__externalMaterialGroup__?.name
      item.lstUnit = [{ id: item.uomId, name: item.__uom__?.name, code: item.__uom__?.code }]
      item.lstExternalMatGroup = [
        { id: item.externalMaterialGroupId, name: item.__externalMaterialGroup__?.name, code: item.__externalMaterialGroup__?.code },
      ]

      item.materialCode = item.__material__?.code
      item.shortText = item.__material__?.name
      item.unitCode = item.__uom__?.code
      item.unitId = item.uomId
      delete item.__material__
      delete item.__uom__
      delete item.__plant__
      delete item.__materialGroup__
      delete item.__externalMaterialGroup__
    }
    res.lstDetail = res.__prices__

    res.lstDetail = res.__prices__ || []

    for (let item of res.__bidSuppliers__) {
      item.supplierCode = item.__supplier__?.code
      item.supplierName = item.__supplier__?.name

      delete item.__supplier__
    }

    res.lstSupplier = res.__bidSuppliers__ || []

    const data1: any = await this.repo.translate([res], enumLanguage.LanguageType.EN.code)

    delete res.__prices__
    delete res.__bidSuppliers__

    return await data1[0]
  }

  /**load danh sách gói thầu cần duyệt .... */
  async biddingRequirement(user: UserDto) {
    const conditions = {
      /// Duyệt gói thầu tạm
      waitApprovingBidTemp: {
        status: enumData.BidStatus.ChoDuyetGoiThauTam.code,
        isDeleted: false,
      },
      ///Duyệt thiết lập bảng giá, cơ cấu giá, ĐKTM,NCC
      waitApprovingSettingPrice: [{ statusChooseSupplier: enumData.BidChooseSupplierStatus.GuiDuyet.code, isDeleted: false }],
      ///Duyệt đánh giá
      waitAppRate: [
        { statusRateTech: enumData.BidTechRateStatus.DaTao.code, isDeleted: false },
        { statusRateTrade: enumData.BidTradeRateStatus.DaTao.code, isDeleted: false },
        { statusRatePrice: enumData.BidPriceRateStatus.DaTao.code, isDeleted: false },
      ],
      ///Duyệt lựa chọn NCC thắng thầu
      waitApprovingChooseSupplierWin: {
        status: enumData.BidStatus.DongThau.code,
        isDeleted: false,
      },
      /// Duyệt kết thúc thầu
      waitApprovingEndBid: {
        status: enumData.BidStatus.DangDuyetKetThucThau.code,
        isDeleted: false,
      },
      ///Duyệt yêu cầu kỹ thuật
      waitApprovingTechBid: {
        statusTech: enumData.BidTechStatus.ChoDuyet.code,
        isDeleted: false,
      },

      ///Duyệt nhà cung cấp
      waitApprovingChooseSupplier: {
        statusChooseSupplier: enumData.BidChooseSupplierStatus.DaChon.code,
        isDeleted: false,
      },
    }
    const [
      lstBidWaitApprovingBidTemp,
      lstBidWaitApprovingSettingPrice,
      lstBidWaitAppRate,
      lstBidWaitApprovingChooseSupplierWin,
      lstBidWaitApprovingEndBid,
      lstBidWaitApprovingTechBid,
      lstBidWaitApprovingChooseSupplier,
    ] = await Promise.all([
      this.repo.count({ where: conditions.waitApprovingBidTemp }),
      this.repo.count({ where: conditions.waitApprovingSettingPrice }),
      this.repo.count({ where: conditions.waitAppRate }),
      this.repo.count({ where: conditions.waitApprovingChooseSupplierWin }),
      this.repo.count({ where: conditions.waitApprovingEndBid }),
      this.repo.count({ where: conditions.waitApprovingTechBid }),
      this.repo.count({ where: conditions.waitApprovingChooseSupplier }),
    ])

    return [
      {
        group: 'BiddingStack',
        groupName: 'Đấu thầu',
        total: lstBidWaitApprovingBidTemp,
        screen: 'Template',
        name: 'Duyệt gói thầu tạm',
        params: {},
      },
      {
        group: 'BiddingStack',
        groupName: 'Đấu thầu',
        total: lstBidWaitApprovingTechBid,
        screen: 'InfoRequirement',
        name: 'Duyệt thiết lập yêu cầu kỹ thuật',
        params: {},
      },
      {
        group: 'BiddingStack',
        groupName: 'Đấu thầu',
        total: lstBidWaitApprovingSettingPrice,
        screen: 'PriceRequirement',
        name: 'Duyệt thiết lập bảng giá, cơ cấu giá, ĐKTM, NCC',
        params: {},
      },
      {
        group: 'BiddingStack',
        groupName: 'Đấu thầu',
        total: lstBidWaitApprovingChooseSupplier,
        screen: 'Contractor',
        name: 'Duyệt nhà cung cấp',
        params: {},
      },
      {
        group: 'BiddingStack',
        groupName: 'Đấu thầu',
        total: lstBidWaitApprovingEndBid,
        screen: 'EvaluationInfo',
        name: 'Duyệt đánh giá yêu cầu kĩ thuật',
        params: {},
      },
      {
        group: 'BiddingStack',
        groupName: 'Đấu thầu',
        total: lstBidWaitAppRate,
        screen: 'EvaluationPrice',
        name: 'Duyệt đánh giá bảng giá, cơ cấu giá, ĐKTM',
        params: {},
      },
      {
        group: 'BiddingStack',
        groupName: 'Đấu thầu',
        total: lstBidWaitApprovingChooseSupplierWin,
        screen: 'WinningContractor',
        name: 'Duyệt danh sách NCC thắng thầu',
        params: {},
      },
    ]
  }

  async templatePagination(user: UserDto, data: PaginationDto) {
    const where = { status: enumData.BidStatus.ChoDuyetGoiThauTam.code, isDeleted: false }
    const [result, count]: any = await this.repo.findAndCount({
      where: where,
      skip: data.skip,
      take: data.take,
      order: { code: 'DESC' },
    })

    const lstId = result.map((c) => c.id)
    const lstEmployeeAccess: any[] = await this.bidEmployeeAccessRepo.find({
      where: { bidId: In(lstId), isDeleted: false },
      relations: { employee: true },
      select: {
        id: true,
        bidId: true,
        type: true,
        employeeId: true,
        employee: {
          id: true,
          name: true,
        },
      },
    })
    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c))
    }

    for (const item of result) {
      const lstAccess = lstEmployeeAccess.filter((c) => c.bidId == item.id)
      const objTech = lstAccess.find((c) => c.type === enumData.BidRuleType.Tech.code)
      item.techName = objTech?.employee?.name || ''
      const objMpo = lstAccess.find((c) => c.type === enumData.BidRuleType.MPO.code)
      item.mpoName = objMpo?.__employee__?.name || ''
      item.statusName = enumData.BidStatus[item.status]?.name
      item.statusColor = '#ED9A1F'
      item.statusBorderColor = '#F5CA89'
      item.statusBgColor = '#FCF0DD'
    }

    if (data.where.isMobile) {
      const mobileRes = []

      for (let item of result) {
        const { canApprove, approvalProgress } = await this.flowService.getRoleApprove(user, {
          targetId: item?.id,
          entityName: BidEntity.name,
          type: enumData.FlowCode.BID.code,
        })

        mobileRes.push({
          id: item.id,
          name: item.name,
          code: item.code,
          mpoName: item.mpoName,
          publicDate: item.publicDate,
          status: item?.status ?? '',
          statusName: 'Chờ duyệt',
          approvalProgress,
          canApprove,
        })
      }

      return [mobileRes, count]
    }

    return [result, count]
  }

  async priceRequirementPagination(user: UserDto, data: PaginationDto) {
    const where = [
      // { statusPrice: enumData.BidPriceStatus.DaTao.code, isDeleted: false },
      { statusChooseSupplier: enumData.BidChooseSupplierStatus.DangChon.code, isDeleted: false },
      // { statusTrade: enumData.BidTradeStatus.DangTao.code, isDeleted: false },
    ]
    const [result, count]: any = await this.repo.findAndCount({
      where: where,
      skip: data.skip,
      take: data.take,
      order: { code: 'DESC' },
    })

    const lstId = result.map((c) => c.id)
    const lstEmployeeAccess: any[] = await this.bidEmployeeAccessRepo.find({
      where: { bidId: In(lstId), isDeleted: false },
      relations: { employee: true },
      select: {
        id: true,
        bidId: true,
        type: true,
        employeeId: true,
        employee: {
          id: true,
          name: true,
        },
      },
    })

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c))
    }

    const mobileRes = [[], count]

    for (const item of result) {
      const lstAccess = lstEmployeeAccess.filter((c) => c.bidId == item.id)
      const objTech = lstAccess.find((c) => c.type === enumData.BidRuleType.Tech.code)
      item.techName = objTech?.employee?.name || ''
      const objMpo = lstAccess.find((c) => c.type === enumData.BidRuleType.MPO.code)
      item.mpoName = objMpo?.__employee__?.name || ''

      const { canApprove, approvalProgress } = await this.flowService.getRoleApprove(user, {
        targetId: item?.id,
        entityName: BidEntity.name,
        type: enumData.FlowCode.BID_CHOOSE_SUPPLIER.code,
      })

      if (data.where.isMobile) {
        mobileRes[0].push({
          id: item.id,
          code: item.code,
          name: item.name,
          mpoName: item?.mpoName,
          publicDate: item.publicDate,
          status: item?.status ?? '',
          statusName: 'Chờ duyệt',
          canApprove,
          approvalProgress,
        })
      }
    }
    if (data.where.isMobile) {
      return mobileRes
    }
    return [result, count]
  }

  async priceRequirementDetail(user: UserDto, data: { id: string }) {
    const { canApprove, approvalProgress } = await this.flowService.getRoleApprove(user, {
      targetId: data?.id,
      entityName: BidEntity.name,
      type: enumData.FlowCode.BID_CHOOSE_SUPPLIER.code,
    })

    const listPriceAndPriceCustom = await this.bidPriceSupService.getPrice(user, data.id, true)
    const listTrade = await this.bidTechTradeService.getTrade(user, data.id, true)
    const listContractor = await this.bidPriceSupService.loadSupplierInvite(user, {
      bidId: data?.id,
      typeGetData: 1,
      isMobile: true,
    })

    return {
      ...listPriceAndPriceCustom,
      ...listTrade,
      listContractor,
      canApprove,
      approvalProgress,
    }
  }

  async evaluationPricePagination(user: UserDto, data: PaginationDto) {
    const where = [
      { statusRateTech: enumData.BidTechRateStatus.DaTao.code, isDeleted: false },
      { statusRateTrade: enumData.BidTradeRateStatus.DaTao.code, isDeleted: false },
      { statusRatePrice: enumData.BidPriceRateStatus.DaTao.code, isDeleted: false },
    ]
    const [result, count]: any = await this.repo.findAndCount({
      where: where,
      skip: data.skip,
      take: data.take,
      order: { code: 'DESC' },
    })

    const lstId = result.map((c) => c.id)
    const lstEmployeeAccess: any[] = await this.bidEmployeeAccessRepo.find({
      where: { bidId: In(lstId), isDeleted: false },
      relations: { employee: true },
      select: {
        id: true,
        bidId: true,
        type: true,
        employeeId: true,
        employee: {
          id: true,
          name: true,
        },
      },
    })

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c))
    }

    const mobileRes = [[], count]

    for (const item of result) {
      const { canApprove, approvalProgress } = await this.flowService.getRoleApprove(user, {
        targetId: item?.id,
        entityName: BidEntity.name,
        type: enumData.FlowCode.EVALUATE_RESULT_TRADE.code,
      })

      const lstAccess = lstEmployeeAccess.filter((c) => c.bidId == item.id)
      const objTech = lstAccess.find((c) => c.type === enumData.BidRuleType.Tech.code)
      item.techName = objTech?.employee?.name || ''
      const objMpo = lstAccess.find((c) => c.type === enumData.BidRuleType.MPO.code)
      item.mpoName = objMpo?.__employee__?.name || ''
      item.statusName = enumData.BidStatus[item.status]?.name
      item.canApprove = canApprove
      item.approvalProgress = approvalProgress

      if (data.where.isMobile) {
        mobileRes[0].push({
          id: item.id,
          name: item.name,
          code: item.code,
          mpoName: item.mpoName || '',
          techName: objTech?.employee?.name || '',
          publicDate: item.publicDate,
          canApprove: canApprove,
          approvalProgress: approvalProgress,
        })
      }
    }

    if (data.where.isMobile) {
      return mobileRes
    }

    return [result, count]
  }

  // Chi tiết đánh giá, giá, cơ cấu giá, điều kiện thương mại, nhà cung cấp
  async evaluationPriceDetail(user: UserDto, data: { id: string }) {
    const { canApprove, approvalProgress } = await this.flowService.getRoleApprove(user, {
      targetId: data.id,
      entityName: BidEntity.name,
      type: enumData.FlowCode.EVALUATE_RESULT_TRADE.code,
    })

    return {
      id: data.id,
      name: 'Chi tiet danh gia goi thau',
      businessPartnerGroup: '',
      supplierServiceNames: '',
      canApprove,
      approvalProgress,
    }
  }

  // Đánh giá giá, cơ cấu giá, điều kiện thương mại, (bảng giá)
  async evaluationPriceTab(user: UserDto, data: { id: string; isMobile?: boolean }) {
    const {
      listPrice: listEvaluationPrice,
      listPriceColId: listEvaluationPriceColId,
      listPriceCol: listEvaluationPriceCol,
    } = await this.bidPriceSupService.getPrice(user, data.id, true)
    // Lấy thêm người đánh giá và kết quả đánh giá

    const res = await this.bidRateService.loadPriceRate(user, data.id)

    return {
      listEvaluationPrice,
      listEvaluationPriceColId,
      listEvaluationPriceCol,
      lstBidSupplier: res?.listItem[0]?.lstBidSupplier.map((item) => {
        return {
          id: item?.id,
          supplierName: item?.supplierName,
          statusFileName: item?.statusFileName,
          scorePrice: item?.scorePrice,
          rankABCD: item?.rankABCD,
          scoreManualPrice: item?.scoreManualPrice,
          statusPriceName: item?.statusPriceName,
          isPriceValid: item?.isPriceValid,
          notePrice: item?.notePrices,
          noteFinishBidMPO: item?.noteFinishBidMPO,
        }
      }),
      lstEmployeeAccess: res.lstEmployeeAccess,
    }
  }

  // Đánh giá giá, cơ cấu giá, điều kiện thương mại, nhà cung cấp (cơ cấu giá)
  async evalutionCustomPriceTab(user: UserDto, data: { id: string; isMobile?: boolean }) {
    const { listCustomPrice: listEvaluationCustomPrice } = await this.bidPriceSupService.getPrice(user, data.id, true)
    // Lấy thêm người đánh giá và kết quả đánh giá
    /* lấy ra danh sách người đánh giá*/

    const res = await this.bidRateService.loadPriceRate(user, data.id)

    return {
      listEvaluationCustomPrice,
      lstBidSupplier: res?.listItem[0]?.lstBidSupplier,
      lstEmployeeAccess: res?.lstEmployeeAccess,
    }
  }

  // Đánh giá giá, cơ cấu giá, điều kiện thương mại, nhà cung cấp (điều kiện thương mại)
  async evaluationTradeTab(user: UserDto, data: { id: string; isMobile?: boolean }) {
    const listEvaluationTrade = await this.bidTechTradeService.getTrade(user, data.id, true)
    // Lấy thêm người đánh giá và kết quả đánh giá
    const res = await this.bidRateService.loadTradeRate(user, data.id)

    // .map((item) => {
    //   return {
    //     id: item?.id,
    //     supplierName: item?.supplierName,
    //     statusFileName: item?.statusFileName,
    //     scorePrice: item?.scorePrice,
    //     rankABCD: item?.rankABCD,
    //     scoreManualPrice: item?.scoreManualPrice,
    //     statusPriceName: item?.statusPriceName,
    //     isPriceValid: item?.isPriceValid,
    //     notePrice: item?.notePrices,
    //     noteFinishBidMPO: item?.noteFinishBidMPO,
    //   }
    // })

    return {
      listEvaluationTrade,
      lstBidSupplier: res?.listItem[0]?.lstBidSupplier,
      lstEmployeeAccess: res?.lstEmployeeAccess,
    }
  }

  // Đánh giá giá, cơ cấu giá, điều kiện thương mại, nhà cung cấp (nhà cung cấp)
  async evaluationContractorTab(user: UserDto, data: { id: string; isMobile?: boolean }) {
    const listEvaluationContractor = await this.bidPriceSupService.loadSupplierInvite(user, {
      bidId: data?.id,
      typeGetData: 1,
      isMobile: true,
    })
    // Lấy thêm người đánh giá và kết quả đánh giá

    return {
      listEvaluationContractor,
      listlistTech: [],
      lstEmployeeAccess: [],
    }
  }

  // Danh sách nhà cung cấp thắng thầu
  async winningContractorPagination(user: UserDto, data: PaginationDto) {
    const where = {
      status: enumData.BidStatus.DongThau.code,
      isDeleted: false,
    }
    const [result, count]: any = await this.repo.findAndCount({
      where: where,
      skip: data.skip,
      take: data.take,
      order: { code: 'DESC' },
      relations: {
        bidSuppliers: { supplier: true },
      },
    })

    const mobileRes = [[], count]

    for (const item of result) {
      const lstBidSuppl = await item.bidSuppliers
      const supplierIsBidSuccess = lstBidSuppl.find((c) => c.isSuccessBid === true)
      const sup = await supplierIsBidSuccess.supplier

      const { canApprove, approvalProgress } = await this.flowService.getRoleApprove(user, {
        targetId: item?.id,
        entityName: BidEntity.name,
        type: enumData.FlowCode.SUPPLIER_WIN_BID.code,
      })

      mobileRes[0].push({
        id: item.id,
        bidId: item?.bidId,
        name: (await sup?.name) || '',
        scoreTech: supplierIsBidSuccess.scoreTech.toString() || 0,
        scoreManualTech: supplierIsBidSuccess.scoreManualTech.toString() || 0,
        scorePrice: supplierIsBidSuccess.scorePrice.toString() || 0,
        scoreManualPrice: supplierIsBidSuccess.scoreManualPrice.toString() || 0,
        publicDate: item?.publicDate,
        ranking: `${1}/${lstBidSuppl.length}`,
        status: item?.status ?? '',
        canApprove,
        approvalProgress,
      })
    }

    if (data.where.isMobile) {
      return mobileRes
    }

    return [result, count]
  }

  async evaluationInfoPagination(user: UserDto, data: PaginationDto & { isMobile?: boolean; mobileScreen?: string }) {
    const where = {
      status: enumData.BidStatus.DangDuyetKetThucThau.code,
      isDeleted: false,
    }
    const [result, count]: any = await this.repo.findAndCount({
      where: where,
      skip: data.skip,
      take: data.take,
      order: { code: 'DESC' },
      relations: {
        bidSuppliers: { supplier: true },
      },
    })

    const lstId = result.map((c) => c.id)
    const lstEmployeeAccess: any[] = await this.bidEmployeeAccessRepo.find({
      where: { bidId: In(lstId), isDeleted: false },
      relations: { employee: true },
      select: {
        id: true,
        bidId: true,
        type: true,
        employeeId: true,
        employee: {
          id: true,
          name: true,
        },
      },
    })

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c))
    }

    for (const item of result) {
      const lstAccess = lstEmployeeAccess.filter((c) => c.bidId == item.id)
      const objTech = lstAccess.find((c) => c.type === enumData.BidRuleType.Tech.code)
      item.techName = objTech?.__employee__?.name || ''
      const objMpo = lstAccess.find((c) => c.type === enumData.BidRuleType.MPO.code)
      item.mpoName = objMpo?.employee?.name || ''
      item.statusName = enumData.BidStatus[item.status]?.name
      item.statusColor = '#ED9A1F'
      item.statusBorderColor = '#F5CA89'
      item.statusBgColor = '#FCF0DD'

      if (data?.isMobile) {
        const { canApprove, approvalProgress } = await this.flowService.getRoleApprove(user, {
          targetId: item?.id,
          entityName: BidEntity.name,
          type: enumData.FlowCode.EVALUATE_RESULT_CAPACITY.code,
        })
        item.canApprove = canApprove
        item.approvalProgress = approvalProgress
      }
    }

    return [result, count]
  }
  async infoRequirementPagination(user: UserDto, data: PaginationDto) {
    const where = {
      statusTech: enumData.BidTechStatus.ChoDuyet.code,
      isDeleted: false,
    }
    const [result, count]: any = await this.repo.findAndCount({
      where: where,
      skip: data.skip,
      take: data.take,
      order: { code: 'DESC' },
      relations: {
        bidSuppliers: { supplier: true },
      },
    })

    const lstId = result.map((c) => c.id)
    const lstEmployeeAccess: any[] = await this.bidEmployeeAccessRepo.find({
      where: { bidId: In(lstId), isDeleted: false },
      relations: { employee: true },
      select: {
        id: true,
        bidId: true,
        type: true,
        employeeId: true,
        employee: {
          id: true,
          name: true,
        },
      },
    })

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c))
    }

    const mobileRes = [[], count]

    for (const item of result) {
      const lstAccess = lstEmployeeAccess.filter((c) => c.bidId === item.id)

      const objTech = lstAccess.find((c) => c.type === enumData.BidRuleType.Tech.code)
      item.techName = objTech?.__employee__?.name || ''

      const objMpo = lstAccess.find((c) => c.type === enumData.BidRuleType.MPO.code)
      item.mpoName = objMpo?.employee?.name || ''

      const { canApprove, approvalProgress } = await this.flowService.getRoleApprove(user, {
        targetId: item?.id,
        entityName: BidEntity.name,
        type: enumData.FlowCode.BIDTECH.code,
      })

      mobileRes[0].push({
        id: item?.id,
        name: item?.name,
        code: item?.code,
        techName: item?.techName,
        mpoName: item?.mpoName,
        publicDate: item?.publicDate,
        status: item?.status ?? '',
        statusName: 'Chờ duyệt',
        canApprove,
        approvalProgress,
      })
    }

    if (data.where.isMobile) {
      return mobileRes
    }

    return [result, count]
  }

  async getBidStatus(user: UserDto, id: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const res: any = await this.repo.findOne({ where: { id, companyId: user.companyId }, select: { id: true, status: true } })
    if (!res) throw new Error(ERROR_NOT_FOUND_DATA)

    return res
  }

  //#endregion

  //#region Mở thầu

  /** Kiểm tra quyền mở thầu */
  async checkPermissionOpenBid(user: UserDto, bidId: string) {
    let result = false
    let message = 'Gói thầu không còn tồn tại'
    const bid = await this.repo.findOne({
      where: [{ id: bidId, parentId: IsNull(), isDeleted: false }, { childs: { id: bidId, isDeleted: false } }],
    })
    if (bid) {
      if (bid.status === enumData.BidStatus.DangNhanBaoGia.code) {
        // result = await this.bidEmployeeAccessRepo.isMPO_MPOLeader(user, bid.id)
        result = true
        if (!result) {
          message = 'Bạn không có quyền mở thầu.'
        }
      } else {
        result = false
        message = 'Gói thầu không được mở!'
      }
    }

    return { hasPermission: result, message }
  }

  /** Load ds Doanh nghiệp khi mở thầu */
  async loadBidSupplierOpenBid(user: UserDto, data: { bidId: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const isGetListAccess = true
    const res: any = await this.repo.getBid1(user, data.bidId, isGetListAccess)
    res.bidOpenDate = new Date()
    if (res.status != enumData.BidStatus.DangNhanBaoGia.code) {
      res.bidOpenDate = res.bidOpenDate || res.startBidDate
    }
    res.listBidSupplier = await this.bidSupplierRepo.find({
      where: { bidId: data.bidId, isDeleted: false },
      relations: { supplier: true },
      select: { id: true, supplierId: true, status: true, supplier: { id: true, code: true, name: true } },
    })

    for (const item of res.listItem) {
      // Trạng thái nộp thầu các Doanh nghiệp theo từng Item
      item.listBidSupplier = await this.bidSupplierRepo.find({
        where: { bidId: item.bidId, isDeleted: false },
        select: { id: true, supplierId: true, status: true, isSuccessBid: true, statusFile: true },
      })
      for (const bidSupplierItem of item.listBidSupplier) {
        bidSupplierItem.isSubmitBid = false
        bidSupplierItem.statusName = 'Chưa bổ sung hồ sơ thầu'
        bidSupplierItem.isValid = bidSupplierItem.statusFile == enumData.BidSupplierFileStatus.HopLe.code
        if (
          bidSupplierItem.status === enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code ||
          bidSupplierItem.status === enumData.BidSupplierStatus.DangDanhGia.code ||
          bidSupplierItem.status === enumData.BidSupplierStatus.DaDanhGia.code
        ) {
          bidSupplierItem.isSubmitBid = true
          bidSupplierItem.statusName = 'Đã bổ sung hồ sơ thầu'
        }
      }
    }

    res.numOfSupplier = res.listBidSupplier.length
    res.numOfSupplierConfirmJoin = 0
    res.numOfSupplierSubmitBid = 0
    for (const bidSupplier of res.listBidSupplier) {
      bidSupplier.supplierCode = bidSupplier.__supplier__.code
      bidSupplier.supplierName = bidSupplier.__supplier__.name
      delete bidSupplier.__supplier__

      // Trạng thái nộp thầu các Item theo từng Doanh nghiệp
      bidSupplier.listItem = []

      const bidSupplierItem = res.listBidSupplier.find((c: any) => c.supplierId == bidSupplier.supplierId)
      if (bidSupplierItem) {
        res.statusName = bidSupplierItem.statusName
        res.isSubmitBid = bidSupplierItem.isSubmitBid
        res.isValid = bidSupplierItem.isValid
        res.bidSupplierId = bidSupplierItem.id
        // if (res.isExmatgroup)
        bidSupplier.listItem.push({
          id: res.id,
          itemName: res.itemName,
          statusName: bidSupplierItem.statusName,
          isSubmitBid: bidSupplierItem.isSubmitBid,
          isValid: bidSupplierItem.isValid,
          bidSupplierId: bidSupplierItem.id,
        })
      }

      // Lấy "Trạng thái nộp thầu Doanh nghiệp"
      const numItem = bidSupplier.listItem.length
      const numSubmitBid = bidSupplier.listItem.filter((c: any) => c.isSubmitBid).length
      const numValid = bidSupplier.listItem.filter((c: any) => c.isValid).length
      if (numSubmitBid == 0) {
        bidSupplier.statusSubmitBid = `Chưa nộp hồ sơ thầu`
      } else {
        res.numOfSupplierSubmitBid++
        bidSupplier.statusSubmitBid = `Đã nộp hồ sơ ${numSubmitBid}/${numItem} Item`
      }
      bidSupplier.statusSubmitValid = `${numValid}/${numItem} Hồ sơ`

      bidSupplier.statusConfirmJoinBid = ''
      if (bidSupplier.status == enumData.BidSupplierStatus.DaThongBaoMoiThau.code) {
        bidSupplier.statusConfirmJoinBid = 'Chưa xác nhận'
      } else if (bidSupplier.status == enumData.BidSupplierStatus.DaXacNhanKhongThamGiaThau.code) {
        bidSupplier.statusConfirmJoinBid = 'Từ chối tham gia'
      } else {
        res.numOfSupplierConfirmJoin++
        bidSupplier.statusConfirmJoinBid = 'Đồng ý tham gia'
      }
    }

    for (const employee of res.lstEmployeeTech) {
      const employeeDetail = await employee.employee
      let depName = ''
      if (employee.orgDepartmentId) {
        const treeNode = await this.organizationalTreeRepository.findOne({ where: { id: employee.orgDepartmentId } })
        depName = treeNode.name
      }
      employee.name = employeeDetail.name
      employee.email = employeeDetail.email
      employee.codeCenter = depName
    }

    for (const employee of res.lstEmployeeOther) {
      const employeeDetail = await employee.employee
      let depName = ''
      if (employee.orgDepartmentId) {
        const treeNode = await this.organizationalTreeRepository.findOne({ where: { id: employee.orgDepartmentId } })
        depName = treeNode.name
      }
      employee.name = employeeDetail.name
      employee.email = employeeDetail.email
      employee.codeCenter = depName
    }

    delete res.listItem
    return res
  }

  /** Xác nhận mở thầu */
  async openBid(
    user: UserDto,
    data: {
      bidId: string
      lstData: any[]
      lstEmployeeId: string[]
      lstEmployeeTech: any[]
      lstEmployeeOther: any[]

      isMemberScore: boolean
      isOtherCouncil: boolean
      isPersonalScoreVisible: boolean
    },
  ) {
    const bid = await this.repo.findOne({ where: { id: data.bidId } })
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionOpenBid(user, bid.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    for (const bidSupplier of data.lstData) {
      for (const item of bidSupplier.listItem) {
        const bidSupplierItem = await this.bidSupplierRepo.findOne({
          where: { id: item.bidSupplierId, companyId: user.companyId },
          select: { id: true, status: true },
        })
        if (bidSupplierItem) {
          let status = bidSupplierItem.status
          let statusFile = enumData.BidSupplierFileStatus.KhongHopLe.code
          if (item.isValid) {
            status = enumData.BidSupplierStatus.DangDanhGia.code
            statusFile = enumData.BidSupplierFileStatus.HopLe.code
          }
          await this.bidSupplierRepo.update(item.bidSupplierId, {
            status,
            statusFile,
            note: item.note,
            updatedBy: user.id,
          })
        }
      }
    }

    await this.repo.update(bid.id, {
      status: enumData.BidStatus.DangDanhGia.code,
      statusRateTech: enumData.BidTechRateStatus.DangTao.code,
      statusRateTrade: enumData.BidTradeRateStatus.DangTao.code,
      statusRatePrice: enumData.BidPriceRateStatus.DangTao.code,
      bidOpenDate: new Date(),
      updatedBy: user.id,
    })
    // Lưu hội đồng kĩ thuật nếu gộp chung thì hội đồng kỹ thuật là hội đồng giá, cơ cấu giá, ĐKTM luôn
    await this.saveRole(user, bid.id, data.lstEmployeeTech, this.bidEmployeeAccessRepo, enumData.BidRuleType.EmpTech.code)
    if (!data.isOtherCouncil) {
      // Lưu hội đồng giá, cơ cấu giá, ĐKTM
      await this.saveRole(user, bid.id, data.lstEmployeeOther, this.bidEmployeeAccessRepo, enumData.BidRuleType.EmpOther.code)
    } else {
      await this.saveRole(user, bid.id, data.lstEmployeeTech, this.bidEmployeeAccessRepo, enumData.BidRuleType.EmpOther.code)
    }
    // Cập nhật lại gói thầu
    await this.repo.update(bid.id, {
      isMemberScore: data.isMemberScore,
      isOtherCouncil: data.isOtherCouncil,
      isPersonalScoreVisible: data.isPersonalScoreVisible,
      updatedBy: user.id,
    })
    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.MoThau.code
    bidHistory.save()
    // {}
    // insert

    // gửi email nội bộ
    this.emailService.ThongBaoMoThau(data.bidId)

    // gửi email Doanh nghiệp
    this.emailService.ThongBaoMoThauNCC(data.bidId)
    // throw new Error('k có')
    return { message: 'Mở thầu thành công.' }
  }

  async saveRole(user: UserDto, bidId: string, lstEmployee: any[], bidEmployeeAccessRepository: BidEmployeeAccessRepository, type: string) {
    for (const item of lstEmployee) {
      const empOtherAccess = new BidEmployeeAccessEntity()
      empOtherAccess.companyId = user.companyId
      empOtherAccess.createdBy = user.id
      empOtherAccess.bidId = bidId
      empOtherAccess.employeeId = item.id
      empOtherAccess.type = type
      empOtherAccess.isScore = true
      await bidEmployeeAccessRepository.save(empOtherAccess)
    }
  }
  //#endregion

  //#region gửi email thông báo

  /** Kiểm tra quyền gửi email thông báo */
  async checkPermissionSendEmailBid(user: UserDto, bidId: string) {
    let result = false
    let message = 'Gói thầu không còn tồn tại'
    const bid = await this.repo.findOne({
      where: [{ id: bidId, parentId: IsNull(), isDeleted: false }, { childs: { id: bidId, isDeleted: false } }],
    })
    if (bid) {
      result = await this.bidEmployeeAccessRepo.isMPO_MPOLeader(user, bid.id)
      if (!result) {
        message = 'Bạn không có quyền gửi email thông báo nội bộ và Doanh nghiệp.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Lấy ds Doanh nghiệp được mời thầu */
  async findBidSupplier(user: UserDto, data: { bidId: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const lstData: any[] = await this.bidSupplierRepo.find({
      where: { bidId: data.bidId, isDeleted: false },
      relations: { supplier: true },
      select: { id: true, bidId: true, supplierId: true, supplier: { code: true, name: true, status: true, createdAt: true } },
    })

    const res: any[] = []
    for (const item of lstData) {
      res.push({
        id: item.supplierId,
        code: item.__supplier__.code,
        name: item.__supplier__.name,
        statusName: enumData.SupplierStatus[item.__supplier__.status]?.name,
        createdAt: item.__supplier__.createdAt,
      })
    }

    return res
  }

  /*
  Lấy ra danh sách item||service của gói thầu được chọn
  Lấy từ trong bảng bidPrItem.
  Nếu gói thầu chỉ có exMAtgr thì gói thầu này item chính là externalMatGr
  Nếu gói thầu có Pr thì item là sum từ 1 hoặc nhiều Pr lại với nhau, tham chiếu theo prItemId
   */
  async findBidService(user: UserDto, data: { bidId: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    /* Kiểm tra xem có tồn tại gói thầu được truyền vào là loại gói thầu theo exmatgr hay theo Pr*/
    let isBitExternal = true
    const bid: any = await this.repo.findOne({
      where: { id: data.bidId, parentId: IsNull(), isDeleted: false },
      relations: { bidPrItem: true },
    })
    if (!bid) throw new NotFoundException('Gói thầu không tồn tại.')
    const res = []
    /* Nếu gói thầu có danh sách Pr thì nó đi theo Pr không phải đi theo exmatGr*/
    const lstPr = await bid.bidPr
    if (lstPr.length > 0) isBitExternal = false
    /* Duyệt qua danh sách item của gói thầu */
    for (const item of bid.__bidPrItem__) {
      /* Nếu là gói thầu theo Exmatgr thì chỉ lấy những item có giá trị isExmatgroup = true */
      if (isBitExternal && item.isExmatgroup) {
        item.name = (await item.service).name
        res.push(item)
      }
      /* Nếu không thì ngược lại*/
      if (!isBitExternal && !item.isExmatgroup) {
        item.name = (await item.prItem)?.name
        res.push(item)
      }
    }
    return res
  }

  async findBidPr(user: UserDto, data: { ltsBidId: [] }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const ltsBidPr: any = await this.repo.find({
      where: { id: In(data.ltsBidId), parentId: IsNull(), isDeleted: false },
      relations: { bidPr: { pr: true } },
    })

    const res = []
    for (const item of ltsBidPr) {
      for (const item1 of item.__bidPr__) {
        if (item1.__pr__) {
          res.push(item1.__pr__)
        }
      }
    }
    return res
  }
  async loadBidByPr(user: UserDto, data: { ltsBidId: any[] }) {
    const res: any = await this.repo.find({
      where: { id: In(data.ltsBidId), isDeleted: false },
      relations: { pr: true },
    })

    const result = []

    for (const bid of res) {
      let prId: any[] = []
      let prCode: any[] = []
      let allPrIds: any[] = []

      if (bid.prId) {
        prId = bid.prId
          .split(',')
          .map((id: string) => id.trim())
          .filter((id) => id !== '')
        prCode = bid.__pr__?.code ? [bid.__pr__.code] : []
      }
      allPrIds = Array.from(new Set([...prId]))

      result.push({
        allPrIds,
        prCode,
      })
    }

    return result
  }

  async loadBidBySupplier(user: UserDto, data: { bidId: string }, lan?: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const ltsBidSupplier: any = await this.repo.find({
      where: { id: data.bidId, isDeleted: false },
      relations: { bidSuppliers: { supplier: { banks: true } } },
    })

    const res = []
    for (const item of ltsBidSupplier) {
      const filteredBidSuppliers = item.__bidSuppliers__.filter((bidSupplier: any) => bidSupplier.isSuccessBid === true)
      for (const item1 of filteredBidSuppliers) {
        if (item1.__supplier__) {
          res.push(item1.__supplier__)
        }
      }
    }
    return await this.repo.translate(res, lan)
  }

  /** Lấy ds Doanh nghiệp khi nộp thầu trang admin */
  async loadBidSupplier(user: UserDto, data: { bidId: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const res: any = {}
    // DS Doanh nghiệp được mời thầu
    res.listBidSupplier = await this.bidSupplierRepo.find({
      where: { bidId: data.bidId, isDeleted: false },
      relations: { supplier: true },
      select: { id: true, bidId: true, supplierId: true, supplier: { code: true, name: true } },
    })
    const setStatus = new Set()
    setStatus.add(enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code)
    setStatus.add(enumData.BidSupplierStatus.DangDanhGia.code)
    setStatus.add(enumData.BidSupplierStatus.DaDanhGia.code)

    for (const bidSupplier of res.listBidSupplier) {
      bidSupplier.supplierCode = bidSupplier.__supplier__.code
      bidSupplier.supplierName = bidSupplier.__supplier__.name
      delete bidSupplier.__supplier__

      // Trạng thái nộp thầu các Item theo từng Doanh nghiệp
      bidSupplier.listItem = await this.bidSupplierRepo.find({
        where: { bidId: bidSupplier.bidId, supplierId: bidSupplier.supplierId, isDeleted: false },
        relations: { bid: { service: true } },
        select: {
          id: true,
          bidId: true,
          supplierId: true,
          status: true,
          isSuccessBid: true,
          statusFile: true,
          bid: { id: true, service: { code: true, name: true } },
        },
      })
      for (const item of bidSupplier.listItem) {
        item.itemName = ''
        delete item.__bid__

        item.isValid = item.statusFile == enumData.BidSupplierFileStatus.HopLe.code
        item.isSubmitBid = false
        item.statusName = 'Chưa bổ sung hồ sơ thầu'
        if (setStatus.has(item.status)) {
          item.isSubmitBid = true
          item.statusName = 'Đã bổ sung hồ sơ thầu'
        }
      }

      // Lấy "Trạng thái nộp thầu Doanh nghiệp"
      const numItem = bidSupplier.listItem.length
      const numSubmitBid = bidSupplier.listItem.filter((c: any) => c.isSubmitBid).length
      const numValid = bidSupplier.listItem.filter((c: any) => c.isValid).length
      if (numSubmitBid == 0) {
        bidSupplier.statusSubmitBid = `Chưa nộp hồ sơ thầu`
      } else {
        bidSupplier.statusSubmitBid = `Đã nộp hồ sơ ${numSubmitBid}/${numItem} Item`
      }
      bidSupplier.statusSubmitValid = `${numValid}/${numItem} Hồ sơ`
    }

    return res
  }

  /** Gửi email nội bộ và NCC được mời tham gia thầu */
  async sendEmailBid(user: UserDto, data: { bidId: string; lstEmployeeId: string[]; lstSupplierId: string[]; emailContent: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionSendEmailBid(user, data.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    this.emailService.sendEmailBid(data)
  }

  /**Mobile: Hàm lấy danh gói thầu chờ duyệt */
  public async bidRequirement(user: UserDto, data: PaginationDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const listRequirementStack: any[] = await this.requirementStack(user, data)

    const totalRequirementStack = listRequirementStack.reduce((sum, item) => sum + (item.totalRequirementScreen || 0), 0)
    return {
      stack: 'MoreStack',
      name: 'Duyệt đấu thầu',
      totalRequirementStack,
      listRequirementStack,
    }
  }

  public async requirementStack(user: UserDto, data: PaginationDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const listWheCon = [
      {
        where: { status: enumData.BidStatus.ChoDuyetGoiThauTam.code },
        screen: 'Temporary',
        name: 'Duyệt gói thầu tạm',
      },
      {
        where: { statusTech: enumData.BidTechStatus.ChoDuyet.code },
        screen: 'InfoRequirement',
        name: 'Duyệt thiết lập yêu cầu kỹ thuật',
      },
      {
        where: [{ statusPrice: enumData.BidPriceStatus.DaTao.code }, { statusTrade: enumData.BidTradeStatus.DaTao.code }],
        screen: 'PriceRequirement',
        name: 'Duyệt thiết lập bảng giá, cơ cấu giá, ĐKTM',
      },
      {
        where: { statusChooseSupplier: enumData.BidChooseSupplierStatus.DaChon.code },
        screen: 'Contractor',
        name: 'Duyệt nhà cung cấp',
      },
      {
        where: { statusRateTech: enumData.BidTechRateStatus.DaTao.code },
        screen: 'EvaluationInfo',
        name: 'Duyệt đánh giá yêu cầu kỹ thuật',
      },
      {
        where: [{ statusRatePrice: enumData.BidPriceRateStatus.DaTao.code }, { statusRateTrade: enumData.BidTradeRateStatus.DaTao.code }],
        screen: 'EvaluationPrice',
        name: 'Duyệt đánh giá bảng giá, cơ cấu giá, ĐKTM',
      },
      {
        where: { status: enumData.BidStatus.DongThau.code },
        screen: 'WinningContractor',
        name: 'Duyệt danh sách NCC thắng thầu',
      },
    ]

    const findAndCount = listWheCon.map((whereCon) =>
      this.repo
        .findAndCount({
          where: whereCon.where,
          order: { code: 'DESC' },
          select: { id: true, code: true, name: true, status: true },
        })
        .then(([list, count]) => ({
          screen: whereCon.screen,
          name: whereCon.name,
          totalRequirementScreen: count,
          params: {},
        })),
    )

    const listRequirementStack = await Promise.all(findAndCount)

    return listRequirementStack
  }

  public async loadBidSupplierFromBid(user: UserDto, data: { bidId: string }) {
    const res: any = await this.bidSupplierRepo.find({
      where: {
        bidId: data.bidId,
      },
      relations: { supplier: true },
    })
    for (let item of res) {
      item.supplierName = item.__supplier__.name
      delete item.__supplier__
    }
    return res
  }

  public async loadSupplierShipmentValue(user: UserDto, data: { bidSupplierId: string }) {
    const res: any = await this.bidSupplierShipmentValueRepo.find({
      where: {
        bidSupplierId: data.bidSupplierId,
      },
      relations: { shipmentPrice: { shipmentCostStageCost: true } },
    })
    for (let item of res) {
      const json = item.__shipmentPrice__.__shipmentCostStageCost__
      item.isCheck = false
      item.conditionType = json.conditionType
      item.description = json.description
      item.amount = json.amount
      item.crcy = json.crcy
      item.per = json.per
      item.conditionValue = json.conditionValue
      item.curr = json.curr
      item.numCCo = json.numCCo
      item.cConDe = json.cConDe
      item.id = item.__shipmentPrice__.__shipmentCostStageCost__.id
      item.jsonCost = item.__shipmentPrice__.__shipmentCostStageCost__.jsonCost

      item.costExpect = +item.value
      delete item.__shipmentPrice__.__shipmentCostStageCost__
    }
    return res
  }

  public async loadListitemFromBid(user: UserDto, data: { bidId: string }) {
    const res: any = await this.bidPrItemRepository.find({
      where: {
        bidId: data.bidId,
      },
      relations: { material: true, unit: true },
    })

    for (let item of res) {
      item.materialName = item.__material__?.name
      item.unitName = item.__unit__?.name

      delete item.__material__
      delete item.__unit__
    }
    return res
  }

  //#endregion

  public async updateRfqToSap() {
    const lstRfqNotSync = await this.rfqRepository.find({ where: { isSynchronizing: false } })
    /* lấy ra danh sách detail chưa up  */
    const lstRfqDetailNotSync = await this.rfqDetailsRepository.find({ where: { isSynchronizing: false } })
    /* lấy ra detail material*/
    const dicMaterial: any = {}
    const lstMaterial = await this.materialRepository.find({ where: { isDeleted: false } })

    for (const material of lstMaterial) {
      dicMaterial[material.id] = material
    }

    const dicRfq: any = {}
    /* từ plant chọn ra được */
    /* lấy thông tin của Bid */
    const lstBidId = lstRfqNotSync.map((c) => c.targetId)
    const lstSupplierId = lstRfqNotSync.map((c) => c.supplierId)

    /* lấy thông tin của Bid */
    const lstPlantId = []
    const lstBid: any = await this.repo.find({ where: { id: In(lstBidId) }, relations: { bidPr: { pr: true } } })
    /* lấy thông tin của supplier */
    const lstSupplier: any = await this.supplierRepository.find({ where: { id: In(lstBidId) } })

    const dicBid = {}
    const dicSupplier = {}
    for (const item of lstSupplier) {
      dicSupplier[item.id] = item
    }

    for (const item of lstBid) {
      dicBid[item.id] = item
      const bid = item
      const bidLstPr = bid.__bidPr__
      const bidPr = bidLstPr[0].__pr__
      lstPlantId.push(bidPr.plantId)
    }

    const plantDetail: any = await this.plantRepository.find({
      where: { id: In(lstPlantId) },
      relations: { companys: { purchasingOrg: { purchasingGroups: true } } },
    })
    const dicPlant = {}

    for (const item of plantDetail) {
      const data: any = {}
      data.plantId = item.id
      data.plantCode = item.code

      data.companyCode = item.__companys__.code
      data.companyId = item.__companys__.id
      data.purchasingOrgId = item.__companys__.__purchasingOrg__.id
      data.purchasingOrgCode = item.__companys__.__purchasingOrg__.code
      const purGr = await this.purchasingGroupRepository.findOne({ where: { purchasingOrgId: data.purchasingOrgId, companyId: data.companyId } })
      data.purchasingGroupId = purGr.id
      data.purchasingGroupCode = purGr.code
      dicPlant[item.id] = data
    }
    console.log(lstPlantId)
    for (const item of lstRfqNotSync) {
      const bid = dicBid[item.targetId]
      const bidLstPr = bid.__bidPr__
      const bidPr = bidLstPr[0].__pr__
      const plantDetail = dicPlant[bidPr.plantId]

      const bodyPostRFQ: any = {}
      bodyPostRFQ.company = plantDetail.companyCode
      bodyPostRFQ.rfqtype = item.type
      bodyPostRFQ.rfqdate = new Date(item.createdAt)
      bodyPostRFQ.quodate = new Date(item.deadLine)
      bodyPostRFQ.purgrp = plantDetail.purchasingOrgCode
      bodyPostRFQ.plant = plantDetail.plantCode
      bodyPostRFQ.supplier = dicSupplier[item.supplierId].code
      bodyPostRFQ.rfqpms = bid.code
      bodyPostRFQ.detail = []
      /* lấy ra danh sách detail của rfq */
      const lstRfqDetail = lstRfqDetailNotSync.filter((c) => c.rfqId === item.id)
      for (const item of lstRfqDetail) {
        const data: any = {}
        data.item = dicMaterial[item.materialId].code
        data.quan = item.rfqQuantity
        data.deli = item.deliveryDate
        bodyPostRFQ.detail.push(data)
      }

      /* call api tích hợp sau đó cập nhật SAP code vàovào */
      if (process.env.SERVER_ID !== 'local') {
        let res: any = await apiHelper.postBudgetFromPMSToSAP(bodyPostRFQ)
        if (res) {
          res = JSON.parse(res)
          await this.rfqRepository.update(item.id, {
            sapCode: res.Header.rfqsap,
          })
        }
        /* update RFQ code */
      }
    }
  }
  /* hàm tự động cập nhật rfq */
  public async updateRfqResultToSap() {
    const lstRfqNotSync = await this.rfqRepository.find({
      where: [{ isSynchronizing: IsNull() }, { isSynchronizing: false, sapCode: Not(IsNull()) }],
    })
    /* map lại của plant */
    const lstRfqDetailNotSync = await this.rfqDetailsRepository.find({ where: { isSynchronizing: false } })
    /* lấy ra detail material*/
    const dicMaterial: any = {}
    const lstMaterial = await this.materialRepository.find({ where: { isDeleted: false } })

    for (const material of lstMaterial) {
      dicMaterial[material.id] = material
    }

    const dicRfq: any = {}
    /* từ plant chọn ra được */
    /* lấy thông tin của Bid */
    const lstBidId = lstRfqNotSync.map((c) => c.targetId)
    const lstSupplierId = lstRfqNotSync.map((c) => c.supplierId)

    /* lấy thông tin của Bid */
    const lstPlantId = []
    const lstBid: any = await this.repo.find({ where: { id: In(lstBidId) }, relations: { bidPr: { pr: true }, bidEx: true } })
    /* lấy thông tin của supplier */
    const lstSupplier: any = await this.supplierRepository.find({ where: { id: In(lstBidId) } })

    const dicBid = {}
    const dicSupplier = {}
    for (const item of lstSupplier) {
      dicSupplier[item.id] = item
    }

    for (const item of lstBid) {
      dicBid[item.id] = item
      const bid = item
      const bidLstPr = bid.__bidPr__
      const bidPr = bidLstPr[0].__pr__
      lstPlantId.push(bidPr.plantId)
    }

    const plantDetail: any = await this.plantRepository.find({
      where: { id: In(lstPlantId) },
      relations: { companys: { purchasingOrg: { purchasingGroups: true } } },
    })
    const dicPlant = {}

    for (const item of plantDetail) {
      const data: any = {}
      data.plantId = item.id
      data.plantCode = item.code

      data.companyCode = item.__companys__.code
      data.companyId = item.__companys__.id
      data.purchasingOrgId = item.__companys__.__purchasingOrg__.id
      data.purchasingOrgCode = item.__companys__.__purchasingOrg__.code
      const purGr = await this.purchasingGroupRepository.findOne({ where: { purchasingOrgId: data.purchasingOrgId, companyId: data.companyId } })
      data.purchasingGroupId = purGr.id
      data.purchasingGroupCode = purGr.code
      dicPlant[item.id] = data
    }
    console.log(lstPlantId)
    for (const item of lstRfqNotSync) {
      const bid = dicBid[item.targetId]
      const bidLstPr = bid.__bidPr__
      const bidPr = bidLstPr[0].__pr__
      const bidEx = bid.__bidEx__
      const exGr = bidEx[0]
      const plantDetail = dicPlant[bidPr.plantId]

      const bodyPostRFQ: any = {}
      bodyPostRFQ.company = plantDetail.companyCode
      bodyPostRFQ.rfqsap = item.sapCode
      bodyPostRFQ.rfqpms = bid.code
      bodyPostRFQ.currency = exGr.currency
      bodyPostRFQ.detail = []
      /* lấy ra danh sách detail của rfq */
      const lstRfqDetail = lstRfqDetailNotSync.filter((c) => c.rfqId === item.id)
      for (const item of lstRfqDetail) {
        const data: any = {}
        data.item = dicMaterial[item.materialId].code
        data.netprice = item.netPrice
        data.price_unit = item.netPrice
        data.Info_update = 'A'
        bodyPostRFQ.detail.push(data)
      }

      /* call api tích hợp sau đó cập nhật SAP code vàovào */
      if (process.env.SERVER_ID !== 'local') {
        let res: any = await apiHelper.postResultRFQ(bodyPostRFQ)
        if (res) {
          res = JSON.parse(res)
          await this.rfqRepository.update(item.id, {
            sapCode: res.Header.rfqsap,
          })
        }
        /* update RFQ code */
      }
    }
  }
}
