import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddColumBidEntity1753271365113 implements MigrationInterface {
  name = 'AddColumBidEntity1753271365113'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "bid" ADD "lstPaymentTermId" varchar(max)`)
    await queryRunner.query(`ALTER TABLE "bid" ADD "lstIncotermId" varchar(max)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "bid" DROP COLUMN "lstIncotermId"`)
    await queryRunner.query(`ALTER TABLE "bid" DROP COLUMN "lstPaymentTermId"`)
  }
}
