import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { ShipmentCostStreamRepository } from '../../repositories/shipmentCostStream.repository'
import { ShipmentCostStreamService } from './shipmentcoststream.service'
import { ShipmentCostStreamController } from './shipmentcoststream.controller'

@Module({
  imports: [TypeOrmExModule.forCustomRepository([ShipmentCostStreamRepository])],
  providers: [ShipmentCostStreamService],
  controllers: [ShipmentCostStreamController],
  exports: [ShipmentCostStreamService],
})
export class ShipmentCostStreamModule {}
