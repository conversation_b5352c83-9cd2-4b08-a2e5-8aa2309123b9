import { Body, Controller, Post, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { ShipmentPlanService } from './shipmentPlan.service'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import { JwtAuthGuard } from '../common/guards'
import { CurrentUser } from '../common/decorators'

/**Cá nhân/ tổ chức */
@ApiBearerAuth()
@ApiTags('Title')
@UseGuards(JwtAuthGuard)
@Controller('shipment_plan')
export class ShipmentPlanController {
  constructor(private readonly service: ShipmentPlanService) {}
  /* hàm load ra danh sách template vận chuyển */
  @Post('load_list')
  @ApiOperation({ summary: 'Load danh sách mẫu vận chuyển' })
  public async loadList(@CurrentUser() user: UserDto) {
    return await this.service.loadTemplateList(user)
  }

  @Post('load_template_details')
  @ApiOperation({ summary: 'Load danh sách mẫu vận chuyển' })
  public async loadloadTemplateDetail(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.loadloadTemplateDetail(user, data.id, data.searchingData)
  }

  /* hàm load pagination */
  @ApiOperation({ summary: 'DS đấu giá có phân trang' })
  @Post('pagination')
  public async pagination(@Body() data: PaginationDto, @CurrentUser() user: UserDto) {
    return await this.service.pagination(data, user)
  }

  @ApiOperation({ summary: 'Lấy danh sách option phương án vận chuyển' })
  @Post('find')
  public async find(@CurrentUser() user: UserDto) {
    return await this.service.find(user)
  }

  /* hàm lấy chi tiết */
  @Post('detail')
  @ApiOperation({ summary: 'Chi tiết' })
  public async detail(@Body() data: FilterOneDto, @CurrentUser() user: UserDto) {
    return await this.service.detail(data, user)
  }

  @ApiOperation({ summary: 'Gửi duyệt' })
  @Post('send_approve')
  public async sendApprove(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.sendApprove(user, data)
  }

  @ApiOperation({ summary: 'API Cập nhật duyệt' })
  @Post('update_approved_year')
  public async updateApproved(@CurrentUser() user: UserDto, @Body() data: { id: string; acceptedValueId: string }) {
    return await this.service.updateApproved(user, data)
  }

  @Post('save')
  @ApiOperation({ summary: 'Load danh sách mẫu vận chuyển' })
  public async save(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.save(user, data)
  }

  @Post('copy')
  @ApiOperation({ summary: 'Load danh sách mẫu vận chuyển' })
  public async copy(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.copy(user, data)
  }

  @ApiOperation({ summary: 'API từ chối duyệt' })
  @Post('update_reject_rule')
  public async updateRejectRule(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateRejectRule(user, data)
  }

  @ApiOperation({ summary: 'Gỡ từ chối duyệt khi từ chối ' })
  @Post('update_revert_status')
  public async updateRevertStatus(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateRevertStatus(user, data)
  }

  @ApiOperation({ summary: 'API gở duyệt' })
  @Post('update_remove_rule_2')
  public async updateRemoveRule2(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateRemoveRule2(user, data)
  }

  @ApiOperation({ summary: 'Xóa' })
  @Post('delete')
  public async deletePlan(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.deletePlan(user, data)
  }
}
