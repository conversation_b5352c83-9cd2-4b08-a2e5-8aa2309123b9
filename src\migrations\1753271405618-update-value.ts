import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateValue1753271405618 implements MigrationInterface {
  name = 'UpdateValue1753271405618'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" DROP COLUMN "price"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" ADD "price" decimal(10,2)`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" DROP COLUMN "priceQuarter1"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" ADD "priceQuarter1" decimal(10,2)`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" DROP COLUMN "priceQuarter2"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" ADD "priceQuarter2" decimal(10,2)`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" DROP COLUMN "priceQuarter3"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" ADD "priceQuarter3" decimal(10,2)`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" DROP COLUMN "priceQuarter4"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" ADD "priceQuarter4" decimal(10,2)`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" DROP COLUMN "formatPrice"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" ADD "formatPrice" decimal(10,2)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" DROP COLUMN "formatPrice"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" ADD "formatPrice" bigint`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" DROP COLUMN "priceQuarter4"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" ADD "priceQuarter4" bigint`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" DROP COLUMN "priceQuarter3"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" ADD "priceQuarter3" bigint`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" DROP COLUMN "priceQuarter2"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" ADD "priceQuarter2" bigint`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" DROP COLUMN "priceQuarter1"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" ADD "priceQuarter1" bigint`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" DROP COLUMN "price"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_log" ADD "price" bigint`)
  }
}
