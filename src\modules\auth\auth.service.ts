import { Injectable, BadRequestException, NotFoundException, UnauthorizedException } from '@nestjs/common'
import {
  CompanyRepository,
  EmployeeRepository,
  OrganizationalTreeRepository,
  PermissionRepository,
  SettingRoleRepository,
  SupplierRepository,
  UserRepository,
} from '../../repositories'
import { In, Like, MoreThanOrEqual } from 'typeorm'
import { EmailService } from '../email/email.service'
import { enumData, ERROR_VALIDATE, PWD_SALT_ROUNDS } from '../../constants'
import { UpdatePasswordDto, UpdateUsernameDto, ForgotPasswordDto, UserLoginDto } from './dto'
import { UserConfirmCodeEntity, UserEntity } from '../../entities'
import { JwtService } from '@nestjs/jwt'
import { Request as IRequest } from 'express'
import { apiHelper, coreHelper } from '../../helpers'
import { nanoid } from 'nanoid'
import { UserDto } from '../../dto'
import { hash } from 'bcrypt'
import { RequestFCMTokenDto } from './dto/requestFcmToken.dto'
import { PermissionGroupRepository } from '../../repositories/pemissionGroup.repository'
import { PermissionViewService } from '../perrmissionView/permissionView.service'

type PayloadType = {
  uid: string
}
@Injectable()
export class AuthService {
  constructor(
    private readonly supplierRepo: SupplierRepository,
    private readonly jwtService: JwtService,
    private readonly userRepo: UserRepository,
    private readonly permissionGroupRepository: PermissionGroupRepository,
    private readonly emailService: EmailService,
    private employeeRepo: EmployeeRepository,
    private readonly supplierNumberSettingRoleRepo: SettingRoleRepository,
    private organizationalTreeRepo: OrganizationalTreeRepository,
    private readonly permissionViewService: PermissionViewService,
  ) {}

  /** Đăng nhập cho nhân viên */
  public async login(data: UserLoginDto) {
    const userEntity: any = await this.userRepo.findOne({
      where: {
        username: data.username,
        type: In([enumData.UserType.Employee.code, enumData.UserType.Admin.code]),
        isDeleted: false,
      },
      relations: { employee: { employeeRole: true, permissionIndividual: true, department: true } },
    })
    if (!userEntity) throw new NotFoundException('Tài khoản không tồn tại!')
    if (userEntity.isDeleted) throw new UnauthorizedException('Tài khoản đã ngưng hoạt động!')

    if (userEntity.type !== enumData.UserType.Admin.code) {
      const checkEmp = await this.employeeRepo.findOne({ where: { id: userEntity?.employeeId, isDeleted: false } })
      if (!checkEmp) throw new UnauthorizedException('Tài khoản đã ngưng hoạt động!')
    }

    // check pw
    const isPasswordMatch = await userEntity.comparePassword(data.password)
    if (!isPasswordMatch) throw new UnauthorizedException('Mật khẩu không đúng!')

    const employee: any = await userEntity.employee
    const orgPosition = []
    if (employee && employee.orgPositionId) orgPosition.push(employee.orgPositionId)

    /**Tìm ds công ty của nhân viên */
    const allPermissions = await this.permissionGroupRepository.find({ where: { isDeleted: false, employeeId: Like(`%${employee.id}%`) } })
    const listCompany = allPermissions.reduce((acc, permission) => {
      if (permission.companyId) {
        acc.push(...(permission.companyId.split(',') || []))
      }
      if (permission.prTypeCode) {
      }
      return acc
    }, [])

    const prType = allPermissions.reduce((acc, permission) => {
      if (permission.prTypeCode) {
        acc.push(...(permission.prTypeCode || []))
      }
      return acc
    }, [])

    // const listCompany = allPermissions
    //   .filter((permission) => permission?.employeeId?.includes(employee.id))
    //   .map((permission) => permission?.companyId?.split(','))

    /* tìm vị trí của nhân viên đó trên cây */
    const positionInOrg = await this.organizationalTreeRepo.findOne({
      where: { targetId: employee.id, isDeleted: false },
    })

    // employeeOrgPosition
    //#endregion "Employee Role"

    //#region phân quyền view nhập liệu cho nhân viên(tạo mã sap)
    let listRoleSupplierNumber: any = []
    if (userEntity.type !== enumData.UserType.Admin.code) {
      let targetEmp = await this.organizationalTreeRepo.findOne({ where: { targetId: employee.id }, select: { id: true, lft: true, rgt: true } })
      if (!targetEmp) listRoleSupplierNumber = []

      const listSettingRole = await this.supplierNumberSettingRoleRepo.find({
        where: {
          isDeleted: false,
          orgGroupCompanyId: employee.orgCompanyId,
        },
      })
      if (targetEmp) {
        for (let item of listSettingRole) {
          if (item.orgPositionId) {
            let position = await this.organizationalTreeRepo.findOne({
              where: { id: item.orgPositionId },
              select: { id: true, lft: true, rgt: true },
            })
            if (position) {
              if (position.lft < targetEmp.lft && targetEmp.rgt < position.rgt) {
                //  nếu cho gán trực tiếp quyền cho user
                if (item.employeeId && item.employeeId === employee.id) {
                  listRoleSupplierNumber.push(item.role)
                }
                // mếu gán tới vị trí
                if (!item.employeeId) {
                  listRoleSupplierNumber.push(item.role)
                }
              }
            }
          }
        }
      }
    }

    //#endregion
    // listCompany =

    //#region phân quyền view nhân viên
    /* lấy ra xem nhân viên đó nằm trong những group nào */
    const lstEmployeeGroup = await this.permissionGroupRepository.getUserGroup(userEntity)
    /* lấy quyền của nhân viên */
    const lstPermission = await this.permissionViewService.getPermissionByGroupId(
      userEntity,
      lstEmployeeGroup.map((item) => item.id),
    )

    return {
      accessToken: this.jwtService.sign({ uid: userEntity.id }),
      name: employee?.name,
      isAdmin: userEntity.type == enumData.UserType.Admin.code,
      employeeId: userEntity?.employeeId,
      enumData: enumData,
      employeePosition: orgPosition,
      userId: userEntity?.id,
      companyCode: employee?.companyCode,
      listRoleSupplierNumber,
      lstPermission,
      listCompany,
      employeeOrgPosition: positionInOrg?.id,
      departmentId: employee.departmentId,
      departmentCode: employee?.__department__?.code,
      prType: prType,
      // lstEnumRole,
    }
  }

  private getListRole(lstRole, lstEnumRole) {
    for (let role of lstRole) {
      const dataRole = role?.[0] || role
      if (dataRole?.children?.length) {
        this.getListRole(dataRole.children, lstEnumRole)
      } else {
        lstEnumRole.push({
          ...dataRole,
        })
      }
    }
    return lstEnumRole
  }

  public async loginClient(user: any) {
    const userEntity: any = await this.userRepo.findOne({
      where: { username: user.username },
      select: { isDeleted: false },
      relations: { supplier: true },
    })
    if (!userEntity) throw new UnauthorizedException('Tài khoản không tồn tại!')
    if (userEntity.type != enumData.UserType.Supplier.code) throw new UnauthorizedException('Vui lòng đăng nhập với tài khoản nhà cung cấp!')

    const listSupplier = await this.supplierRepo.find({
      where: { code: userEntity.supplier.code },
    })

    const isLock = listSupplier.every((item) => item.status === enumData.SupplierStatus.Locked.code)
    if (isLock) throw new UnauthorizedException('Tài khoản đã bị khóa vui lòng liên hệ với quản trị viên!')

    // if (userEntity.supplier.requestUpdateStatus === enumData.RequestUpdateStatusSupplier.DISCONTINUED.code) {
    //   throw new UnauthorizedException('Tài khoản đã ngưng hoạt động!')
    // }

    if (userEntity.isDeleted) throw new UnauthorizedException('Tài khoản đã ngưng hoạt động!')

    const isPasswordMatch = await userEntity.comparePassword(user.password)
    if (!isPasswordMatch) throw new UnauthorizedException('Mật khẩu không đúng!')

    const isProduct = process.env.IS_PRODUCT == 'true'
    const accessToken = isProduct ? user.accessToken : this.jwtService.sign({ uid: userEntity.id })

    return {
      enumData: enumData,
      accessToken,
      supplierId: userEntity.supplierId,
      type: userEntity.supplier.type,
      supplierCode: userEntity.supplier.code,
      supplieName: userEntity.supplier.name,
    }
  }

  /** Lấy thông tin authorization Doanh nghiệp */
  public async authorizationClient(user: UserDto) {
    const userEntity = await this.userRepo.findOne({
      where: {
        id: user.id,
        type: enumData.UserType.Supplier.code,
        isDeleted: false,
      },
      relations: { supplier: true },
      select: {
        id: true,
        username: true,
        supplierId: true,
        supplier: { id: true, code: true, name: true },
      },
    })
    if (!userEntity) throw new NotFoundException('Tài khoản không tồn tại.')

    return {
      id: userEntity.id,
      enumData: enumData,
      username: userEntity.username,
      supplierId: userEntity.supplierId,
      supplierCode: userEntity.supplier.code,
      supplierName: userEntity.supplier.name,
    }
  }

  /** Đổi mật khẩu */
  public async updatePassword(info: UpdatePasswordDto, user: UserDto, req: IRequest) {
    if (info.newPassword === info.currentPassword) throw new BadRequestException('Trùng mật khẩu cũ.')

    const userEntity = await this.userRepo.findOne({ where: { id: user.id }, select: { id: true } })
    if (!userEntity) throw new NotFoundException('Tài khoản không tồn tại.')
    const hashedPassword = await hash(info.newPassword, PWD_SALT_ROUNDS)
    await this.userRepo.update(user.id, { password: hashedPassword, updatedBy: user.id })
    return { message: 'Đổi mật khẩu thành công.' }
  }

  /** Đổi tên tài khoản supplier */
  public async updateUsername(info: UpdateUsernameDto, user: UserDto, req: IRequest) {
    const newUsername = info.newUsername.trim()
    if (!user.supplierId) throw new NotFoundException('Chức năng chỉ áp dụng cho tài khoản nhà cung cấp!')
    if (!newUsername || newUsername.length < 3) throw new NotFoundException('Tài khoản mới không hợp lệ')

    const userEntity = await this.userRepo.findOne({
      where: { id: user.id },
      select: { id: true, username: true, password: true },
    })
    if (!userEntity) throw new NotFoundException('Tài khoản không tồn tại.')

    if (newUsername === userEntity.username) throw new BadRequestException('Trùng tên đăng nhập cũ.')

    const existUserEntity = await this.userRepo.findOne({ where: { username: newUsername, companyId: user.companyId }, select: { id: true } })
    if (existUserEntity) throw new Error(`Tài khoản [${newUsername}] đã tồn tại.`)

    const isCurrentPasswordMatch = await userEntity.comparePassword(info.currentPassword)
    if (!isCurrentPasswordMatch) throw new BadRequestException('Sai mật khẩu cũ.')
    // Update user bên auth
    await this.userRepo.update(user.id, { username: newUsername, updatedBy: user.id })
    return { message: 'Đổi tên đăng nhập thành công.' }
  }

  /** Gửi mã xác nhận khi quên mật khẩu */
  public async sendConfirmCode(req: Request, data: { email: string }) {
    const companyId = await apiHelper.getDomain(req)
    const userConfirmCodeRepo = this.userRepo.manager.getRepository(UserConfirmCodeEntity)
    let email = data.email ? data.email.trim().toLowerCase() : ''
    if (!email) throw new Error(ERROR_VALIDATE)

    const user = await this.supplierRepo.findOne({ where: { email, companyId, isDeleted: false }, select: { id: true, email: true, userId: true } })
    if (!user || user.email.trim().toLowerCase() !== email) throw new Error('User không còn tồn tại!')

    const dtNow = new Date()
    const entity = await userConfirmCodeRepo.findOne({
      where: {
        userId: user.userId,
        exDate: MoreThanOrEqual(dtNow),
        companyId,
        isDeleted: false,
      },
      select: { id: true },
    })
    if (entity) throw new Error('Mã xác nhận đã được gửi, vui lòng kiểm tra lại email!')

    const expireDate = new Date()
    expireDate.setHours(expireDate.getHours() + 8)
    const confirmCode = nanoid(8)

    const createdData = userConfirmCodeRepo.create({
      userId: user.userId,
      exDate: expireDate,
      code: confirmCode,
      companyId,
      createdBy: user.userId,
    })
    await userConfirmCodeRepo.save(createdData)

    this.emailService.sendConfirmCode({ companyId, email, confirmCode })

    return { message: 'Mã đã gửi, vui lòng kiểm tra email đăng ký tài khoản.' }
  }

  /** Nhập mã xác nhận khi supplier quên mật khẩu */
  public async forgotPassword(req: Request, { email, confirmCode, newPassword }: ForgotPasswordDto) {
    const emailStr = email.trim() ? email.trim().toLowerCase() : ''
    if (!emailStr) throw new Error(ERROR_VALIDATE)

    const supplier: any = await this.supplierRepo.findOne({
      where: { email: emailStr, isDeleted: false },
      relations: { user: true },
      select: { id: true, email: true, userId: true, user: { id: true, username: true } },
    })
    if (!supplier || supplier.email.trim().toLowerCase() !== emailStr) throw new Error('User không còn tồn tại')
    const user = supplier.__user__

    const dtNow = new Date()
    const confirm = await this.userRepo.manager.getRepository(UserConfirmCodeEntity).findOne({
      where: {
        userId: supplier.userId,
        exDate: MoreThanOrEqual(dtNow),
        code: confirmCode,
        isDeleted: false,
      },
      select: { id: true, userId: true },
    })
    if (!confirm) throw new NotFoundException('Mã xác nhận không hợp lệ!')

    const userEntity = await this.userRepo.findOne({ where: { id: confirm.userId }, select: { id: true } })
    if (!userEntity) throw new NotFoundException('Tài khoản không tồn tại.')

    const hashedPassword = await hash(newPassword, PWD_SALT_ROUNDS)
    await this.userRepo.update(confirm.userId, { password: hashedPassword, updatedBy: user.userId })

    return { message: 'Thay đổi mật khẩu thành công.' }
  }

  public async requestFcmToken(user: RequestFCMTokenDto) {
    const userEntity = await this.userRepo.findOne({
      where: {
        id: user.userId,
        isDeleted: false,
      },
    })
    if (!userEntity) throw new NotFoundException('Tài khoản không tồn tại.')
    let result: any = await this.userRepo.update(
      {
        id: userEntity.id,
      },
      {
        deviceToken: user.deviceToken,
        fcmToken: user.fcmToken,
      },
    )

    return result
  }
  /* hàm chuyển đổi company đang đăng nhập của nhân viên */
  public async convertCompanyLogin(user: UserDto, companyId: string) {
    const userEntity = await this.userRepo.findOne({
      where: { id: user.id, isDeleted: false },
      relations: { employee: true },
    })
    if (!userEntity) throw new NotFoundException('Tài khoản không tồn tại.')
    // if (userEntity.type !== enumData.UserType.Employee.code) throw new UnauthorizedException('Chức năng chỉ áp dụng cho tài khoản nhân viên!')
    if (userEntity.isDeleted) throw new UnauthorizedException('Tài khoản đã ngưng hoạt động!')
    if (!userEntity.employee) throw new NotFoundException('Nhân viên không tồn tại!')
    if (userEntity.employee.isDeleted) throw new UnauthorizedException('Nhân viên đã ngưng hoạt động!')
    /* lưu lại công ty đang đăng nhập */
    return await this.userRepo.update({ id: userEntity.id }, { currentCompanyId: companyId })
  }

  async refresh(refreshStr: string) {
    const decodedJwt = this.jwtService.decode(refreshStr) as PayloadType
    const userEntity = await this.userRepo.findOne({
      where: {
        id: decodedJwt.uid,
        type: In([enumData.UserType.Employee.code, enumData.UserType.Admin.code]),
        isDeleted: false,
      },
      relations: { employee: { employeeRole: true } },
    })
    if (!userEntity) throw new NotFoundException('Tài khoản không tồn tại!')
    if (userEntity.isDeleted) throw new UnauthorizedException('Tài khoản đã ngưng hoạt động!')

    if (!userEntity) throw new NotFoundException('Tài khoản không tồn tại!')
    if (userEntity.isDeleted) throw new UnauthorizedException('Tài khoản đã ngưng hoạt động!')

    if (userEntity.type !== enumData.UserType.Admin.code) {
      const checkEmp = await this.employeeRepo.findOne({ where: { id: userEntity.employeeId, isDeleted: false } })
      if (!checkEmp) throw new UnauthorizedException('Tài khoản đã ngưng hoạt động!')
    }

    const employee: any = userEntity.employee
    const position = []
    if (employee && employee.position) position.push(employee.position)

    //#region "Employee Role"
    const dicRole: any = {}
    let dataReturn: any = {}
    const lstRoleSetting = employee?.__employeeRole__ || []
    for (const role of lstRoleSetting) {
      dicRole[role.roleCode] = role
    }

    //sinh ra danh sách quyền hiện tại
    const lstRole = coreHelper.convertObjToArray(enumData.RoleType)
    for (const item of lstRole) {
      dataReturn[item.code] = {}
      for (const dataCol of item.dbCol) {
        if (dicRole[item.code]) {
          const data = dicRole[item.code]
          const dataArr = data[dataCol]
          if (dataArr) dataReturn[item.code][dataCol] = dataArr.split(',')
        }
      }
    }

    return {
      accessToken: this.jwtService.sign({ uid: userEntity.id }),
      name: employee?.name,
      lstRole: dataReturn,
      isAdmin: userEntity.type == enumData.UserType.Admin.code,
      employeeId: userEntity.employeeId,
      enumData: enumData,
      employeePosition: position,
      companyCode: employee?.companyCode,
      positionId: employee?.positionId || employee?.orgPositionId,
    }
  }
}
