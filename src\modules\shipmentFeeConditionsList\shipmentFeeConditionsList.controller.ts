import { Body, Controller, Delete, Post, UseGuards } from '@nestjs/common'
import { ShipmentFeeConditionsListService } from './shipmentFeeConditionsList.service'
import { ShipmentFeeConditionsListCreateDto, ShipmentFeeConditionsListUpdateDto } from '../../dto/shipmentFeeConditionsList.dto'
import { ApiBearerAuth, ApiOperation } from '@nestjs/swagger'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import { CurrentUser } from '../common/decorators'
import { JwtAuthGuard } from '../common/guards'
import { GetShipmentFeeConditionsListByPlanDto } from './dto/getByShipmentPlan.dto'

// @ApiBearerAuth()
// @UseGuards(JwtAuthGuard)
@Controller('shipment_fee_conditions_list')
export class ShipmentFeeConditionsListController {
  constructor(private readonly service: ShipmentFeeConditionsListService) {}

  @ApiOperation({ summary: 'Hàm tạo' })
  @Post('create_data')
  async create(@CurrentUser() user: UserDto, @Body() data: ShipmentFeeConditionsListCreateDto) {
    return this.service.create(user, data)
  }

  @Post('pagination')
  async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return this.service.pagination(user, data)
  }

  @Post('find')
  async findOne(@Body() data: FilterOneDto) {
    return this.service.findOne(data)
  }

  @Post('update')
  async update(@CurrentUser() user: UserDto, @Body() data: ShipmentFeeConditionsListUpdateDto) {
    return this.service.update(user, data)
  }

  @Delete('update_delete')
  async remove(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return this.service.remove(user, data)
  }

  @ApiOperation({ summary: 'Lấy danh sách ShipmentFeeConditionsListEntity từ shipmentPlanId' })
  @Post('get_options')
  async getOptions(@CurrentUser() user: UserDto, @Body() data: GetShipmentFeeConditionsListByPlanDto) {
    return this.service.getOptions(user, data)
  }
}
