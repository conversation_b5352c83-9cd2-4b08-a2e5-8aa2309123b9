import { MigrationInterface, QueryRunner } from 'typeorm'

export class ShipmentPlanNumber1753257811604 implements MigrationInterface {
  name = 'ShipmentPlanNumber1753257811604'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" ADD "shipmentCostStreamConfigTable" nvarchar(max)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" DROP COLUMN "shipmentCostStreamConfigTable"`)
  }
}
