import { MigrationInterface, QueryRunner } from "typeorm";

export class AddColumnContractItem1753178191515 implements MigrationInterface {
    name = 'AddColumnContractItem1753178191515'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "contract_item" ADD "costCenterCode" varchar(50)`);
        await queryRunner.query(`ALTER TABLE "contract_item" ADD "assetDesc" nvarchar(max)`);
        await queryRunner.query(`ALTER TABLE "contract_item" ADD "ioName" nvarchar(max)`);
        await queryRunner.query(`ALTER TABLE "contract_item" ADD "iotype" nvarchar(max)`);
        await queryRunner.query(`ALTER TABLE "contract" ADD "plantId" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "contract" ADD CONSTRAINT "FK_6218e434add48d8480c1674f9bc" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "contract" DROP CONSTRAINT "FK_6218e434add48d8480c1674f9bc"`);
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "plantId"`);
        await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "iotype"`);
        await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "ioName"`);
        await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "assetDesc"`);
        await queryRunner.query(`ALTER TABLE "contract_item" DROP COLUMN "costCenterCode"`);
    }

}
