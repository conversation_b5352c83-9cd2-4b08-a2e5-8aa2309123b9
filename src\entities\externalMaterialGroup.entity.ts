import { BaseEntity } from './base.entity'
import { En<PERSON><PERSON>, Column, OneToMany, JoinColumn, ManyToOne } from 'typeorm'
import { MaterialEntity } from './material.entity'
import { MaterialGroupEntity } from './materialGroup.entity'
import { enumData } from '../constants'
import { ServiceCapacityEntity } from './serviceCapacity.entity'
import { ServiceSceneEntity } from './serviceScene.entity'
import { ServicePurchaseHistoryEntity } from './servicePurchaseHistory.entity'
import { BidEntity } from './bid.entity'
import { PrEntity } from './pr.entity'
import { AuctionEntity } from './auction.entity'
import { AuctionSupplierPriceEntity } from './auctionSupPrice.entity'
import { OfferEntity } from './offer.entity'
import { ContractEntity } from './contract.entity'
import { POEntity } from './po.entity'
import { OfferServiceEntity } from './offerService.entity'
import { RoundUpContEntity } from './roundUpCont.entity'
import { MaterialTypeEntity } from './materialType.entity'
import { BusinessPlanEntity } from './businessPlan.entity'
import { PrItemEntity } from './prItem.entity'

import { BidExMatGroupEntity } from './bidExgroup.entity'
import { UserExternalMaterialGroupEntity } from './userExternalMaterialGroup.entity'
import { RequestQuoteDetailEntity } from './requestQuoteDetail.entity'
import { RequestQuoteSupplierEntity } from './requestQuoteSupplier.entity'
import { BidPriceEntity, RfqDetailsEntity } from '.'

/** Nhóm vật liệu bên ngoài */
@Entity('external_material_group')
export class ExternalMaterialGroupEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 500,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @OneToMany(() => MaterialEntity, (p) => p.externalMaterialGroup)
  materials: Promise<MaterialEntity[]>

  @OneToMany(() => BidExMatGroupEntity, (p) => p.externalMaterialGroup)
  bidEx: Promise<BidExMatGroupEntity[]>

  @OneToMany(() => UserExternalMaterialGroupEntity, (p) => p.externalMaterialGroup)
  userExGr: Promise<UserExternalMaterialGroupEntity[]>

  @OneToMany(() => PrEntity, (p) => p.externalMaterialGroup)
  prs: Promise<PrEntity[]>

  @OneToMany(() => MaterialGroupEntity, (p) => p.externalMaterialGroup)
  materialGroups: Promise<MaterialGroupEntity[]>

  /** Công thức tính cột đơn giá */
  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  fomular: string

  /** Cách tính điểm giá */
  @Column({ type: 'varchar', length: 50, nullable: true, default: enumData.PriceScoreCalculateWay.SumScore.code })
  wayCalScorePrice: string

  @Column({ type: 'varchar', length: 100, nullable: true, default: enumData.SupplierExpertiseCapacityStatus.ChuaThamDinh.code })
  statusCapacity: string

  // 1 dịch vụ sẽ có nhiều cấu hình năng lực
  @OneToMany(() => ServiceCapacityEntity, (p) => p.exMatGroup)
  capacities: Promise<ServiceCapacityEntity[]>

  @OneToMany(() => AuctionSupplierPriceEntity, (p) => p.exMatGroup)
  auctionSupplierPrice: Promise<AuctionSupplierPriceEntity[]>

  // 1 dịch vụ sẽ có bảng đánh giá hiện trường
  @OneToMany(() => ServiceSceneEntity, (p) => p.exMatGroup)
  scenes: Promise<ServiceSceneEntity[]>

  /** Các gói thầu tổng và gói thầu chi tiết của Pr */
  @OneToMany(() => BidEntity, (p) => p.exMatGroup)
  bids: Promise<BidEntity[]>

  // 1 dịch vụ sẽ có bảng đánh giá lịch sử mua hàng
  @OneToMany(() => ServicePurchaseHistoryEntity, (p) => p.exMatGroup)
  purchaseHistorys: Promise<ServicePurchaseHistoryEntity[]>

  @OneToMany(() => AuctionEntity, (p) => p.exMatGroup)
  auctions: Promise<AuctionEntity[]>

  @OneToMany(() => OfferEntity, (p) => p.exMatGroup)
  offer: Promise<OfferEntity[]>

  @OneToMany(() => OfferServiceEntity, (p) => p.exMatGroup)
  offerService: Promise<OfferServiceEntity[]>

  @OneToMany(() => ContractEntity, (p) => p.externalMaterialGroup)
  contracts: Promise<ContractEntity[]>

  @OneToMany(() => POEntity, (p) => p.externalMaterialGroup)
  pos: Promise<POEntity[]>

  /** đã đồng bộ */
  @Column({
    nullable: true,
    default: false,
  })
  isSync: boolean

  @OneToMany(() => RoundUpContEntity, (p) => p.externalMaterialGroup)
  roundUpCont: Promise<RoundUpContEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialTypeId: string
  @ManyToOne(() => MaterialTypeEntity, (p) => p.externalMaterialGroups)
  @JoinColumn({ name: 'materialTypeId', referencedColumnName: 'id' })
  materialType: Promise<MaterialTypeEntity>

  @OneToMany(() => BusinessPlanEntity, (p) => p.externalMaterialGroup)
  businessPlan: Promise<BusinessPlanEntity[]>

  @OneToMany(() => PrItemEntity, (p) => p.externalMaterialGroup)
  prItem: Promise<PrItemEntity[]>

  @OneToMany(() => RequestQuoteDetailEntity, (p) => p.externalMaterialGroup)
  requestQuoteDetails: Promise<RequestQuoteDetailEntity[]>

  @OneToMany(() => RequestQuoteSupplierEntity, (p) => p.externalMaterialGroup)
  requestQuoteSuppliers: Promise<RequestQuoteSupplierEntity[]>

  @OneToMany(() => RfqDetailsEntity, (p) => p.externalMaterialGroup)
  rfqDetails: Promise<RfqDetailsEntity[]>

  @OneToMany(() => BidPriceEntity, (p) => p.externalMaterialGroup)
  bidPrices: Promise<BidPriceEntity[]>
}
