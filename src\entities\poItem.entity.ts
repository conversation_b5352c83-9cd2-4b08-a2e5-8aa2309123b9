import { Column, <PERSON>tity, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ContractEntity } from './contract.entity'
import { POEntity } from './po.entity'
import { PrEntity } from './pr.entity'
@Entity('po_item')
export class PoItemEntity extends BaseEntity {
  @Column({ type: 'varchar', nullable: true })
  materialCode: string

  @Column({ type: 'varchar', nullable: true })
  materialName: string

  @Column({ type: 'varchar', nullable: true })
  unitName: string

  @Column({ nullable: true, type: 'int' })
  quantity: number

  @Column({
    nullable: true,
    type: 'int',
  })
  quantityRemaining: number

  @Column({
    nullable: true,
    type: 'int',
  })
  quantityUptoPO: number

  @Column({ nullable: true, type: 'int' })
  price: number

  @Column({ nullable: true, type: 'int' })
  totalPrice: number

  /** --- PO INFO --- */
  @Column({ type: 'varchar', nullable: true })
  poId: string
  @ManyToOne(() => POEntity, (p) => p.poItems)
  @JoinColumn({ name: 'poId' })
  po: Promise<POEntity>

  /** --- PR ONLY --- */
  @Column({ type: 'varchar', nullable: true })
  prId: string
  @ManyToOne(() => PrEntity, (p) => p.prItems)
  @JoinColumn({ name: 'prId' })
  pr: Promise<PrEntity>

  /** --- CONTRACT ONLY --- */
  @Column({ type: 'varchar', nullable: true })
  contractId: string
  @ManyToOne(() => ContractEntity, (p) => p.contractItems)
  @JoinColumn({ name: 'contractId' })
  contract: Promise<ContractEntity>
}
