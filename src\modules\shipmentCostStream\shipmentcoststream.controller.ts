/*
https://docs.nestjs.com/controllers#controllers
*/

import { Body, Controller, Delete, Get, Param, Post, Put, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { JwtAuthGuard } from '../common/guards'
import { CurrentUser } from '../common/decorators'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import { ShipmentCostStreamService } from './shipmentcoststream.service'

@ApiBearerAuth()
@ApiTags('Shipment-Cost-Stream')
@UseGuards(JwtAuthGuard)
@Controller('shipment_cost_stream')
export class ShipmentCostStreamController {
  constructor(private readonly service: ShipmentCostStreamService) {}

  @ApiOperation({ summary: 'Hàm tìm' })
  @Post('find')
  async create(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.find(user, data)
  }
  @ApiOperation({ summary: 'Hàm phân trang' })
  @Post('pagination')
  async pagination(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.pagination(user, data)
  }

  @ApiOperation({ summary: 'Hàm tạo' })
  @Post('save')
  async saveRow(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.save(user, data)
  }

  @ApiOperation({ summary: 'Hàm update' })
  @Post('update')
  async update(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.update(user, data)
  }

  @ApiOperation({ summary: 'Hàm update' })
  @Post('delete')
  async delete(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.delete(user, data)
  }
}
