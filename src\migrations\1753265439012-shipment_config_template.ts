import { MigrationInterface, QueryRunner } from 'typeorm'

export class ShipmentConfigTemplate1753265439012 implements MigrationInterface {
  name = 'ShipmentConfigTemplate1753265439012'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_config_template" ADD "configCostShipment" nvarchar(max)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_config_template" DROP COLUMN "configCostShipment"`)
  }
}
