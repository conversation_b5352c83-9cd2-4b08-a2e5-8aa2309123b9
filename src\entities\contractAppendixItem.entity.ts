import { Column, <PERSON>ti<PERSON>, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm'
import { BaseEntity } from './base.entity'
import { ContractAppendixEntity } from './contractAppendix.entity'
import { MaterialEntity } from './material.entity'
import { UomEntity } from './uom.entity'
import { MaterialGroupEntity } from './materialGroup.entity'
import { PlantEntity } from './plant.entity'
import { MaterialStorageLocationEntity } from './materialStorageLocation.entity'
import { FactorySupplierEntity } from './factorySupplier.entity'

/** item của phụ lục hợp đồng */
@Entity({ name: 'contract_appendix_item' })
export class ContractAppendixItemEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  type: string

  /** Số lượng */
  @Column({
    nullable: true,
    default: 0,
  })
  quantity: number

  /**Đơn giá */
  @Column({
    nullable: true,
  })
  price: number

  /**Total price */
  @Column({
    type: 'float',
    nullable: true,
  })
  totalPrice: number

  /** contract appendix*/
  @Column({
    type: 'varchar',
    nullable: false,
  })
  contractAppendixId: string
  @ManyToOne(() => ContractAppendixEntity, (p) => p.contractAppendixItems)
  @JoinColumn({ name: 'contractAppendixId', referencedColumnName: 'id' })
  contractAppendix: Promise<ContractAppendixEntity>

  /** material */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialId: string
  @ManyToOne(() => MaterialEntity, (p) => p.contractAppendixItems)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  /** Hàng hóa */
  @Column({
    type: 'nvarchar',
    length: 350,
    nullable: true,
  })
  shortText: string

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  unitId: string
  @ManyToOne(() => UomEntity, (p) => p.contractAppendixItems)
  @JoinColumn({ name: 'unitId', referencedColumnName: 'id' })
  unit: Promise<UomEntity>

  /**Unit of Measure/ĐVT  */
  @Column({
    nullable: true,
  })
  unitName: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  itemNo: string

  /** Lot/Lô hàng */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  lotNumber: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  acccate: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  category: string

  /** material group */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialGroupId: string
  @ManyToOne(() => MaterialGroupEntity, (p) => p.contractAppendixItems)
  @JoinColumn({ name: 'materialGroupId', referencedColumnName: 'id' })
  materialGroup: Promise<MaterialGroupEntity>

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  assetCode: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  orderCode: string

  /** Nhà máy */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string
  @ManyToOne(() => PlantEntity, (p) => p.contractAppendixItems)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  storageLocation: string

  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
  })
  ounCode: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  ounId: string
  @ManyToOne(() => UomEntity, (p) => p.contractAppendixItemUons)
  @JoinColumn({ name: 'ounId', referencedColumnName: 'id' })
  oun: Promise<UomEntity>

  /**Hệ số quy đổi */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  quantityAlternative: number

  /**Thành tiền VND*/
  @Column({
    type: 'float',
    nullable: true,
  })
  totalPriceVND: number

  /**Mã thuế */
  @Column({
    type: 'nvarchar',
    length: 100,
    nullable: true,
  })
  taxCode: string

  /**Thuế */

  @Column({
    nullable: true,
    default: 0,
  })
  taxRate: number

  /**Thành tiền sau thuế*/
  @Column({
    type: 'float',
    nullable: true,
  })
  totalPriceAfterTax: number

  /**Origin/Xuất xứ */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  origin: string

  /**Manufacturer/Nhà máy */
  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  manufacturer: string

  @Column({
    nullable: true,
    type: 'datetime',
  })
  deliveryDate: Date

  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  underDeliveryTolerance: string

  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  overDeliveryTolerance: string

  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  stockType: string

  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  valuationType: string

  /**Specification/Tiêu chuẩn*/
  @Column({
    type: 'nvarchar',
    length: 450,
    nullable: true,
  })
  technicalSpec: string

  /**Ngày giao hàng trễ nhất */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  latestDeliveryDate: Date

  /**Tolerance/Dung sai*/
  @Column({
    type: 'nvarchar',
    length: 100,
    nullable: true,
  })
  toleranceRange: string

  /** Vị trí kho hàng */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialStorageLocationId: string
  @ManyToOne(() => MaterialStorageLocationEntity, (p) => p.contractAppendixItemUons)
  @JoinColumn({ name: 'materialStorageLocationId', referencedColumnName: 'id' })
  materialStorageLocation: Promise<MaterialStorageLocationEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  factorySupplierId: string
  @ManyToOne(() => FactorySupplierEntity, (p) => p.contractAppendixItems)
  @JoinColumn({ name: 'factorySupplierId', referencedColumnName: 'id' })
  factorySupplier: Promise<FactorySupplierEntity>

  /** Cost center */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  costCenterCode: string

  /** asset Desc */
  @Column({
    length: 'max',
    nullable: true,
  })
  assetDesc: string

  /** io Name */
  @Column({
    length: 'max',
    nullable: true,
  })
  ioName: string

  /** iotype */
  @Column({
    length: 'max',
    nullable: true,
  })
  iotype: string
}
