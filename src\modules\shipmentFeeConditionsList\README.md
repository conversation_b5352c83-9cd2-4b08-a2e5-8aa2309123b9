# ShipmentFeeConditionsList API

## Mô tả
API này cung cấp chức năng lấy danh sách `ShipmentFeeConditionsListEntity` từ `shipmentPlanId` thông qua các bước sau:

1. Từ `shipmentPlanId` lấy ra tất cả `ShipmentPlanNumberEntity`
2. Thu thập `shipmentFeeConditionTypeCompactCode: string[]` từ các `ShipmentPlanNumber`
3. Từ các code này tìm ra `shipmentFeeConditionTypeCompactId: string[]` tương ứng
4. Sử dụng các id này để lấy ra các record `ShipmentFeeConditionsListEntity` thông qua quan hệ nhiều-nhiều trong bảng `ShipmentFeeConditionsToListEntity`

## Endpoint

### POST /shipment_fee_conditions_list/get_by_shipment_plan_id

**Request Body:**
```json
{
  "shipmentPlanId": "uuid-of-shipment-plan"
}
```

**Response:**
```json
[
  {
    "id": "uuid-1",
    "code": "FCL001",
    "name": "Đi<PERSON>u kiện phí 1",
    "description": "Mô tả điều kiện phí 1",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z",
    "isDeleted": false
  },
  {
    "id": "uuid-2", 
    "code": "FCL002",
    "name": "Điều kiện phí 2",
    "description": "Mô tả điều kiện phí 2",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z",
    "isDeleted": false
  }
]
```

## Cách hoạt động

### Bước 1: Lấy ShipmentPlanNumber
```typescript
const shipmentPlanNumbers = await this.shipmentPlanNumberRepository.find({
  where: { 
    shipmentPlanId: data.shipmentPlanId, 
    isDeleted: false 
  },
  select: {
    id: true,
    shipmentFeeConditionTypeCompactCode: true,
    shipmentFeeConditionTypeCompactId: true
  }
})
```

### Bước 2: Thu thập compact codes
```typescript
const allCompactCodes: string[] = []
shipmentPlanNumbers.forEach(planNumber => {
  if (planNumber.shipmentFeeConditionTypeCompactCode && Array.isArray(planNumber.shipmentFeeConditionTypeCompactCode)) {
    allCompactCodes.push(...planNumber.shipmentFeeConditionTypeCompactCode)
  }
})
const uniqueCompactCodes = [...new Set(allCompactCodes)]
```

### Bước 3: Lấy shipmentFeeCondition IDs
```typescript
const shipmentFeeConditions = await this.shipmentFeeConditionsRepository.find({
  where: {
    code: In(uniqueCompactCodes),
    isDeleted: false
  },
  select: {
    id: true,
    code: true
  }
})
```

### Bước 4: Lấy ShipmentFeeConditionsListEntity
```typescript
const shipmentFeeConditionsToList = await this.shipmentFeeConditionsToListRepository.find({
  where: {
    shipmentFeeConditionsId: In(shipmentFeeConditionIds),
    isDeleted: false
  },
  relations: {
    shipmentFeeConditionsList: true
  }
})
```

## Lưu ý
- API sẽ trả về mảng rỗng nếu không tìm thấy dữ liệu
- Các bản ghi đã bị xóa (isDeleted = true) sẽ được loại bỏ
- Kết quả sẽ loại bỏ các bản ghi trùng lặp
- Cần xác thực JWT token để sử dụng API này
