import { MigrationInterface, QueryRunner } from 'typeorm'

export class FixEntityPO1753164707219 implements MigrationInterface {
  name = 'FixEntityPO1753164707219'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "po_pr" DROP CONSTRAINT "FK_67d4c2ecf748c2b1e1a0d005452"`)
    await queryRunner.query(`ALTER TABLE "po_pr" DROP CONSTRAINT "FK_529f0b9999d77fe7a93eba692f1"`)
    await queryRunner.query(`ALTER TABLE "po_contract" DROP CONSTRAINT "FK_6a4db1ae62b6adab3addeecb9c9"`)
    await queryRunner.query(`ALTER TABLE "po_contract" DROP CONSTRAINT "FK_66e273ae29d05ea3faca09ec8d2"`)
    await queryRunner.query(
      `CREATE TABLE "po_item" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_b1cc7fd2f38a35d8b172cb2310a" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_dce6554eb75b2d115b2c0eb4a65" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "materialCode" varchar(255), "materialName" varchar(255), "unit" varchar(255), "quantity" int CONSTRAINT "DF_f25fa5faf4f1ad3f145ed0b6991" DEFAULT 0, "quantityRemaining" int, "quantityUptoPO" int CONSTRAINT "DF_e3310925a02037b7327f81c397b" DEFAULT 0, "price" varchar(255), "totalPrice" varchar(255), "poId" uniqueidentifier, "prId" uniqueidentifier, "contractId" uniqueidentifier, CONSTRAINT "PK_b1cc7fd2f38a35d8b172cb2310a" PRIMARY KEY ("id"))`,
    )

    await queryRunner.query(`ALTER TABLE "po" DROP COLUMN "usedBudget"`)
    await queryRunner.query(`ALTER TABLE "po" DROP COLUMN "quantityPo"`)

    await queryRunner.query(`ALTER TABLE "po_contract" DROP COLUMN "quantityPo"`)
    await queryRunner.query(`ALTER TABLE "po_contract" DROP COLUMN "totalPO"`)
    await queryRunner.query(`ALTER TABLE "po_contract" DROP COLUMN "usedBudget"`)
    await queryRunner.query(`ALTER TABLE "po" DROP COLUMN "prId"`)
    await queryRunner.query(`ALTER TABLE "po" ADD "prId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "po" DROP COLUMN "totalPO"`)
    await queryRunner.query(`ALTER TABLE "po" ADD "totalPO" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "po_pr" DROP COLUMN "poId"`)
    await queryRunner.query(`ALTER TABLE "po_pr" ADD "poId" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "po_pr" DROP COLUMN "prIds"`)
    await queryRunner.query(`ALTER TABLE "po_pr" ADD "prIds" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "po_contract" DROP COLUMN "poId"`)
    await queryRunner.query(`ALTER TABLE "po_contract" ADD "poId" nvarchar(255)`)
    await queryRunner.query(`ALTER TABLE "po_contract" DROP COLUMN "contractId"`)
    await queryRunner.query(`ALTER TABLE "po_contract" ADD "contractId" nvarchar(255)`)
    await queryRunner.query(
      `ALTER TABLE "po_item" ADD CONSTRAINT "FK_d151c30653f77435ea9d9804da4" FOREIGN KEY ("poId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "po_item" ADD CONSTRAINT "FK_8614870585988c69e03f2b34059" FOREIGN KEY ("prId") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "po_item" ADD CONSTRAINT "FK_867ab8a73ab3a557235538c30be" FOREIGN KEY ("contractId") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "po" ADD CONSTRAINT "FK_9ae35e3316a98f3a9d740df26c0" FOREIGN KEY ("prId") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "po" DROP CONSTRAINT "FK_9ae35e3316a98f3a9d740df26c0"`)
    await queryRunner.query(`ALTER TABLE "po_item" DROP CONSTRAINT "FK_867ab8a73ab3a557235538c30be"`)
    await queryRunner.query(`ALTER TABLE "po_item" DROP CONSTRAINT "FK_8614870585988c69e03f2b34059"`)
    await queryRunner.query(`ALTER TABLE "po_item" DROP CONSTRAINT "FK_d151c30653f77435ea9d9804da4"`)
    await queryRunner.query(`ALTER TABLE "po_contract" DROP COLUMN "contractId"`)
    await queryRunner.query(`ALTER TABLE "po_contract" ADD "contractId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "po_contract" DROP COLUMN "poId"`)
    await queryRunner.query(`ALTER TABLE "po_contract" ADD "poId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "po_pr" DROP COLUMN "prIds"`)
    await queryRunner.query(`ALTER TABLE "po_pr" ADD "prIds" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "po_pr" DROP COLUMN "poId"`)
    await queryRunner.query(`ALTER TABLE "po_pr" ADD "poId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "po" DROP COLUMN "totalPO"`)
    await queryRunner.query(`ALTER TABLE "po" ADD "totalPO" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "po" DROP COLUMN "prId"`)
    await queryRunner.query(`ALTER TABLE "po" ADD "prId" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "po_contract" ADD "usedBudget" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "po_contract" ADD "totalPO" int`)
    await queryRunner.query(`ALTER TABLE "po_contract" ADD "quantityPo" int`)

    await queryRunner.query(`ALTER TABLE "po" ADD "quantityPo" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "po" ADD "usedBudget" varchar(250)`)

    await queryRunner.query(`DROP TABLE "po_item"`)
    await queryRunner.query(
      `ALTER TABLE "po_contract" ADD CONSTRAINT "FK_66e273ae29d05ea3faca09ec8d2" FOREIGN KEY ("poId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "po_contract" ADD CONSTRAINT "FK_6a4db1ae62b6adab3addeecb9c9" FOREIGN KEY ("contractId") REFERENCES "contract"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "po_pr" ADD CONSTRAINT "FK_529f0b9999d77fe7a93eba692f1" FOREIGN KEY ("prIds") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "po_pr" ADD CONSTRAINT "FK_67d4c2ecf748c2b1e1a0d005452" FOREIGN KEY ("poId") REFERENCES "po"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }
}
