import { MigrationInterface, QueryRunner } from 'typeorm'

export class ChangeNamePO1753239538665 implements MigrationInterface {
  name = 'ChangeNamePO1753239538665'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "po_item" DROP COLUMN "unit"`)
    await queryRunner.query(`ALTER TABLE "po_item" ADD "unitName" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "po_item" DROP CONSTRAINT "DF_f25fa5faf4f1ad3f145ed0b6991"`)
    await queryRunner.query(`ALTER TABLE "po_item" DROP CONSTRAINT "DF_e3310925a02037b7327f81c397b"`)
    await queryRunner.query(`ALTER TABLE "po_item" DROP COLUMN "price"`)
    await queryRunner.query(`ALTER TABLE "po_item" ADD "price" int`)
    await queryRunner.query(`ALTER TABLE "po_item" DROP COLUMN "totalPrice"`)
    await queryRunner.query(`ALTER TABLE "po_item" ADD "totalPrice" int`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "po_item" DROP COLUMN "totalPrice"`)
    await queryRunner.query(`ALTER TABLE "po_item" ADD "totalPrice" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "po_item" DROP COLUMN "price"`)
    await queryRunner.query(`ALTER TABLE "po_item" ADD "price" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "po_item" ADD CONSTRAINT "DF_e3310925a02037b7327f81c397b" DEFAULT 0 FOR "quantityUptoPO"`)
    await queryRunner.query(`ALTER TABLE "po_item" ADD CONSTRAINT "DF_f25fa5faf4f1ad3f145ed0b6991" DEFAULT 0 FOR "quantity"`)
    await queryRunner.query(`ALTER TABLE "po_item" DROP COLUMN "unitName"`)
    await queryRunner.query(`ALTER TABLE "po_item" ADD "unit" varchar(255)`)
  }
}
