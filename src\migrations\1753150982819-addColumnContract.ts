import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddColumnContract1753150982819 implements MigrationInterface {
  name = 'AddColumnContract1753150982819'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contract" ADD "partialDeliveryName" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "partialDeliveryEnName" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "transshipmentName" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "transshipmentEnName" nvarchar(450)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "showLotName" nvarchar(100)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "showLotEnName" nvarchar(100)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "freightPaymentTermName" nvarchar(100)`)
    await queryRunner.query(`ALTER TABLE "contract" ADD "freightPaymentTermEnName" nvarchar(100)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "freightPaymentTermEnName"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "freightPaymentTermName"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "showLotEnName"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "showLotName"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "transshipmentEnName"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "transshipmentName"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "partialDeliveryEnName"`)
    await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "partialDeliveryName"`)
  }
}
