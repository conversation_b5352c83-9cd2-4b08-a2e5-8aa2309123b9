import { Injectable } from '@nestjs/common'
import { ShipmentConfigTemplateDetailRepository, ShipmentConfigTemplateRepository } from '../../repositories/shipmentConfigTemplate.repository'
import { PaginationDto, UserDto } from '../../dto'
import { Between, In, Like } from 'typeorm'
import { ShipmentConfigTemplateCreateDto, ShipmentConfigTemplateUpdateDto } from './dto/shipmentConfigTemplateCreate.dto'
import { EmployeeRepository, SupplierRepository } from '../../repositories'
import { ShipmentConfigTemplateEntity } from '../../entities/shipmentConfigTemplate.entity'
import { ShipmentConfigTemplateDetailEntity } from '../../entities/shipmentConfigTemplateDetail.entity'
import { enumData } from '../../constants'
import { ShipmentFeeConditionsRepository } from '../../repositories/shipmentTemplate.repository'

@Injectable()
export class ShipmentConfigTemplateService {
  constructor(
    private readonly repo: ShipmentConfigTemplateRepository,
    private readonly shipmentConfigTemplateDetailRepo: ShipmentConfigTemplateDetailRepository,
    private readonly shipmentFeeConditionsRepository: ShipmentFeeConditionsRepository,
    private readonly supplierRepository: SupplierRepository,
    private readonly employeeRepo: EmployeeRepository,
  ) {}

  async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { isDeleted: false, status: 'Active' }
    if (data.where.name) whereCon.createdBy = Like(`%${data.where.name}%`)
    if (data.where.createdAt?.length === 2) {
      whereCon.createdAt = Between(new Date(data.where.createdAt[0]), new Date(data.where.createdAt[1]))
    }
    if (data.where.status) whereCon.status = Like(`%${data.where.status}%`)

    let res: any = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
    })
    const dicEmployee = await this.employeeRepo.find({
      where: {
        isDeleted: false,
      },
    })
    for (const item of res[0]) {
      const employee = dicEmployee.find((emp) => emp.id === item.createdBy)
      // item.createdByEmployee = employee?.name
      item.createdByEmployeeName = employee.name
      item.createdByEmployee = employee.id
      item.employeeId = employee.id
      item.statusName = enumData.ShipmentConfigTemplate[item.status]?.name
      item.statusColor = enumData.ShipmentConfigTemplate[item.status]?.color
      item.statusBgColor = enumData.ShipmentConfigTemplate[item.status]?.backgroundColor
      item.statusBorderColor = enumData.ShipmentConfigTemplate[item.status]?.statusBorderColor
    }

    return res
  }

  async create(user: UserDto, data: ShipmentConfigTemplateCreateDto) {
    await this.repo.manager.transaction(async (trans) => {
      const repo = trans.getRepository(ShipmentConfigTemplateEntity)
      const repoDetail = trans.getRepository(ShipmentConfigTemplateDetailEntity)
      const entity = repo.create(data)
      entity.createdBy = data.employeeId
      entity.uomIdQuantity = data.templateObject?.uomIdQuantity
      entity.uomIdWeight = data.templateObject?.uomIdWeight
      entity.uomIdVolume = data.templateObject?.uomIdVolume
      entity.quantity = data.templateObject?.quantity || 0
      entity.weight = data.templateObject?.weight || 0
      entity.volume = data.templateObject?.volume || 0
      entity.configTable = data.templateObject?.tableConfig

      entity.shipmentFeeConditionTypeCompactId = (data.templateObject.dataHeaderTemplate.map((item: any) => item.id) || []).join(',')

      entity.shipmentFeeConditionTypeCompactCode = (data.templateObject.dataHeaderTemplate.map((item: any) => item.code) || []).join(',')

      entity.configCostShipment = data.templateObject?.lstCostConfigShipment
      // map ra danh sách giá trị
      const lstCompactValueId: any = []
      data.templateObject.dataHeaderTemplate.forEach((element: any) => {
        /* tạo mới detail  */
        if (data.templateObject.data[element.code]) {
          lstCompactValueId.push(data.templateObject.data[element.code])
        } else {
          lstCompactValueId.push(null)
        }
      })
      entity.shipmentFeeConditionTypeCompactValue = lstCompactValueId.join(',')

      entity.companyId = user.companyId
      const template = await repo.save(entity)

      const lstDetailId = data.lstShipmentConfigTemplateDetail.map((item) => item.id)
      for (const id of lstDetailId) {
        const detail = {
          shipmentConfigTemplateId: template.id,
          shipmentConditionTypeId: id,
        }
        const detailEntity = repoDetail.create(detail)
        await repoDetail.save(detailEntity)
      }
    })
    return { message: 'IMPORT_SUCCESS' }
  }

  async update(user: UserDto, data: ShipmentConfigTemplateUpdateDto) {
    const lstCompactValueId: any = []
    data.templateObject.dataHeaderTemplate.forEach((element: any) => {
      /* tạo mới detail  */
      if (data.templateObject.data[element.code]) {
        lstCompactValueId.push(data.templateObject.data[element.code])
      } else {
        lstCompactValueId.push(null)
      }
    })

    await this.repo.update(data.id, {
      updatedBy: user.id,
      createdBy: data.employeeId,
      name: data.name,
      description: data.description,
      uomIdQuantity: data.templateObject?.uomIdQuantity,
      shipmentFeeConditionTypeCompactId: (data.templateObject.dataHeaderTemplate.map((item: any) => item.id) || []).join(','),
      shipmentFeeConditionTypeCompactValue: lstCompactValueId.join(','),
      shipmentFeeConditionTypeCompactCode: (data.templateObject.dataHeaderTemplate.map((item: any) => item.code) || []).join(','),
      uomIdWeight: data.templateObject?.uomIdWeight,
      uomIdVolume: data.templateObject?.uomIdVolume,
      quantity: data.templateObject?.quantity || 0,
      weight: data.templateObject?.weight || 0,
      volume: data.templateObject?.volume || 0,
      configTable: data.templateObject?.tableConfig,
      configCostShipment: data.templateObject?.lstCostConfigShipment,
      updatedAt: new Date(),
    })

    // danh sách condition type gốc
    const dicConditionType = await this.shipmentConfigTemplateDetailRepo.find({
      where: {
        shipmentConfigTemplateId: data.id,
        isDeleted: false,
      },
    })
    // danh sách condition type update
    const lstConditionTypeUpdate = data.lstShipmentConfigTemplateDetail.map((item) => item.id)
    // Xóa các conditionType bị xóa khi update
    for (const item of dicConditionType) {
      const conditionTypeUpdate = lstConditionTypeUpdate.find((emp) => emp === item.shipmentConditionTypeId)
      if (!conditionTypeUpdate) {
        await this.shipmentConfigTemplateDetailRepo.delete({
          shipmentConfigTemplateId: data.id,
          shipmentConditionTypeId: item.shipmentConditionTypeId,
        })
      }
    }

    for (const item of data.lstShipmentConfigTemplateDetail) {
      const existing = await this.shipmentConfigTemplateDetailRepo.findOne({
        where: {
          shipmentConfigTemplateId: data.id,
          shipmentConditionTypeId: item.id,
        },
      })
      /**Kiểm tra xem ds detail có condition type này hay chưa */
      if (!existing) {
        const detail = {
          shipmentConfigTemplateId: data.id,
          shipmentConditionTypeId: item.id,
        }
        const detailEntity = this.shipmentConfigTemplateDetailRepo.create(detail)
        await this.shipmentConfigTemplateDetailRepo.save(detailEntity)
      }
    }
    return { message: 'UPDATE_SUCCESS' }
  }
  async detailTemplate(user: UserDto, data: any) {
    if (!data?.id) {
      return {}
    }
    const template: any = await this.repo.findOne({
      where: {
        id: data.id,
        isDeleted: false,
      },
      relations: {
        shipmentConfigTemplateDetails: {
          shipmentConditionType: true,
        },
      },
    })
    const dicEmployee = await this.employeeRepo.find({
      where: {
        isDeleted: false,
      },
    })
    const employee = dicEmployee.find((emp) => emp.id === template.createdBy)
    template.createdByEmployeeName = employee.name
    template.createdByEmployee = employee.id
    template.employeeId = employee.id
    template.tableConfig = template.configTable
    template.costConfig = template.configCostShipment
    const lstDetail = template?.__shipmentConfigTemplateDetails__ || []
    const lstConditionType = lstDetail.map((item) => item.__shipmentConditionType__).filter((x) => !!x)
    template.lstConditionType = lstConditionType
    template.dataHeaderTemplate = await this.findList(template.shipmentFeeConditionTypeCompactId?.split(',') || [])
    const dicNumberDetails: any = {}
    template.dataHeaderTemplate.forEach((element) => {
      dicNumberDetails[element.id] = element
    })
    template.shipmentFeeConditionTypeCompactCode = template.shipmentFeeConditionTypeCompactCode?.split(',')
    template.shipmentFeeConditionTypeCompactValue = template.shipmentFeeConditionTypeCompactValue?.split(',')

    let index = 0
    template.data = {}
    template?.shipmentFeeConditionTypeCompactCode?.forEach((detail: any) => {
      // if (dicNumberDetails[detail.shipmentFeeConditionId] && dicNumberDetails[detail.shipmentFeeConditionId].code && detail.idValue) {
      template.data[detail] = template.shipmentFeeConditionTypeCompactValue[index]
      // }
      index++
    })
    delete template.__shipmentConfigTemplateDetails__
    return template
  }

  public async findList(lstId: string[]): Promise<any[]> {
    const lstShipmentFeeCondition: any = await this.shipmentFeeConditionsRepository.find({
      where: { id: In(lstId), isDeleted: false },
      relations: { shipmentFeeConditionsToList: { shipmentFeeConditionsList: true } },
      order: { code: 'ASC' },
    })
    let lstSupplier: any = []
    /* nếu như trong list có isRelatedToSupplier  thì tìm danh sách supplier*/
    const isHaveSupplier = lstShipmentFeeCondition.some((item) => item.isRelatedToSupplier)
    if (isHaveSupplier) {
      /* load danh sách nhà cung cấp */
      lstSupplier = await this.supplierRepository.find({
        select: { id: true, code: true, name: true },
        where: { isDeleted: false },
        order: { code: 'ASC' },
      })
    }
    for (const item of lstShipmentFeeCondition) {
      item.selectionBox = []
      if (item.__shipmentFeeConditionsToList__ && item.__shipmentFeeConditionsToList__.length > 0 && !item.isRelatedToSupplier)
        item.__shipmentFeeConditionsToList__.forEach((condition) => {
          if (condition.__shipmentFeeConditionsList__) {
            const shipmentFeeConditionsListDetail = condition.__shipmentFeeConditionsList__
            item.selectionBox.push({ id: shipmentFeeConditionsListDetail.id, name: shipmentFeeConditionsListDetail.name })
          }
        })
      if (item.__shipmentFeeConditionsToList__ && item.__shipmentFeeConditionsToList__.length > 0 && item.isRelatedToSupplier) {
        item.selectionBox = lstSupplier
      }
    }
    return lstShipmentFeeCondition
  }

  async deleteTempDetail(user: UserDto, data: any) {
    return await this.shipmentConfigTemplateDetailRepo.delete({ shipmentConfigTemplateId: data.id, shipmentConditionTypeId: data.idType })
  }

  async deleteTempConfig(user: UserDto, data: any) {
    return await this.repo.update({ id: data.id }, { isDeleted: true })
  }
  async changStatus(user: UserDto, data: any) {
    return this.repo.update({ id: data.id }, { status: data.status })
  }
}
