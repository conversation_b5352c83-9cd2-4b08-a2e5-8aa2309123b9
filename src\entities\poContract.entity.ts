import { Column, Entity } from 'typeorm'
import { BaseEntity } from './base.entity'

/** ListItem trong PO và quyền*/
@Entity({ name: 'po_contract' })
export class POContractEntity extends BaseEntity {
  @Column({
    nullable: true,
  })
  poId: string

  //   @ManyToOne(() => POEntity, (po) => po.poContract)
  //   @JoinColumn({ name: 'poId', referencedColumnName: 'id' })
  //   po: Promise<POEntity>

  @Column({
    nullable: true,
  })
  contractId: string

  //   @ManyToOne(() => ContractEntity, (po) => po.poContract)
  //   @JoinColumn({ name: 'contractId', referencedColumnName: 'id' })
  //   contract: Promise<ContractEntity>
}
