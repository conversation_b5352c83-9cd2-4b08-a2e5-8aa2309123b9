import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateValue1753268993302 implements MigrationInterface {
  name = 'UpdateValue1753268993302'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" DROP COLUMN "price"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" ADD "price" decimal(10,2)`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" DROP COLUMN "priceYear"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" ADD "priceYear" decimal(10,2)`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" DROP COLUMN "priceQuarter1"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" ADD "priceQuarter1" decimal(10,2)`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" DROP COLUMN "priceQuarter2"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" ADD "priceQuarter2" decimal(10,2)`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" DROP COLUMN "priceQuarter3"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" ADD "priceQuarter3" decimal(10,2)`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" DROP COLUMN "priceQuarter4"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" ADD "priceQuarter4" decimal(10,2)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" DROP COLUMN "priceQuarter4"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" ADD "priceQuarter4" bigint`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" DROP COLUMN "priceQuarter3"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" ADD "priceQuarter3" bigint`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" DROP COLUMN "priceQuarter2"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" ADD "priceQuarter2" bigint`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" DROP COLUMN "priceQuarter1"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" ADD "priceQuarter1" bigint`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" DROP COLUMN "priceYear"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" ADD "priceYear" bigint`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" DROP COLUMN "price"`)
    await queryRunner.query(`ALTER TABLE "shipment_condition_type_template" ADD "price" bigint`)
  }
}
