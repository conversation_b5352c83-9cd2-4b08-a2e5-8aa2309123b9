import { Injectable, NotFoundException, BadRequestException, MethodNotAllowedException, NotAcceptableException } from '@nestjs/common'
import {
  ERROR_YOU_DO_NOT_HAVE_PERMISSION,
  ERROR_NOT_FOUND_DATA,
  ERROR_SUPPLIER_USED_TEMPLATE,
  ERROR_INVALID_FOMULAR,
  UPDATE_SUCCESS,
  CREATE_SUCCESS,
  DELETE_SUCCESS,
  IMPORT_SUCCESS,
} from '../../constants'
import { EmailService } from '../email/email.service'
import {
  ServiceRepository,
  BidRepository,
  ServicePriceRepository,
  SupplierServiceRepository,
  ServiceCustomPriceRepository,
  BidCustomPriceRepository,
  BidPriceRepository,
  BidPriceColRepository,
  BidSupplierRepository,
  SupplierRepository,
  BidPrItemRepository,
  BidExMatGroupRepository,
  MaterialGroupRepository,
} from '../../repositories'
import { coreHelper } from '../../helpers'
import { enumData } from '../../constants/enumData'
import { UserDto } from '../../dto'
import { In, IsNull, Like } from 'typeorm'
import {
  BidAuctionEntity,
  BidAuctionPriceEntity,
  BidAuctionSupplierEntity,
  BidAuctionSupplierPriceValueEntity,
  BidCustomPriceEntity,
  BidDealEntity,
  BidDealPriceEntity,
  BidDealSupplierEntity,
  BidDealSupplierPriceValueEntity,
  BidEntity,
  BidHistoryEntity,
  BidPriceColEntity,
  BidPriceColValueEntity,
  BidPriceEntity,
  BidPriceListDetailEntity,
  BidSupplierEntity,
  BidSupplierPriceColValueEntity,
  BidSupplierPriceValueEntity,
  ServicePriceColEntity,
  SupplierServiceEntity,
} from '../../entities'
import {
  BidCustomPriceCreateDto,
  BidCustomPriceUpdateDto,
  BidPriceColCreateDto,
  BidPriceColUpdateDto,
  BidPriceCreateDto,
  BidPriceUpdateDto,
} from './dto2'
import { BidPrEntity } from '../../entities/bidPr.entity'
import { FlowApproveService } from '../flowApprove/flowApprove.service'
import { BidPrItemEntity } from '../../entities/bidPrItem.entity'
import { BidExMatGroupEntity } from '../../entities/bidExgroup.entity'

@Injectable()
export class BidPriceSupService {
  constructor(
    private readonly repo: BidRepository,
    private readonly emailService: EmailService,
    private readonly serviceRepo: ServiceRepository,
    private readonly servicePriceRepo: ServicePriceRepository,
    private readonly serviceCustomPriceRepo: ServiceCustomPriceRepository,
    private readonly bidPriceRepo: BidPriceRepository,
    private readonly bidPriceColRepo: BidPriceColRepository,
    private readonly bidCustomPriceRepo: BidCustomPriceRepository,
    private readonly bidSupplierRepo: BidSupplierRepository,
    private readonly supplierRepo: SupplierRepository,
    private readonly bidPrItemRepository: BidPrItemRepository,
    private readonly bidExMatGroupRepository: BidExMatGroupRepository,
    private flowService: FlowApproveService,
    private materialGroupRepo: MaterialGroupRepository,
    private supplierServiceRepo: SupplierServiceRepository,
  ) {}

  //#region bidPrice

  /** Lấy các hạng mục giá */
  public async price_find(user: UserDto, data: { bidId: string }) {
    return await this.bidPriceRepo.find({
      where: { bidExgroupId: data.bidId, level: 1, isDeleted: false },
      relations: { childs: true },
    })
  }

  /** Check quyền tạo chào giá cho gói thầu */
  async checkPermissionPriceCreate(user: UserDto, bidId: string) {
    /** chờ rule phân quyền data -> sửa sau */

    let result = false
    let message = 'Gói thầu không còn tồn tại'
    const lstStatusCanEdit = [
      enumData.BidStatus.DangCauHinhGoiThau.code,
      enumData.BidStatus.DangChonNCC.code,
      enumData.BidStatus.TuChoiGoiThau.code,
      enumData.BidStatus.DangDuyetGoiThau.code,
      enumData.BidStatus.DangNhanBaoGia.code,
    ]
    const bid = await this.repo.findOne({
      where: [{ id: bidId }],
    })

    if (bid && lstStatusCanEdit.includes(bid.status)) {
      result = true
      if (!result) {
        message = 'Bạn không có quyền thiết lập các hạng mục chào giá cho gói thầu.'
      }
    } else {
      result = false
      message = 'Chỉ được phép thiết lập các hạng mục chào giá khi chưa mở thầu.'
    }

    return { hasPermission: result, message }
  }

  /** Update statusPrice => DangTao */
  async creatingPrice(user: UserDto, bidId: string) {
    const bid = await this.repo.findOne({ where: { id: bidId } })
    if (!bid) return
    if (bid.statusResetPrice == enumData.BidResetPriceStatus.DangTao.code) return

    await this.repo.update(bidId, {
      statusPrice: enumData.BidPriceStatus.DangTao.code,
      status: enumData.BidStatus.DangCauHinhGoiThau.code,
      updatedBy: user.id,
    })
  }

  async loadPriceItem(user: UserDto, bidItemId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const array = bidItemId.split('.,.,.')
    const bid = await this.bidExMatGroupRepository.findOne({ where: { id: array[0] } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const flag = await this.checkPermissionMpoEditTemplate(user, array[0])
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    await this.priceDeleteAllData(user, bidItemId)
    const lstServicePrice = await this.bidPrItemRepository.find({
      where: { bidId: bid.bidId, isDeleted: false, isExmatgroup: false },
    })

    // Tạo danh sách yêu cầu giá cho gói thầu
    await this.repo.manager.transaction(async (manager) => {
      const bidPriceRepo = manager.getRepository(BidPriceEntity)
      const bidRepo = manager.getRepository(BidEntity)
      const itemRepo = manager.getRepository(BidExMatGroupEntity)
      await itemRepo.update(array[0], { currency: array[1] })
      const lstCol = []
      let count = 1
      for (const servicePrice1 of lstServicePrice) {
        //#region Tạo lv1
        const itemLv1 = new BidPriceEntity()
        itemLv1.sort = count
        count++
        itemLv1.companyId = user.companyId
        itemLv1.createdBy = user.id
        itemLv1.bidItemId = servicePrice1.id
        itemLv1.bidExgroupId = bid.id
        itemLv1.baseItemId = servicePrice1.id
        itemLv1.currency = array[1]
        itemLv1.bidId = bid.bidId
        if (array[1]) itemLv1.currency = array[1]
        itemLv1.name = servicePrice1.shortText || ''
        itemLv1.type = enumData.DataType.String.code
        itemLv1.number = servicePrice1.quantityItem
        itemLv1.level = 1
        // itemLv1.servicePriceId = servicePrice1.id
        const bidPriceLv1 = await bidPriceRepo.save(itemLv1)

        const itemRepo = manager.getRepository(BidExMatGroupEntity)
        await itemRepo.update(bid.id, { currency: array[1] })
        await bidRepo.update(bid.bidId, { isLoadFromItem: true })
        //#endregion
      }
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.id)
  }
  /** Lấy bảng chào giá của lĩnh vực mời thầu */
  async loadPrice(user: UserDto, bidItemId: string) {
    const array = bidItemId.split('.,.,.')
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bid = await this.bidExMatGroupRepository.findOne({ where: { id: array[0] } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const service = await this.serviceRepo.findOne({ where: { externalMaterialGroupId: bid.externalMaterialGroupId.toLowerCase() } })

    const objPermission = await this.checkPermissionPriceCreate(user, bid.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)
    await this.priceDeleteAllData(user, array[0])
    const flag = await this.checkPermissionMpoEditTemplate(user, array[0])
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    const lstServicePrice = await this.servicePriceRepo.find({
      where: { serviceId: service.id, parentId: IsNull(), isDeleted: false },
      order: { sort: 'ASC', createdAt: 'ASC' },
    })

    const lstServicePriceCol = await this.repo.manager.getRepository(ServicePriceColEntity).find({
      where: { serviceId: service.id, isDeleted: false },
      order: { sort: 'ASC', createdAt: 'ASC' },
    })

    // Tạo danh sách yêu cầu giá cho gói thầu
    await this.repo.manager.transaction(async (manager) => {
      const bidPriceRepo = manager.getRepository(BidPriceEntity)
      const bidPriceListDetailRepo = manager.getRepository(BidPriceListDetailEntity)
      const bidPriceColRepo = manager.getRepository(BidPriceColEntity)
      const bidPriceColValueRepo = manager.getRepository(BidPriceColValueEntity)

      const lstCol = []
      for (const col of lstServicePriceCol) {
        const item = new BidPriceColEntity()
        item.companyId = user.companyId
        item.createdBy = user.id

        item.bidExgroupId = bid.id
        item.bidId = bid.bidId

        item.code = col.code
        // item.
        item.name = col.name

        item.fomular = col.fomular
        item.type = col.type
        item.colType = col.colType
        item.isRequired = col.isRequired
        item.sort = col.sort
        const bidPriceColEntity = await bidPriceColRepo.save(item)
        const temp = { ...col, bidPriceColId: bidPriceColEntity.id }
        lstCol.push(temp)
      }

      for (const servicePrice1 of lstServicePrice) {
        //#region Tạo lv1
        const itemLv1 = new BidPriceEntity()
        itemLv1.companyId = user.companyId
        itemLv1.createdBy = user.id
        itemLv1.bidExgroupId = bid.id
        itemLv1.bidId = bid.bidId

        itemLv1.sort = servicePrice1.sort
        itemLv1.name = servicePrice1.name
        itemLv1.isRequired = servicePrice1.isRequired
        itemLv1.type = servicePrice1.type
        itemLv1.percent = servicePrice1.percent
        itemLv1.level = servicePrice1.level
        itemLv1.description = servicePrice1.description
        itemLv1.parentId = servicePrice1.parentId
        itemLv1.scoreDLC = servicePrice1.scoreDLC
        itemLv1.requiredMin = servicePrice1.requiredMin
        itemLv1.unit = servicePrice1.unit
        itemLv1.currency = array[1]
        itemLv1.isSetup = servicePrice1.isSetup
        itemLv1.isTemplate = servicePrice1.isTemplate
        itemLv1.number = servicePrice1.number
        itemLv1.servicePriceId = servicePrice1.id
        const bidPriceLv1 = await bidPriceRepo.save(itemLv1)
        //#endregion

        // Tạo thông tin bổ sung lv1
        const lstDetailLv1 = (await servicePrice1.servicePriceListDetails).filter((c) => !c.isDeleted)
        for (const detailLv1 of lstDetailLv1) {
          const itemDetail1 = new BidPriceListDetailEntity()
          itemDetail1.companyId = user.companyId
          itemDetail1.createdBy = user.id

          itemDetail1.bidPriceId = bidPriceLv1.id
          itemDetail1.name = detailLv1.name
          itemDetail1.type = detailLv1.type
          itemDetail1.value = detailLv1.value
          await bidPriceListDetailRepo.save(itemDetail1)
        }

        // Tạo giá trị cho các cột động lv1
        const lstValueLv1 = await servicePrice1.servicePriceColValues
        for (const itemValueLv1 of lstValueLv1) {
          const objCol = lstCol.find((c) => c.id === itemValueLv1.servicePriceColId)
          if (objCol) {
            const itemValueNew1 = new BidPriceColValueEntity()
            itemValueNew1.companyId = user.companyId
            itemValueNew1.createdBy = user.id
            itemValueNew1.bidPriceId = bidPriceLv1.id
            itemValueNew1.bidPriceColId = objCol.bidPriceColId
            itemValueNew1.value = itemValueLv1.value
            await bidPriceColValueRepo.save(itemValueNew1)
          }
        }

        // Tạo thông tin lv2
        const lstServicePrice2 = (await servicePrice1.childs).filter((c) => !c.isDeleted)
        if (lstServicePrice2.length > 0) {
          for (const servicePrice2 of lstServicePrice2) {
            //#region Tạo lv2
            const itemLv2 = new BidPriceEntity()
            itemLv2.companyId = user.companyId
            itemLv2.createdBy = user.id

            itemLv2.bidExgroupId = bid.id
            itemLv2.bidId = bid.bidId
            itemLv2.sort = servicePrice2.sort
            itemLv2.name = servicePrice2.name
            itemLv2.isRequired = servicePrice2.isRequired
            itemLv2.type = servicePrice2.type
            itemLv2.currency = array[1]
            itemLv2.percent = servicePrice2.percent
            itemLv2.level = servicePrice2.level
            itemLv2.description = servicePrice2.description
            itemLv2.parentId = bidPriceLv1.id
            itemLv2.scoreDLC = servicePrice2.scoreDLC
            itemLv2.requiredMin = servicePrice2.requiredMin
            itemLv2.unit = servicePrice2.unit
            itemLv2.currency = servicePrice2.currency
            itemLv2.number = servicePrice2.number
            itemLv2.isSetup = servicePrice2.isSetup
            itemLv2.servicePriceId = servicePrice2.id
            const bidPriceLv2 = await bidPriceRepo.save(itemLv2)
            //#endregion

            // Tạo thông tin bổ sung lv2
            const lstDetailLv2 = (await servicePrice2.servicePriceListDetails).filter((c) => !c.isDeleted)
            for (const detailLv2 of lstDetailLv2) {
              const itemDetail2 = new BidPriceListDetailEntity()
              itemDetail2.companyId = user.companyId
              itemDetail2.createdBy = user.id
              itemDetail2.bidPriceId = bidPriceLv2.id
              itemDetail2.name = detailLv2.name
              itemDetail2.type = detailLv2.type
              itemDetail2.value = detailLv2.value
              await bidPriceListDetailRepo.save(itemDetail2)
            }

            // Tạo giá trị cho các cột động lv2
            const lstValueLv2 = await servicePrice2.servicePriceColValues
            for (const itemValueLv2 of lstValueLv2) {
              const objCol = lstCol.find((c) => c.id === itemValueLv2.servicePriceColId)
              if (objCol) {
                const itemValueNew2 = new BidPriceColValueEntity()
                itemValueNew2.companyId = user.companyId
                itemValueNew2.createdBy = user.id
                itemValueNew2.bidPriceId = bidPriceLv2.id
                itemValueNew2.bidPriceColId = objCol.bidPriceColId
                itemValueNew2.value = itemValueLv2.value
                await bidPriceColValueRepo.save(itemValueNew2)
              }
            }

            // Tạo thông tin lv3
            const lstServicePrice3 = (await servicePrice2.childs).filter((c) => !c.isDeleted)
            if (lstServicePrice3.length > 0) {
              for (const servicePrice3 of lstServicePrice3) {
                //#region Tạo lv3
                const itemLv3 = new BidPriceEntity()
                itemLv3.companyId = user.companyId
                itemLv3.createdBy = user.id
                itemLv3.bidExgroupId = bid.id
                itemLv3.bidId = bid.bidId
                itemLv3.sort = servicePrice3.sort
                itemLv3.name = servicePrice3.name
                itemLv3.currency = array[1]
                itemLv3.isRequired = servicePrice3.isRequired
                itemLv3.type = servicePrice3.type
                itemLv3.percent = servicePrice3.percent
                itemLv3.level = servicePrice3.level
                itemLv3.description = servicePrice3.description
                itemLv3.parentId = bidPriceLv2.id
                itemLv3.scoreDLC = servicePrice3.scoreDLC
                itemLv3.requiredMin = servicePrice3.requiredMin
                itemLv3.unit = servicePrice3.unit
                itemLv3.currency = servicePrice3.currency
                itemLv3.number = servicePrice3.number
                itemLv3.isSetup = servicePrice3.isSetup
                itemLv3.servicePriceId = servicePrice3.id
                const bidPriceLv3 = await bidPriceRepo.save(itemLv3)
                //#endregion

                // Tạo thông tin bổ sung lv3
                const lstDetailLv3 = (await servicePrice3.servicePriceListDetails).filter((c) => !c.isDeleted)
                for (const detailLv3 of lstDetailLv3) {
                  const itemDetail3 = new BidPriceListDetailEntity()
                  itemDetail3.companyId = user.companyId
                  itemDetail3.createdBy = user.id
                  itemDetail3.bidPriceId = bidPriceLv3.id
                  itemDetail3.name = detailLv3.name
                  itemDetail3.type = detailLv3.type
                  itemDetail3.value = detailLv3.value
                  await bidPriceListDetailRepo.save(itemDetail3)
                }

                // Tạo giá trị cho các cột động lv3
                const lstValueLv3 = await servicePrice3.servicePriceColValues
                for (const itemValueLv3 of lstValueLv3) {
                  const objCol = lstCol.find((c) => c.id === itemValueLv3.servicePriceColId)
                  if (objCol) {
                    const itemValueNew3 = new BidPriceColValueEntity()
                    itemValueNew3.companyId = user.companyId
                    itemValueNew3.createdBy = user.id
                    itemValueNew3.bidPriceId = bidPriceLv3.id
                    itemValueNew3.bidPriceColId = objCol.bidPriceColId
                    itemValueNew3.value = itemValueLv3.value
                    await bidPriceColValueRepo.save(itemValueNew3)
                  }
                }
              }
            }
          }
        }
      }
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.id)
  }

  /** Lấy thông tin cơ cấu giá của lĩnh vực mời thầu */
  async loadCustomPrice(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bid = await this.bidExMatGroupRepository.findOne({ where: { id: bidId } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const service = await this.serviceRepo.findOne({ where: { externalMaterialGroupId: bid.externalMaterialGroupId.toLowerCase() } })
    const objPermission = await this.checkPermissionPriceCreate(user, bid.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    const lstServiceCustomPrice = await this.serviceCustomPriceRepo.find({
      where: { serviceId: bid.id, isDeleted: false },
      order: { sort: 'ASC', createdAt: 'ASC' },
    })

    await this.repo.manager.transaction(async (manager) => {
      const bidCustomPriceRepo = manager.getRepository(BidCustomPriceEntity)
      for (const a of lstServiceCustomPrice) {
        const item = new BidCustomPriceEntity()
        item.companyId = user.companyId
        item.createdBy = user.id
        item.bidExgroupId = bidId
        item.sort = a.sort
        item.name = a.name
        item.isRequired = a.isRequired
        item.type = a.type
        item.unit = a.unit
        item.currency = a.currency
        item.number = a.number
        await bidCustomPriceRepo.save(item)
      }
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.id)
  }

  /** Tạo chào giá cho gói thầu */
  async createPrice(user: UserDto, bidId: string, data: { notePrice: string; isSurvey?: boolean; skipApprove?: boolean }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionPriceCreate(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: bidId } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    bid.statusPrice = data.isSurvey || data.skipApprove ? enumData.BidPriceStatus.DaDuyet.code : enumData.BidPriceStatus.DaTao.code
    bid.notePrice = data.notePrice
    if (bid) {
      bid.statusPrice = data.isSurvey || data.skipApprove ? enumData.BidPriceStatus.DaDuyet.code : enumData.BidPriceStatus.DaTao.code
      bid.notePrice = data.notePrice
      if (bid.statusTech === enumData.BidTechStatus.DaDuyet.code && bid.statusTrade === enumData.BidTradeStatus.DaDuyet.code) {
        bid.status = enumData.BidStatus.DangChonNCC.code
        // chưa chọn => đang chọn, để chọn Doanh nghiệp
        if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.ChuaChon.code) {
          bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DangChon.code
        }
        // đã duyệt => đã chọn, để duyệt lại
        if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.DaDuyet.code) {
          bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DaChon.code
        }
      }
    }
    bid.updatedBy = user.id
    await this.repo.save(bid)

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.TaoGia.code
    bidHistory.save()

    let flowType: string
    flowType = enumData.FlowCode.BIDPRICE.code

    return { message: 'Gửi yêu cầu phê duyệt template chào giá cho gói thầu thành công.' }
  }

  /** Duyệt thiết lập hạng mục cơ cấu, cơ cấu giá của gói thầu */
  async priceAccept(user: UserDto, bidId: string, data: { notePrice?: string }) {
    const approveStatus = await this.flowService.approveRule(user, {
      targetId: bidId,
      entityName: BidEntity.name,
      type: enumData.FlowCode.BIDPRICE.code,
    })

    if (approveStatus.status === enumData.APPROVE_TYPE.NOT_DONE.code) {
      return { message: `Đã duyệt thành công, Vui lòng chờ cấp sau duyệt` }
    } else {
      // const objPermission = await this.checkPermissionTechAccept(user, bidId)
      // if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

      const bid = await this.repo.findOne({ where: { id: bidId, companyId: user.companyId } })
      if (bid) {
        bid.statusPrice = enumData.BidPriceStatus.DaDuyet.code
        bid.notePrice = data.notePrice
        if (
          bid.statusTech === enumData.BidTechStatus.DaDuyet.code &&
          (bid.statusTrade === enumData.BidTradeStatus.DaTao.code || bid.statusTrade === enumData.BidTradeStatus.DaDuyet.code)
        ) {
          bid.status = enumData.BidStatus.DangChonNCC.code
          // chưa chọn => đang chọn, để chọn Doanh nghiệp
          if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.ChuaChon.code) {
            bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DangChon.code
          }
          // đã duyệt => đã chọn, để duyệt lại
          if (bid.statusChooseSupplier === enumData.BidChooseSupplierStatus.DaDuyet.code) {
            bid.statusChooseSupplier = enumData.BidChooseSupplierStatus.DaChon.code
          }
        }
        bid.updatedBy = user.id
        await this.repo.save(bid)
      }

      // Bid History
      const bidHistory = new BidHistoryEntity()
      bidHistory.companyId = user.companyId
      bidHistory.createdBy = user.id
      bidHistory.bidId = bidId
      bidHistory.employeeId = user.employeeId
      bidHistory.status = enumData.BidHistoryStatus.DuyetGia.code
      bidHistory.save()

      // gửi email
      // await this.emailService.ThongBaoDaDuyetKyThuat(bidId)

      return { message: 'Duyệt thiết lập hạng mục chào giá, cơ cấu giá của gói thầu thành công.' }
    }
  }

  /** Lấy chào giá của gói thầu */
  async getPrice(user: UserDto, bidId: string, isMobile?: boolean) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const res = await this.repo.getBid1(user, bidId)
    const havePr = await res.bidPr
    // nếu như không có Pr thì lọc ra
    // if (havePr.length === 0) {
    const lstDataItem = []
    for (const item of res.__bidEx__) {
      lstDataItem.push(item)
    }
    res.listItem = lstDataItem
    res.currency = res.listItem[0]?.currency
    if (res.listItem[0]?.currency) res.isShow = true

    const getDataCell = (row, col) => {
      row[col.id] = ''
      if (row.__bidPriceColValue__?.length > 0) {
        const cell = row.__bidPriceColValue__.find((c) => c.bidPriceColId === col.id)
        if (cell) row[col.id] = cell.value
      }
    }
    for (const item of res.listItem) {
      item.listPrice = await this.bidPriceRepo.getPrice(user, item.id)
      item.listPriceCol = await this.bidPriceColRepo.getBidPriceColMPO(user, item.id)
      item.listPriceColId = item.listPriceCol.map((c) => c.id)
      item.listCustomPrice = await this.bidCustomPriceRepo.find({
        where: { bidExgroupId: item.id },
        order: { sort: 'ASC', createdAt: 'ASC' },
      })

      if (item.listPriceCol.length == 0) continue
      for (const data1 of item.listPrice) {
        for (const col of item.listPriceCol) getDataCell(data1, col)
        for (const data2 of data1.__childs__) {
          for (const col of item.listPriceCol) getDataCell(data2, col)
          for (const data3 of data2.__childs__) {
            for (const col of item.listPriceCol) getDataCell(data3, col)
          }
        }
      }
    }

    if (isMobile) {
      const { listPrice = [], listCustomPrice = [], listPriceCol = [], listPriceColId = [] } = res?.listItem.find((item) => item.isExmatgroup)

      return {
        id: res?.id,
        listPrice,
        listCustomPrice,
        listPriceColId,
        listPriceCol,
      }
    }

    for (let item of res.__prices__) {
      item.lstMaterial = [{ id: item.materialId, name: item.__material__?.name, code: item.__material__?.code }]
      item.lstPlant = [{ id: item.plantId, name: item.__plant__?.name, code: item.__plant__?.code }]
      item.lstMatGroup = [{ id: item.materialGroupId, name: item.__materialGroup__?.name, code: item.__materialGroup__?.code }]
      item.externalMaterialGroupName = item.__externalMaterialGroup__?.name
      item.lstUnit = [{ id: item.uomId, name: item.__uom__?.name, code: item.__uom__?.code }]
      item.lstExternalMatGroup = [
        { id: item.externalMaterialGroupId, name: item.__externalMaterialGroup__?.name, code: item.__externalMaterialGroup__?.code },
      ]

      item.materialCode = item.__material__?.code
      item.shortText = item.__material__?.name
      item.unitCode = item.__uom__?.code
      item.unitId = item.uomId
      delete item.__material__
      delete item.__uom__
      delete item.__plant__
      delete item.__materialGroup__
      delete item.__externalMaterialGroup__
    }
    res.lstDetail = res.__prices__

    return res
  }

  /** Kiểm tra quyền cấu hình lại bảng giá cho gói thầu */
  async checkPermissionResetBidPrice(user: UserDto, bidId: string) {
    let result = false
    let message = 'Gói thầu không còn tồn tại'
    const lstStatus = [enumData.BidStatus.DongDamPhanGia.code, enumData.BidStatus.DongDauGia.code, enumData.BidStatus.HoanTatDanhGia.code]
    const bid = await this.repo.findOne({
      where: [{ id: bidId, parentId: IsNull(), isDeleted: false }, { childs: { id: bidId, isDeleted: false } }],
    })
    if (bid) {
      if (lstStatus.includes(bid.status)) {
        // result = await this.bidEmployeeAccessRepo.isMPO_MPOLeader(user, bid.id)
        result = true
        if (!result) message = 'Bạn không có quyền cấu hình lại bảng giá cho gói thầu.'
      } else message = 'Gói thầu đã thay đổi trạng thái, vui lòng kiểm tra lại.'
    }

    return { hasPermission: result, message }
  }

  /** MPO/MPOLeader cấu hình lại bảng giá cho gói thầu */
  async resetPrice(user: UserDto, bidId: string) {
    /** MPO/MPOLeader cấu hình lại bảng giá cho gói thầu */
    await this.repo.manager.transaction(async (manager) => {
      const bidRepo = manager.getRepository(BidEntity)
      const bidSupplierRepo = manager.getRepository(BidSupplierEntity)
      const bidPriceRepo = manager.getRepository(BidPriceEntity)
      const bidPriceColRepo = manager.getRepository(BidPriceColEntity)
      const priceValueRepo = manager.getRepository(BidSupplierPriceValueEntity)
      const priceColValueRepo = manager.getRepository(BidSupplierPriceColValueEntity)

      const bidPrItemRepository = manager.getRepository(BidPrEntity)
      const bidDealRepo = manager.getRepository(BidDealEntity)
      const bidDealPriceRepo = manager.getRepository(BidDealPriceEntity)
      const bidDealSupplierRepo = manager.getRepository(BidDealSupplierEntity)
      const bidDealSupplierPriceValueRepo = manager.getRepository(BidDealSupplierPriceValueEntity)

      const bidAuctionRepo = manager.getRepository(BidAuctionEntity)
      const bidAuctionPriceRepo = manager.getRepository(BidAuctionPriceEntity)
      const bidAuctionSupplierRepo = manager.getRepository(BidAuctionSupplierEntity)
      const bidAuctionSupplierPriceValueRepo = manager.getRepository(BidAuctionSupplierPriceValueEntity)

      if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
      const bid = await bidPrItemRepository.findOne({ where: { id: bidId } })
      if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
      const objPermission = await this.checkPermissionResetBidPrice(user, bid.bidId)
      if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

      // Lấy những Doanh nghiệp đã nộp hồ sơ thầu
      const lstStatus = [
        enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code,
        enumData.BidSupplierStatus.DangDanhGia.code,
        enumData.BidSupplierStatus.DaDanhGia.code,
      ]
      const lstBidSupplier = await bidSupplierRepo.find({ where: { bidId, status: In(lstStatus) } })

      const bidPrice = await bidPriceRepo.find({ where: { id: bidId } })
      const bidPriceCol = await bidPriceColRepo.find({ where: { id: bidId } })
      const bidDeal = await bidDealRepo.find({ where: { id: bidId } })
      const lstBidDealId = bidDeal.map((c) => c.id)
      const bidAuction = await bidAuctionRepo.find({ where: { id: bidId } })
      const lstBidAuctionId = bidAuction.map((c) => c.id)

      for (const bidSupplier of lstBidSupplier) {
        // Thông tin chào giá cũ của Doanh nghiệp
        const bidPriceValue = await bidSupplier.bidSupplierPriceValue
        const bidPriceColValue = await bidSupplier.bidSupplierPriceColValue

        // Thông tin các lần đàm phán của Doanh nghiệp
        let lstBidDealSupplier: BidDealSupplierEntity[] = []
        if (lstBidDealId.length > 0) {
          lstBidDealSupplier = await bidDealSupplierRepo.find({
            where: { bidDealId: In(lstBidDealId), supplierId: bidSupplier.supplierId },
            relations: { bidDealSupplierPriceValue: true, bidDeal: { bidDealPrices: true } },
          })
        }

        // Thông tin các lần đấ gái của Doanh nghiệp
        let lstBidAuctionSupplier: BidAuctionSupplierEntity[] = []
        if (lstBidAuctionId.length > 0) {
          lstBidAuctionSupplier = await bidAuctionSupplierRepo.find({
            where: { bidAuctionId: In(lstBidAuctionId), supplierId: bidSupplier.supplierId },
            relations: { bidAuctionSupplierPriceValue: true, bidAuction: { bidAuctionPrice: true } },
          })
        }

        // Data lưu log
        const dataSave = {
          bidPrice,
          bidPriceCol,
          bidPriceValue,
          bidPriceColValue,
          lstBidDealSupplier,
          lstBidAuctionSupplier,
        }

        bidSupplier.dataJson = JSON.stringify(dataSave)
        bidSupplier.updatedBy = user.id

        await bidSupplierRepo.save(bidSupplier)
      }

      await bidSupplierRepo.update(
        { bidId },
        {
          statusResetPrice: enumData.BidSupplierResetPriceStatus.KhongYeuCau.code,
          updatedBy: user.id,
        },
      )

      const lstBidPrice = await bidPriceRepo.find({ where: { bidExgroupId: bidId } })
      // Nếu chưa có cấu hình bảng giá trước đó thì không có gì để xóa
      if (lstBidPrice.length == 0) return

      // Xóa chào giá cũ
      const lstBidPriceId = lstBidPrice.map((c) => c.id)
      await priceValueRepo.delete({ bidPriceId: In(lstBidPriceId) })
      await priceColValueRepo.delete({ bidPriceId: In(lstBidPriceId) })

      // Xóa đàm phán giá
      if (lstBidDealId.length > 0) {
        await bidDealPriceRepo.delete({ bidDealId: In(lstBidDealId) })

        const lstBidDealSupplier = await bidDealSupplierRepo.find({ where: { bidDealId: In(lstBidDealId) } })
        const lstBidDealSupplierId = lstBidDealSupplier.map((c) => c.id)
        if (lstBidDealSupplierId.length > 0) {
          await bidDealSupplierPriceValueRepo.delete({
            bidDealSupplierId: In(lstBidDealSupplierId),
          })
          await bidDealSupplierRepo.delete({
            bidDealId: In(lstBidDealId),
          })
        }

        await bidDealRepo.delete({ bidId })
      }

      // Xóa đấu giá
      if (lstBidAuctionId.length > 0) {
        await bidAuctionPriceRepo.delete({ bidAuctionId: In(lstBidAuctionId) })

        const lstBidAuctionSupplier = await bidAuctionSupplierRepo.find({ where: { bidAuctionId: In(lstBidAuctionId) } })
        const lstBidAuctionSupplierId = lstBidAuctionSupplier.map((c) => c.id)
        if (lstBidAuctionSupplierId.length > 0) {
          await bidAuctionSupplierPriceValueRepo.delete({
            bidAuctionSupplierId: In(lstBidAuctionSupplierId),
          })
          await bidAuctionSupplierRepo.delete({
            bidAuctionId: In(lstBidAuctionId),
          })
        }

        await bidAuctionRepo.delete({ bidId })
      }
    })

    await this.repo.update(bidId, {
      // status: enumData.BidStatus.DangDanhGia.code,
      // statusRatePrice: enumData.BidPriceRateStatus.ChuaTao.code,
      statusResetPrice: enumData.BidResetPriceStatus.DangTao.code,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.HieuChinhBangGia.code
    bidHistory.save()

    return { message: 'Thao tác thành công! Gói thầu đang được cấu hình lại bảng giá.' }
  }

  /** MPO/MPOLeader lưu cấu hình lại bảng giá cho gói thầu */
  async saveResetPrice(
    user: UserDto,
    bidId: string,
    data: {
      lstSupplierId: string[]
      resetPriceEndDate: Date
      isRequireFilePriceDetail: boolean
      isRequireFileTechDetail: boolean
    },
  ) {
    if (!data.resetPriceEndDate) throw new BadRequestException('Vui lòng chọn thời điểm kết thúc nộp chào giá hiệu chỉnh.')
    if (data.lstSupplierId == null || data.lstSupplierId.length == 0)
      throw new BadRequestException('Vui lòng chọn doanh nghiệp nộp bảng giá hiệu chỉnh.')

    const bid = await this.bidPrItemRepository.findOne({ where: { id: bidId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionResetBidPrice(user, bid.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    await this.repo.manager.transaction(async (manager) => {
      const bidRepo = manager.getRepository(BidEntity)
      const bidSupplierRepo = manager.getRepository(BidSupplierEntity)

      await bidRepo.update(bidId, {
        resetPriceEndDate: data.resetPriceEndDate,
        isRequireFilePriceDetail: data.isRequireFilePriceDetail,
        isRequireFileTechDetail: data.isRequireFileTechDetail,
        updatedBy: user.id,
      })

      // update lại các thông tin giá của các Doanh nghiệp tham gia gói thầu
      await bidSupplierRepo.update(
        { bidId, supplierId: In(data.lstSupplierId) },
        {
          statusPrice: enumData.BidSupplierPriceStatus.DangBoSung.code,
          scoreManualPrice: 0,
          scorePrice: 0,
          notePrice: '',
          isPriceValid: true,
          statusResetPrice: enumData.BidSupplierResetPriceStatus.YeuCauBoSung.code,
          updatedBy: user.id,
        },
      )
    })

    await this.repo.update(bidId, {
      status: enumData.BidStatus.DangDanhGia.code,
      statusRatePrice: enumData.BidPriceRateStatus.ChuaTao.code,
      statusResetPrice: enumData.BidResetPriceStatus.DaTao.code,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.HoanTatHieuChinhBangGia.code
    bidHistory.save()

    // Gửi email các Doanh nghiệp yêu cầu nộp bảng giá hiệu chỉnh
    // this.emailService.GuiNCCNopLaiChaoGia(bidId)

    return { message: 'Gói thầu đã được cấu hình lại bảng giá thành công.' }
  }

  /** Kiểm tra quyền kết thúc nộp chào giá hiệu chỉnh cho gói thầu */
  async checkPermissionEndResetBidPrice(user: UserDto, bidId: string) {
    let result = false
    let message = 'Gói thầu không còn tồn tại'
    const bid = await this.repo.findOne({
      where: [{ id: bidId }],
    })
    if (bid) {
      if (bid.status == enumData.BidStatus.DangDanhGia.code && bid.statusResetPrice == enumData.BidResetPriceStatus.DaTao.code) {
        result = true
        if (!result) {
          message = 'Bạn không có quyền kết thúc nộp chào giá hiệu chỉnh cho gói thầu.'
        }
      } else {
        result = false
        message = 'Gói thầu đã thay đổi trạng thái, vui lòng kiểm tra lại.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Danh sách Doanh nghiệp tham gia gói thầu */
  async bidSupplierJoinResetPrice(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const objPermission = await this.checkPermissionEndResetBidPrice(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    return await this.bidSupplierRepo.find({
      where: { bidId, statusResetPrice: enumData.BidSupplierResetPriceStatus.DaBoSung.code },
      relations: { supplier: true },
    })
  }

  /** MPO/MPOLeader kết thúc nộp chào giá hiệu chỉnh cho gói thầu */
  async endResetPrice(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionEndResetBidPrice(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    await this.repo.update(bidId, {
      // status: enumData.BidStatus.DangDanhGia.code,
      statusRatePrice: enumData.BidPriceRateStatus.DangTao.code,
      statusRateTrade: enumData.BidTradeRateStatus.DaTao.code, // update để hiện nút duyệt chung cho (chào giá & thương mại)
      statusResetPrice: enumData.BidResetPriceStatus.KetThuc.code,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.KetThucNopChaoGiaHieuChinh.code
    bidHistory.save()

    return { message: 'Thao tác thành công! Gói thầu đã kết thúc nộp chào giá hiệu chỉnh.' }
  }

  /** Lấy cơ cấu giá của gói thầu */
  async getCustomPrice(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const res = await this.repo.getBid1(user, bidId)
    for (const item of res.listItem) {
      item.listCustomPrice = await this.bidCustomPriceRepo.find({
        where: { bidExgroupId: item.id },
        order: { sort: 'ASC', createdAt: 'ASC' },
      })
    }

    return res
  }

  async priceCreateData(user: UserDto, data: BidPriceCreateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.bidId) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await this.bidExMatGroupRepository.findOne({ where: { id: data.bidId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionPriceCreate(user, bid.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Tạo thêm ngoài lấy từ template
    await this.repo.manager.transaction(async (manager) => {
      const bidPriceRepo = manager.getRepository(BidPriceEntity)

      const item = new BidPriceEntity()
      item.companyId = user.companyId
      item.createdBy = user.id
      item.bidExgroupId = bid.id
      if (data.sort !== null) item.sort = data.sort
      item.name = data.name
      item.isRequired = data.isRequired
      item.type = data.type
      item.percent = data.percent
      item.level = data.level
      item.description = data.description
      item.parentId = data.parentId
      item.scoreDLC = data.scoreDLC
      item.requiredMin = data.requiredMin
      item.isSetup = data.isSetup
      if (data.isSetup) {
        item.isTemplate = data.isTemplate
      }
      item.unit = data.unit
      item.currency = data.currency
      item.bidId = bid.bidId
      item.number = data.number

      await bidPriceRepo.save(item)

      const itemRepo = manager.getRepository(BidExMatGroupEntity)
      await itemRepo.update(data.bidId, { currency: data.currency })
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.id)
  }

  async priceUpdateData(user: UserDto, data: BidPriceUpdateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.bidExgroupId) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await this.bidExMatGroupRepository.findOne({ where: { id: data.bidExgroupId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    // const objPermission = await this.checkPermissionPriceCreate(user, bid.bidId)
    // if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    // const flag = await this.checkPermissionMpoEditTemplate(user, data.bidItemId)
    // if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Update
    await this.repo.manager.transaction(async (manager) => {
      const bidPriceRepo = manager.getRepository(BidPriceEntity)

      const item = await bidPriceRepo.findOne({ where: { id: data.id } })
      if (!item) throw new Error(ERROR_NOT_FOUND_DATA)
      if (data.sort !== null) item.sort = data.sort
      item.name = data.name
      item.bidExgroupId = bid.id
      item.isRequired = data.isRequired
      item.type = data.type
      item.percent = data.percent
      item.level = data.level
      item.description = data.description
      item.scoreDLC = data.scoreDLC
      item.requiredMin = data.requiredMin
      item.isSetup = data.isSetup
      item.bidId = bid.bidId
      if (data.isSetup) {
        item.isTemplate = data.isTemplate
      }
      item.unit = data.unit
      item.currency = data.currency
      item.number = data.number
      item.updatedBy = user.id

      await bidPriceRepo.save(item)
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.id)
  }

  async priceDeleteData(user: UserDto, bidPriceId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bidPrice = await this.bidPriceRepo.findOne({ where: { id: bidPriceId } })
    if (!bidPrice) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await bidPrice.bidExgroup
    const objPermission = await this.checkPermissionPriceCreate(user, bid.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    // const flag = await this.checkPermissionMpoEditTemplate(user, bidPrice.bidId)
    // if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Xoá
    await this.repo.manager.transaction(async (manager) => {
      const bidPriceRepo = manager.getRepository(BidPriceEntity)
      const bidPriceListDetailRepo = manager.getRepository(BidPriceListDetailEntity)
      const bidPriceColValueRepo = manager.getRepository(BidPriceColValueEntity)

      const bidPrice = await bidPriceRepo.findOne({ where: { id: bidPriceId } })
      if (!bidPrice) throw new Error(ERROR_NOT_FOUND_DATA)

      // lv2
      const lstData2 = await bidPrice.childs
      if (lstData2.length > 0) {
        for (const data2 of lstData2) {
          const lstData3 = await data2.childs
          if (lstData3.length > 0) {
            for (const data3 of lstData3) {
              // xóa thông tin mở rộng lv3
              await bidPriceListDetailRepo.delete({ bidPriceId: data3.id })
              // xóa giá trị cột động lv3
              await bidPriceColValueRepo.delete({ bidPriceId: data3.id })
            }

            // xóa lv3
            await bidPriceRepo.delete({ parentId: data2.id })
          }

          // xóa thông tin mở rộng lv2
          await bidPriceListDetailRepo.delete({ bidPriceId: data2.id })
          // xóa giá trị cột động lv2
          await bidPriceColValueRepo.delete({ bidPriceId: data2.id })
        }

        // xóa lv2
        await bidPriceRepo.delete({ parentId: bidPriceId })
      }

      // xóa thông tin mở rộng lv1
      await bidPriceListDetailRepo.delete({ bidPriceId: bidPriceId })
      // xóa giá trị cột động lv1
      await bidPriceColValueRepo.delete({ bidPriceId: bidPriceId })

      // xóa lv1
      await bidPriceRepo.delete(bidPriceId)

      const bidPriceRemain = await bidPriceRepo.find({ where: { bidId: bid.bidId } })

      const bidCustomPriceRepo = manager.getRepository(BidCustomPriceEntity)
      const bidCustomPriceRemain = await bidCustomPriceRepo.find({ where: { bidId: bid.bidId } })
      const itemRepo = manager.getRepository(BidPrItemEntity)
      if (bidPriceRemain.length === 0 && bidCustomPriceRemain.length === 0) await itemRepo.update(bid.bidId, { currency: null })
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.id)
  }

  async priceDeleteAllData(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bid = await this.bidExMatGroupRepository.findOne({ where: { id: bidId, isDeleted: false } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionPriceCreate(user, bid.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bid.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // Xóa tất cả
    await this.repo.manager.transaction(async (manager) => {
      const bidPriceRepo = manager.getRepository(BidPriceEntity)
      const bidPriceListDetailRepo = manager.getRepository(BidPriceListDetailEntity)
      const bidPriceColValueRepo = manager.getRepository(BidPriceColValueEntity)

      const level = 1
      const lstBidPriceLv1 = await bidPriceRepo.find({ where: { bidExgroupId: bidId, level } })
      if (lstBidPriceLv1.length > 0) {
        for (const bidPriceLv1 of lstBidPriceLv1) {
          const lstBidPriceLv2 = await bidPriceLv1.childs
          if (lstBidPriceLv2.length > 0) {
            for (const bidPriceLv2 of lstBidPriceLv2) {
              const lstBidPriceLv3 = await bidPriceLv2.childs
              if (lstBidPriceLv3.length > 0) {
                for (const bidPriceLv3 of lstBidPriceLv3) {
                  // xóa thông tin mở rộng lv3
                  await bidPriceListDetailRepo.delete({ bidPriceId: bidPriceLv3.id })
                  // xóa giá trị cột động lv3
                  await bidPriceColValueRepo.delete({ bidPriceId: bidPriceLv3.id })
                }
                // xóa lv3
                await bidPriceRepo.delete({ parentId: bidPriceLv2.id })
              }

              // xoá thông tin mở rộng Lv2
              await bidPriceListDetailRepo.delete({ bidPriceId: bidPriceLv2.id })
              // xoá giá trị cột động Lv2
              await bidPriceColValueRepo.delete({ bidPriceId: bidPriceLv2.id })
            }
            // xoá Lv2
            await bidPriceRepo.delete({ parentId: bidPriceLv1.id })
          }

          // xoá thông tin mở rộng Lv1
          await bidPriceListDetailRepo.delete({ bidPriceId: bidPriceLv1.id })
          // xoá giá trị cột động Lv1
          await bidPriceColValueRepo.delete({ bidPriceId: bidPriceLv1.id })
        }
        // xoá Lv1
        const bidExgroupId = bidId
        await bidPriceRepo.delete({ bidExgroupId })
      }
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.id)
  }

  async setting_fomular(user: UserDto, data: { id: string; fomular: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bid = await this.bidExMatGroupRepository.findOne({ where: { id: data.id, isDeleted: false } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionPriceCreate(user, bid.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.id)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    const lstField = await this.bidPriceColRepo.getBidPriceColAll(user, data.id)
    const isValidFomular = await coreHelper.checkFomular(data.fomular, lstField)
    if (!isValidFomular) {
      throw new NotAcceptableException(ERROR_INVALID_FOMULAR)
    }

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.id)

    bid.fomular = data.fomular
    bid.updatedBy = user.id
    const res = await this.bidExMatGroupRepository.save(bid)
    return res
  }

  /** Setup cách tính điểm giá của Item gói thầu */
  async saveSettingPriceCalWay(user: UserDto, data: { id: string; wayCalScorePrice: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionPriceCreate(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.id)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    await this.bidExMatGroupRepository.update(data.id, { wayCalScorePrice: data.wayCalScorePrice, updatedBy: user.id })

    return { message: UPDATE_SUCCESS }
  }

  /** Import excel chào giá */
  public async price_import(user: UserDto, bidId: string, data: { lstDataTable1: any[]; lstDataTable2: any[] }) {
    await this.priceDeleteAllData(user, bidId)

    // Import excel chào giá
    await this.repo.manager.transaction(async (manager) => {
      const bidPriceColValueRepo = manager.getRepository(BidPriceColValueEntity)
      const bidPriceRepo = manager.getRepository(BidPriceEntity)
      const bidPriceColRepo = manager.getRepository(BidPriceColEntity)
      const bidPriceListDetailRepo = manager.getRepository(BidPriceListDetailEntity)

      const lstBidPriceCol = await bidPriceColRepo.find({
        where: { bidExgroupId: bidId, colType: enumData.ColType.MPO.code, isDeleted: false },
        order: { sort: 'ASC', createdAt: 'ASC' },
      })

      // add lv1
      var lstDataLv1 = data.lstDataTable1.filter((c: any) => c.level == 1)
      for (const item of lstDataLv1) {
        const objBidPriceNew = new BidPriceEntity()
        objBidPriceNew.companyId = user.companyId
        objBidPriceNew.createdBy = user.id
        // objBidPriceNew.bidId = bidId
        objBidPriceNew.bidExgroupId = bidId
        objBidPriceNew.name = item.name
        objBidPriceNew.sort = item.sort || 0
        objBidPriceNew.unit = item.unit
        objBidPriceNew.currency = item.currency
        objBidPriceNew.number = item.number
        objBidPriceNew.isRequired = item.isRequired
        objBidPriceNew.level = 1
        objBidPriceNew.type = enumData.DataType.Number.code
        objBidPriceNew.isTemplate = false
        objBidPriceNew.isSetup = false

        const objBidPrice = await bidPriceRepo.save(objBidPriceNew)
        item.id = objBidPrice.id

        for (const col of lstBidPriceCol) {
          if (item[col.id] != null) {
            const objBidPriceColValueNew = new BidPriceColValueEntity()
            objBidPriceColValueNew.companyId = user.companyId
            objBidPriceColValueNew.createdBy = user.id
            objBidPriceColValueNew.bidPriceId = item.id
            objBidPriceColValueNew.bidPriceColId = col.id
            objBidPriceColValueNew.value = item[col.id]

            await bidPriceColValueRepo.save(objBidPriceColValueNew)
          }
        }

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new BidPriceListDetailEntity()
            detailNew.companyId = user.companyId
            detailNew.createdBy = user.id
            detailNew.bidPriceId = item.id
            detailNew.name = detail.nameList
            detailNew.type = detail.typeDataList
            detailNew.value = detail.valueList
            await bidPriceListDetailRepo.save(detailNew)
          }
        }
      }

      // add lv2
      var lstDataLv2 = data.lstDataTable1.filter((c: any) => c.level == 2)
      for (const item of lstDataLv2) {
        const objBidPriceNew = new BidPriceEntity()
        objBidPriceNew.companyId = user.companyId
        objBidPriceNew.createdBy = user.id
        objBidPriceNew.bidExgroupId = bidId
        objBidPriceNew.name = item.name
        objBidPriceNew.sort = item.sort || 0
        objBidPriceNew.unit = item.unit
        objBidPriceNew.currency = item.currency
        objBidPriceNew.number = item.number
        objBidPriceNew.isRequired = item.isRequired
        objBidPriceNew.level = 2
        objBidPriceNew.type = enumData.DataType.Number.code
        objBidPriceNew.isTemplate = false
        objBidPriceNew.isSetup = false
        const parent = lstDataLv1.find((c: any) => c.zenId == item.parentZenId)
        if (parent) objBidPriceNew.parentId = parent.id

        const objBidPrice = await bidPriceRepo.save(objBidPriceNew)
        item.id = objBidPrice.id

        for (const col of lstBidPriceCol) {
          if (item[col.id] != null) {
            const objBidPriceColValueNew = new BidPriceColValueEntity()
            objBidPriceColValueNew.companyId = user.companyId
            objBidPriceColValueNew.createdBy = user.id
            objBidPriceColValueNew.bidPriceId = item.id
            objBidPriceColValueNew.bidPriceColId = col.id
            objBidPriceColValueNew.value = item[col.id]

            await bidPriceColValueRepo.save(objBidPriceColValueNew)
          }
        }

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new BidPriceListDetailEntity()
            detailNew.companyId = user.companyId
            detailNew.createdBy = user.id
            detailNew.bidPriceId = item.id
            detailNew.name = detail.nameList
            detailNew.type = detail.typeDataList
            detailNew.value = detail.valueList
            await bidPriceListDetailRepo.save(detailNew)
          }
        }
      }

      // add lv3
      var lstDataLv3 = data.lstDataTable1.filter((c: any) => c.level == 3)
      for (const item of lstDataLv3) {
        const objBidPriceNew = new BidPriceEntity()
        objBidPriceNew.companyId = user.companyId
        objBidPriceNew.createdBy = user.id
        objBidPriceNew.bidExgroupId = bidId
        objBidPriceNew.name = item.name
        objBidPriceNew.sort = item.sort || 0
        objBidPriceNew.unit = item.unit
        objBidPriceNew.currency = item.currency
        objBidPriceNew.number = item.number
        objBidPriceNew.isRequired = item.isRequired
        objBidPriceNew.level = 3
        objBidPriceNew.type = enumData.DataType.Number.code
        objBidPriceNew.isTemplate = false
        objBidPriceNew.isSetup = false
        const parent = lstDataLv2.find((c: any) => c.zenId == item.parentZenId)
        if (parent) objBidPriceNew.parentId = parent.id

        const objBidPrice = await bidPriceRepo.save(objBidPriceNew)
        item.id = objBidPrice.id

        for (const col of lstBidPriceCol) {
          if (item[col.id] != null) {
            const objBidPriceColValueNew = new BidPriceColValueEntity()
            objBidPriceColValueNew.companyId = user.companyId
            objBidPriceColValueNew.createdBy = user.id
            objBidPriceColValueNew.bidPriceId = item.id
            objBidPriceColValueNew.bidPriceColId = col.id
            objBidPriceColValueNew.value = item[col.id]

            await bidPriceColValueRepo.save(objBidPriceColValueNew)
          }
        }

        const lstDetail = data.lstDataTable2.filter((c: any) => c.zenListId == item.zenId)
        if (lstDetail.length > 0) {
          for (const detail of lstDetail) {
            const detailNew = new BidPriceListDetailEntity()
            detailNew.companyId = user.companyId
            detailNew.createdBy = user.id
            detailNew.bidPriceId = item.id
            detailNew.name = detail.nameList
            detailNew.type = detail.typeDataList
            detailNew.value = detail.valueList
            await bidPriceListDetailRepo.save(detailNew)
          }
        }
      }
    })

    return { message: IMPORT_SUCCESS }
  }

  async customPriceCreateData(user: UserDto, data: BidCustomPriceCreateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.bidId) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await this.bidExMatGroupRepository.findOne({ where: { id: data.bidId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionPriceCreate(user, bid.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    await this.repo.manager.transaction(async (manager) => {
      const bidCustomPriceRepo = manager.getRepository(BidCustomPriceEntity)

      const item = new BidCustomPriceEntity()
      item.companyId = user.companyId
      item.createdBy = user.id
      item.bidExgroupId = bid.id
      if (data.sort !== null) item.sort = data.sort
      item.name = data.name
      item.isRequired = data.isRequired
      item.type = data.type
      item.unit = data.unit
      item.currency = data.currency
      item.number = data.number
      await bidCustomPriceRepo.save(item)

      const itemRepo = manager.getRepository(BidPrItemEntity)
      await itemRepo.update(data.bidId, { currency: data.currency })
    })

    /* cập nhật bidItem thành currency của itemitem */

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.id)
  }

  async customPriceUpdateData(user: UserDto, data: BidCustomPriceUpdateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.bidId) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await this.bidExMatGroupRepository.findOne({ where: { id: data.bidId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    // const objPermission = await this.checkPermissionPriceCreate(user, bid.bidId)
    // if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, data.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    await this.repo.manager.transaction(async (manager) => {
      const bidCustomPriceRepo = manager.getRepository(BidCustomPriceEntity)
      const item = await bidCustomPriceRepo.findOne({ where: { id: data.id } })
      if (!item) throw new Error(ERROR_NOT_FOUND_DATA)
      if (data.sort !== null) item.sort = data.sort
      item.name = data.name
      item.isRequired = data.isRequired
      item.type = data.type
      item.unit = data.unit
      item.currency = data.currency
      item.number = data.number
      item.updatedBy = user.id
      await bidCustomPriceRepo.save(item)
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.id)
  }

  async customPriceDeleteData(user: UserDto, bidCustomPriceId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bidCustomPrice = await this.bidCustomPriceRepo.findOne({ where: { id: bidCustomPriceId } })
    if (!bidCustomPrice) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const bid = await bidCustomPrice.bidExgroup

    const objPermission = await this.checkPermissionPriceCreate(user, bid.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)
    const flag = await this.checkPermissionMpoEditTemplate(user, bid.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    await this.repo.manager.transaction(async (manager) => {
      const bidCustomPriceRepo = manager.getRepository(BidCustomPriceEntity)
      const bidCustomPrice = await bidCustomPriceRepo.findOne({ where: { id: bidCustomPriceId } })
      if (!bidCustomPrice) throw new Error(ERROR_NOT_FOUND_DATA)
      await bidCustomPriceRepo.delete(bidCustomPriceId)
      const bidPriceRepo = manager.getRepository(BidPriceEntity)
      const bidPriceRemain = await bidPriceRepo.find({ where: { bidId: bid.bidId } })

      const bidCustomPriceRemain = await bidCustomPriceRepo.find({ where: { bidId: bid.bidId } })
      const itemRepo = manager.getRepository(BidPrItemEntity)
      if (bidPriceRemain.length === 0 && bidCustomPriceRemain.length === 0) await itemRepo.update(bid.bidId, { currency: null })
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.id)
  }

  async customPriceDeleteAllData(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bid = await this.bidExMatGroupRepository.findOne({ where: { id: bidId } })
    const objPermission = await this.checkPermissionPriceCreate(user, bid.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    const flag = await this.checkPermissionMpoEditTemplate(user, bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    await this.repo.manager.transaction(async (manager) => {
      const bidCustomPriceRepo = manager.getRepository(BidCustomPriceEntity)
      const bidExgroupId = bidId
      await bidCustomPriceRepo.delete({ bidExgroupId })
    })

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.id)
  }

  public async custompriceImport(user: UserDto, bidId: string, data: { lstData: any[] }) {
    await this.customPriceDeleteAllData(user, bidId)

    return this.repo.manager.transaction(async (manager) => {
      const bidCustomPriceRepo = manager.getRepository(BidCustomPriceEntity)

      for (const item of data.lstData) {
        const objBidCustomPriceNew = new BidCustomPriceEntity()
        objBidCustomPriceNew.companyId = user.companyId
        objBidCustomPriceNew.createdBy = user.id
        // objBidCustomPriceNew.bidId = bidId
        objBidCustomPriceNew.bidExgroupId = bidId

        objBidCustomPriceNew.name = item.name
        objBidCustomPriceNew.sort = item.sort || 0
        objBidCustomPriceNew.unit = item.unit
        objBidCustomPriceNew.currency = item.currency
        objBidCustomPriceNew.number = item.number
        objBidCustomPriceNew.isRequired = item.isRequired
        objBidCustomPriceNew.type = enumData.DataType.Number.code

        await bidCustomPriceRepo.save(objBidCustomPriceNew)
      }
    })
  }

  public async bidPriceCol_list(user: UserDto, bidId: string) {
    const res: any[] = await this.bidPriceColRepo.getBidPriceColAll(user, bidId)
    for (const item of res) {
      /* tìm bidId dựa theo bidItemId */
      // const bid = await this.bidPrItemRepository.findOne({ where: { id: item.bidItemId } })
      item.bidId = item.bidExgroupId
      item.typeName = enumData.DataType[item.type]?.name || ''
      item.colTypeName = enumData.ColType[item.colType]?.name || ''
    }

    return res
  }

  public async bidPriceCol_create_data(user: UserDto, data: BidPriceColCreateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bid = await this.bidExMatGroupRepository.findOne({ where: { id: data.bidId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionPriceCreate(user, bid.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    // const flag = await this.checkPermissionMpoEditTemplate(user, data.bidId)
    // if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    if (data.fomular?.length > 0) {
      const lstField = await this.bidPriceColRepo.getBidPriceColAll(user, data.bidId)
      const isValidFomular = await coreHelper.checkFomular(data.fomular, lstField)
      if (!isValidFomular) throw new NotAcceptableException(ERROR_INVALID_FOMULAR)
    }

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.id)

    const entity = new BidPriceColEntity()
    entity.companyId = user.companyId
    entity.createdBy = user.id
    entity.code = data.code
    entity.name = data.name
    entity.fomular = data.fomular
    entity.type = data.type
    entity.colType = data.colType
    entity.isRequired = data.isRequired
    entity.sort = data.sort || 0
    // entity.bidId = data.bidId
    entity.bidExgroupId = bid.id
    entity.bidId = bid.bidId

    return await entity.save()
  }

  public async bidPriceCol_update_data(user: UserDto, data: BidPriceColUpdateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const bid = await this.bidExMatGroupRepository.findOne({ where: { id: data.bidId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    const objPermission = await this.checkPermissionPriceCreate(user, data.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bid.bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.id)

    const entity = await this.bidPriceColRepo.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (data.fomular !== entity.fomular) {
      // Nếu update cthuc thì kiểm tra lại
      if (data.fomular?.length > 0) {
        const lstField = await this.bidPriceColRepo.getBidPriceColAll(user, entity.bidExgroupId)
        const isValidFomular = await coreHelper.checkFomular(data.fomular, lstField)
        if (!isValidFomular) throw new Error(ERROR_INVALID_FOMULAR)
      }

      entity.fomular = data.fomular
    }
    entity.code = data.code
    entity.name = data.name
    entity.type = data.type
    entity.colType = data.colType
    entity.isRequired = data.isRequired
    entity.sort = data.sort || 0
    entity.updatedBy = user.id
    return await entity.save()
  }

  public async bidPriceCol_delete_data(user: UserDto, id: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bidPriceCol = await this.bidPriceColRepo.findOne({ where: { id } })
    if (!bidPriceCol) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const bid = await bidPriceCol.bidItem

    const objPermission = await this.checkPermissionPriceCreate(user, bidPriceCol.bidExgroupId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    // const flag = await this.checkPermissionMpoEditTemplate(user, bidPriceCol.bidItemId)
    // if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.id)

    return this.repo.manager.transaction(async (manager) => {
      const bidPriceColValueRepo = manager.getRepository(BidPriceColValueEntity)
      const bidPriceColRepo = manager.getRepository(BidPriceColEntity)
      await bidPriceColValueRepo.delete({ bidPriceColId: id })
      await bidPriceColRepo.delete(id)
      return { message: DELETE_SUCCESS }
    })
  }

  public async bidPriceCol_delete_all_data(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const objPermission = await this.checkPermissionPriceCreate(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    const bid = await this.bidExMatGroupRepository.findOne({ where: { id: bidId } })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.id)

    return this.repo.manager.transaction(async (manager) => {
      const bidPriceColValueRepo = manager.getRepository(BidPriceColValueEntity)
      const bidPriceColRepo = manager.getRepository(BidPriceColEntity)
      const bidExgroupId = bidId
      const lstBidPriceCol = await bidPriceColRepo.find({ where: { bidExgroupId } })
      for (const bidPriceCol of lstBidPriceCol) {
        await bidPriceColValueRepo.delete({ bidPriceColId: bidPriceCol.id })
      }

      await bidPriceColRepo.delete({ bidExgroupId })

      return { message: DELETE_SUCCESS }
    })
  }

  public async bidPriceListDetail_list(user: UserDto, bidPriceId: string) {
    return await this.repo.manager.getRepository(BidPriceListDetailEntity).find({
      where: { bidPriceId },
      order: { createdAt: 'ASC' },
    })
  }

  public async bidPriceListDetail_create_data(user: UserDto, data: { bidPriceId: string; name: string; type: string; value: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bidPrice = await this.bidPriceRepo.findOne({ where: { id: data.bidPriceId } })
    if (!bidPrice) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const bid = await bidPrice.bidExgroup

    const objPermission = await this.checkPermissionPriceCreate(user, bidPrice.bidExgroupId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidPrice.bidExgroupId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.id)

    const entity = new BidPriceListDetailEntity()
    entity.companyId = user.companyId
    entity.createdBy = user.id
    entity.name = data.name
    entity.type = data.type
    entity.value = data.value
    entity.bidPriceId = data.bidPriceId
    await entity.save()

    return { id: entity.id, message: CREATE_SUCCESS }
  }

  public async bidPriceListDetail_update_data(user: UserDto, data: { id: string; bidPriceId: string; name: string; type: string; value: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bidPrice = await this.bidPriceRepo.findOne({ where: { id: data.bidPriceId } })
    if (!bidPrice) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const bid = await bidPrice.bidExgroup

    const objPermission = await this.checkPermissionPriceCreate(user, bidPrice.bidExgroupId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidPrice.bidExgroupId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.id)

    const entity = await this.repo.manager.getRepository(BidPriceListDetailEntity).findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    entity.name = data.name
    entity.type = data.type
    entity.value = data.value
    entity.updatedBy = user.id
    await entity.save()

    return { id: entity.id, message: UPDATE_SUCCESS }
  }

  public async bidPriceListDetail_delete_data(user: UserDto, id: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const bidPriceListDetail = await this.repo.manager.getRepository(BidPriceListDetailEntity).findOne({ where: { id } })
    if (!bidPriceListDetail) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    const bidPrice = await bidPriceListDetail.bidPrice
    const bid = await bidPrice.bidExgroup

    const objPermission = await this.checkPermissionPriceCreate(user, bidPrice.bidExgroupId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const flag = await this.checkPermissionMpoEditTemplate(user, bidPrice.bidExgroupId)
    if (!flag) throw new NotFoundException(ERROR_SUPPLIER_USED_TEMPLATE)

    // cập nhật statusPrice => DangTao
    await this.creatingPrice(user, bid.id)

    await this.repo.manager.getRepository(BidPriceListDetailEntity).delete(id)

    return { message: DELETE_SUCCESS }
  }

  //#endregion

  //#region bidChooseSupplier

  /** Kiểm tra quyền mời thầu */
  async checkPermissionChooseSupplier(user: UserDto, bidId: string) {
    let result = false
    let message = 'Gói thầu không còn tồn tại'
    const lstStatusCanEdit = [
      enumData.BidStatus.DangCauHinhGoiThau.code,
      enumData.BidStatus.DangChonNCC.code,
      enumData.BidStatus.TuChoiGoiThau.code,
      enumData.BidStatus.DangDuyetGoiThau.code,
      enumData.BidStatus.DangNhanBaoGia.code,
    ]
    const bid = await this.repo.findOne({
      where: [{ id: bidId }],
    })
    if (bid) {
      if (lstStatusCanEdit.includes(bid.status)) {
        result = true
        if (!result) {
          message = 'Bạn không có quyền chọn nhà cung cấp tham gia cho gói thầu.'
        }
      } else {
        result = false
        message = 'Chỉ được phép chọn nhà cung cấp tham gia khi chưa mở thầu.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Danh sách nhà cung cấp mời thầu */
  async loadSupplierInvite(
    user: UserDto,
    data: { bidId: string; supplierName?: string; lstStatus?: string[]; typeGetData: number; isMobile?: boolean },
  ) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.bidId) throw new BadRequestException(`Vui lòng chọn gói thầu trước`)
    const bid: any = await this.repo.findOne({
      where: { id: data.bidId, isDeleted: false },
      relations: { bidPrItem: true, employeeAccess: true, bidPr: { pr: true } },
      select: {
        id: true,
        name: true,
        status: true,
        statusPrice: true,
        statusTech: true,
        statusTrade: true,
        statusChooseSupplier: true,
        bidPr: { id: true, pr: true },
        bidPrItem: { id: true, serviceId: true },
        employeeAccess: { id: true, employeeId: true, type: true },
      },
    })

    const lstExMatGr = []
    console.log(bid)
    for (const bidPR of bid.__bidPr__) {
      if (bidPR.__pr__) {
        const lstExMat = bidPR.__pr__.lstExternalMaterialGroupId.split(',')

        for (const item of lstExMat) {
          lstExMatGr.push(item.toLowerCase())
        }
      }
    }
    const lstService = await this.serviceRepo.find({
      where: { isDeleted: false, externalMaterialGroupId: In(lstExMatGr) },
      select: { id: true, code: true, name: true },
    })
    const lstServiceId = lstService.map((c) => c.id)
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    let whereCon: any = { isDeleted: false }
    whereCon.supplierServices = { serviceId: In(lstServiceId), isDeleted: false }

    if (data.lstStatus?.length > 0) whereCon.supplierServices.statusExpertise = In(data.lstStatus)

    if (data.supplierName) {
      whereCon = [
        { ...whereCon, code: Like(`%${data.supplierName}%`) },
        { ...whereCon, name: Like(`%${data.supplierName}%`) },
      ]
    }

    let lstData: any[] = await this.supplierRepo.find({
      where: whereCon,
      order: { code: 'ASC' },
    })

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.SupplierStatus)
      lstStatus.forEach((c) => (dicStatus[c.code] = c.name))
    }

    let res: any[] = []
    for (const item of lstData) {
      const temp: any = {}
      temp.supplierId = item.id
      temp.supplierCode = item.code
      temp.supplierName = item.name
      temp.isChosen = false
      temp.statusName = dicStatus[item.status]
      temp.createdAt = item.createdAt
      res.push(temp)
    }

    if (bid.statusChooseSupplier != enumData.BidChooseSupplierStatus.ChuaChon.code) {
      const lstBidSupplier = await this.bidSupplierRepo.find({
        where: { bidId: data.bidId },
        select: { id: true, supplierId: true },
      })
      const lstSupplierInvitedID = lstBidSupplier.map((c) => c.supplierId)
      for (const item of res) item.isChosen = lstSupplierInvitedID.includes(item.supplierId)
    }

    // nếu 'Chọn cả doanh nghiệp ngoài LVMH' (không xét isChosen vì có isChosen thì đã gen data supplierService & bidSupplier)
    if (data.typeGetData == 2) {
      let lstSupplier = await this.supplierRepo.find({
        where: { isDeleted: false },
        select: { id: true, code: true, name: true },
      })

      if (lstSupplier.length > 0) {
        const lstSupplierId = lstData.map((c) => c.supplierId)
        if (lstSupplierId.length > 0) {
          lstSupplier = lstSupplier.filter((c) => lstSupplierId.indexOf(c.id) == -1)
        }
        for (const supplier of lstSupplier) {
          const temp: any = {}
          temp.supplierId = supplier.id
          temp.supplierCode = supplier.code
          temp.supplierName = supplier.name
          temp.isChosen = false
          temp.statusName = enumData.SupplierServiceStatus.ChoXacNhan.code
          temp.createdAt = supplier.createdAt
          res.push(temp)
        }
      }
    }

    bid.isMPO = bid.__employeeAccess__.some((p) => p.type === enumData.BidRuleType.MPO.code && p.employeeId === user.employeeId)
    bid.isMPOLeader = bid.__employeeAccess__.some((p) => p.type === enumData.BidRuleType.MPOLeader.code && p.employeeId === user.employeeId)

    const dataRs = await this.flowService.checkCanApproveByType(user, {
      lsType: [enumData.FlowCode.BID_CHOOSE_SUPPLIER.code],
    })

    const enumStatus = enumData.BidChooseSupplierStatus
    const status = bid.statusChooseSupplier
    const lstStatus = [enumStatus.DangChon.code, enumStatus.ChuaChon.code, enumStatus.TuChoi.code]
    const isShowChooseSupplier = lstStatus.includes(status) && bid.isMPO
    const isShowSendMPOLeaderCheck =
      status === enumStatus.DaChon.code &&
      (bid.statusPrice === enumData.BidPriceStatus.DaTao.code || enumData.BidPriceStatus.DaDuyet.code) &&
      (bid.statusTech === enumData.BidTechStatus.DaTao.code || bid.statusTech === enumData.BidTechStatus.DaDuyet.code) &&
      (bid.statusTrade === enumData.BidTradeStatus.DaTao.code || bid.statusTrade === enumData.BidTradeStatus.DaDuyet.code)
    const isShowAcceptChooseSupplier = status === enumStatus.GuiDuyet.code
    const canAcceptSup = dataRs[enumData.FlowCode.BID_CHOOSE_SUPPLIER.code]

    if (
      status === enumStatus.DaChon.code ||
      status === enumStatus.DaDuyet.code ||
      status === enumStatus.GuiDuyet.code ||
      (bid.isMPOLeader && !bid.isMPO)
    ) {
      res = res.filter((c) => c.isChosen)
    }

    if (data.isMobile) {
      return res
        .filter((item) => item.isChosen)
        .map((item) => {
          return {
            supplierId: item.supplierId ?? '',
            supplierCode: item.supplierCode ?? '',
            supplierName: item.supplierName ?? '',
            isChosen: item.isChosen ?? false,
            statusName: item.statusName ?? '',
            createdAt: item.createAt ?? '',
          }
        })
    }
    const dataDetail: any = {}

    /* Tìm ra danh sách cấp duyệt và comment và người duyệt */
    dataDetail.haveProgress = false
    dataDetail.approvalProgress = await this.flowService.getListApproveDetail(user, {
      targetId: data.bidId,
      entityName: BidEntity.name,
      type: enumData.FlowCode.BID_CHOOSE_SUPPLIER.code,
    })
    dataDetail.showComment = false
    /* nếu như nhân viên hiện tại chưa duyệt và nằm trong luồng duyệt thì toggle bật showComment = true */
    for (const item of dataDetail.approvalProgress) {
      if (item.approveType == user.orgPositionId && !item.approved) {
        dataDetail.showComment = true
        break
      }
    }
    if (dataDetail.approvalProgress.length > 0) dataDetail.haveProgress = true

    return { isShowChooseSupplier, isShowSendMPOLeaderCheck, isShowAcceptChooseSupplier, canAcceptSup, bidName: bid.name, lstData: res }
  }

  /** Mời thầu */
  async bidChooseSupplier(user: UserDto, data: { bidId: string; lstData: any[] }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionChooseSupplier(user, data.bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid: any = await this.repo.findOne({
      where: { id: data.bidId },
      relations: { bidPrItem: true },
      select: { id: true, bidPrItem: { id: true, serviceId: true } },
    })
    if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

    // xoá bidsupplier cũ của gói thầu
    await this.bidSupplierRepo.delete({ bidId: bid.id })
    const lstItem = bid.__bidPrItem__ || []
    const lstItemId = lstItem.map((c) => c.id)
    // xoá bidsupplier cũ của các Item
    await this.bidSupplierRepo.delete({ bidId: In(lstItemId) })

    const lstSupplierChooseId = data.lstData.filter((item: any) => item.isChosen).map((item: any) => item.supplierId)

    for (const supplierId of lstSupplierChooseId) {
      const newBidSupplier = new BidSupplierEntity()
      newBidSupplier.companyId = user.companyId
      newBidSupplier.createdBy = user.id
      newBidSupplier.supplierId = supplierId || supplierId.supplierId
      newBidSupplier.bidId = data.bidId
      newBidSupplier.status = enumData.BidSupplierStatus.DaDuocChon.code
      newBidSupplier.statusFile = enumData.BidSupplierFileStatus.ChuaKiemTra.code
      newBidSupplier.statusTech = enumData.BidSupplierTechStatus.ChuaXacNhan.code
      newBidSupplier.statusTrade = enumData.BidSupplierTradeStatus.ChuaXacNhan.code
      newBidSupplier.statusPrice = enumData.BidSupplierPriceStatus.ChuaXacNhan.code
      newBidSupplier.scoreTech = newBidSupplier.scoreTrade = newBidSupplier.scorePrice = 0
      await newBidSupplier.save()
    }

    await this.repo.update(data.bidId, {
      statusChooseSupplier: enumData.BidChooseSupplierStatus.DaChon.code,
      updatedBy: user.id,
    })

    if (bid.isSurvey) {
      await this.repo.update(bid.bidId, {
        status: enumData.BidStatus.DangNhanBaoGia.code,
        statusTrade: enumData.BidTradeStatus.DaDuyet.code,
        statusPrice: enumData.BidPriceStatus.DaDuyet.code,
        statusChooseSupplier: enumData.BidChooseSupplierStatus.DaDuyet.code,
        updatedBy: user.id,
      })
    }

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.ChonNCC.code
    bidHistory.save()

    return { message: UPDATE_SUCCESS }
  }

  /** Chọn lại danh sách nhà cung cấp mời thầu */
  async bidRechooseSupplier(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionChooseSupplier(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const objCheck = await this.bidSupplierRepo.findOne({
      where: { bid: [{ id: bidId }, { parentId: bidId }], status: enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code },
    })
    if (objCheck) throw new MethodNotAllowedException('Chỉ được phép chọn lại Doanh nghiệp tham gia khi chưa có Doanh nghiệp nộp hồ sơ thầu.')

    await this.repo.update(bidId, {
      statusChooseSupplier: enumData.BidChooseSupplierStatus.DangChon.code,
      status: enumData.BidStatus.DangChonNCC.code,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.ChonLaiNCC.code
    bidHistory.save()
  }

  /** Gửi yêu cầu phê duyệt bảng chào giá, cơ cấu giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu */
  async bidSendMpoleaderCheck(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionChooseSupplier(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    await this.repo.update(bidId, {
      statusChooseSupplier: enumData.BidChooseSupplierStatus.GuiDuyet.code,
      status: enumData.BidStatus.DangDuyetGoiThau.code,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = bidId
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.GuiMPOLeader.code
    bidHistory.save()

    this.emailService.GuiMpoLeadDuyetGia(bidId)

    // tạo quyền duyệt danh sách nhà cung cấp mời thầu
    let flowType: string
    flowType = enumData.FlowCode.BID_CHOOSE_SUPPLIER.code
    await this.flowService.setRoleRule(user, {
      targetId: bidId,
      entityName: BidEntity.name,
      flowType: flowType,
      companyId: user.orgCompanyId,
      // departmentId: user?.departmentId,
    })

    return { message: 'Gởi yêu cầu phê duyệt nhà cung cấp mời thầu cho gói thầu thành công.' }
  }

  //#endregion

  //#region Accept all & send email invite supplier

  /** Kiểm tra quyền duyệt tất cả */
  async checkPermissionMpoAcceptAll(user: UserDto, bidId: string) {
    let result = false
    let message = 'Gói thầu không còn tồn tại'
    const bid = await this.repo.findOne({
      where: [{ id: bidId }],
    })
    if (bid) {
      if (bid.status === enumData.BidStatus.DangDuyetGoiThau.code) {
        result = true
        if (!result) {
          message = 'Bạn không có quyền xét duyệt bảng chào giá, cơ cấu giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu.'
        }
      } else {
        result = false
        message =
          'Gói thầu đã được xét duyệt bảng chào giá, cơ cấu giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu hoặc chưa khởi tạo xong yêu cầu kỹ thuật, bảng chào giá, cơ cấu giá, điều kiện thương mại và danh sách nhà cung cấp mời thầu.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Duyệt tất cả */
  async acceptAll(user: UserDto, data: { id: string; comment: string }) {
    const approveStatus = await this.flowService.approveRule(user, {
      targetId: data.id,
      entityName: BidEntity.name,
      comment: data.comment,
      type: enumData.FlowCode.BID_CHOOSE_SUPPLIER.code,
    })

    if (approveStatus.status === enumData.APPROVE_TYPE.NOT_DONE.code) {
      return { message: `Đã duyệt thành công, Vui lòng chờ cấp sau duyệt` }
    } else {
      if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
      const bid = await this.repo.findOne({ where: { id: data.id } })
      if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

      // const objPermission = await this.checkPermissionMpoAcceptAll(user, data.id)
      // if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

      const status = enumData.BidSupplierStatus.DaThongBaoMoiThau.code
      await this.bidSupplierRepo.update({ bidId: data.id }, { status, updatedBy: user.id })

      await this.repo.update(data.id, {
        status: enumData.BidStatus.DangNhanBaoGia.code,
        statusTrade: enumData.BidTradeStatus.DaDuyet.code,
        statusPrice: enumData.BidPriceStatus.DaDuyet.code,
        statusChooseSupplier: enumData.BidChooseSupplierStatus.DaDuyet.code,
        noteMPOLeader: data.comment || '',
        updatedBy: user.id,
      })

      // Bid History
      const bidHistory = new BidHistoryEntity()
      bidHistory.companyId = user.companyId
      bidHistory.createdBy = user.id
      bidHistory.bidId = data.id
      bidHistory.employeeId = user.employeeId
      bidHistory.status = enumData.BidHistoryStatus.DuyetGoiThau.code
      bidHistory.save()

      // gửi email
      this.emailService.ThongBaoDaDuyetGia(data.id)
      if (bid.isSendEmailInviteBid) {
        this.emailService.GuiNccThongBaoMoiThau(user, data.id)
      }

      return { message: UPDATE_SUCCESS }
    }
  }

  /** Từ chối tất cả */
  async rejectAll(user: UserDto, data: { id: string; comment?: string }) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionMpoAcceptAll(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    await this.flowService.rejectRule(user, {
      targetId: data.id,
      entityName: BidEntity.name,
      type: enumData.FlowCode.BIDTECH.code,
      comment: data.comment,
    })

    await this.repo.update(data.id, {
      status: enumData.BidStatus.TuChoiGoiThau.code,
      statusTrade: enumData.BidTradeStatus.TuChoi.code,
      statusPrice: enumData.BidPriceStatus.TuChoi.code,
      statusChooseSupplier: enumData.BidChooseSupplierStatus.TuChoi.code,
      updatedBy: user.id,
    })

    // Bid History
    const bidHistory = new BidHistoryEntity()
    bidHistory.companyId = user.companyId
    bidHistory.createdBy = user.id
    bidHistory.bidId = data.id
    bidHistory.employeeId = user.employeeId
    bidHistory.status = enumData.BidHistoryStatus.TuChoiGoiThau.code
    bidHistory.save()

    // gửi email
    this.emailService.GuiMpoTuChoiGia(data.id)

    return { message: UPDATE_SUCCESS }
  }

  //#endregion
  async checkPermissionMpoEditTemplate(user: UserDto, bidId: string) {
    const bid = await this.repo.findOne({ where: { id: bidId } })
    if (bid && bid.statusResetPrice == enumData.BidResetPriceStatus.DangTao.code) {
      return true
    }

    let result = true
    const lstBidSupplierStatus = [
      enumData.BidSupplierStatus.DaXacNhanThamGiaThau.code,
      enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code,
      enumData.BidSupplierStatus.DangDanhGia.code,
      enumData.BidSupplierStatus.DaDanhGia.code,
    ]

    const lstBidSupplier = await this.bidSupplierRepo.find({
      where: { bidId, status: In(lstBidSupplierStatus) },
      select: { id: true },
    })

    if (lstBidSupplier.length > 0) {
      result = false
    }

    return result
  }

  /** Tạo chào giá cho gói thầu */
  async createPriceNew(user: UserDto, data: any) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    const objPermission = await this.checkPermissionPriceCreate(user, data.id)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)
    if (data.lstDetail && data.lstDetail.length === 0) throw new NotFoundException(` Vui lòng thêm ít nhất 1 dòng item.`)
    await this.repo.manager.transaction(async (transac) => {
      const bidHistoryRepo = transac.getRepository(BidHistoryEntity)

      const bid = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
      if (!bid) throw new NotFoundException(ERROR_NOT_FOUND_DATA)

      bid.statusPrice = data.isSurvey || data.skipApprove ? enumData.BidPriceStatus.DaDuyet.code : enumData.BidPriceStatus.DaTao.code
      bid.reference = data.reference
      bid.prId = data.prId
      bid.updatedBy = user.id
      if (data.lstIncotermId) bid.lstIncotermId = data.lstIncotermId
      if (data.lstPaymentTermId) bid.lstPaymentTermId = data.lstPaymentTermId
      bid.updatedAt = new Date()
      await this.repo.save(bid, {}, transac)
      await this.bidPriceRepo.delete({ bidId: bid.id })
      if (data.lstDetail && data.lstDetail.length > 0) {
        for (let item of data.lstDetail) {
          const detail = new BidPriceEntity()
          detail.bidId = bid.id
          detail.itemNo = item.itemNo
          detail.assetCode = item.assetCode
          detail.category = item.category
          detail.materialId = item.materialId
          detail.materialGroupId = item.materialGroupId
          detail.shortText = item.shortText
          detail.deliveryDate = item.deliveryDate
          detail.orderCode = item.orderCode
          detail.plantId = item.plantId
          detail.quantity = item.quantity
          detail.assetDesc = item.assetDesc
          detail.sloc = item.sloc
          detail.orderName = item.orderName
          detail.createdAt = new Date()
          detail.createdBy = user.id
          detail.externalMaterialGroupId = item.externalMaterialGroupId
          detail.uomId = item.unitId
          await this.bidPriceRepo.save(detail, {}, transac)
        }
      }

      // Bid History
      const bidHistory = new BidHistoryEntity()
      bidHistory.companyId = user.companyId
      bidHistory.createdBy = user.id
      bidHistory.bidId = data.id
      bidHistory.employeeId = user.employeeId
      bidHistory.status = enumData.BidHistoryStatus.TaoGia.code
      await bidHistoryRepo.insert(bidHistory)
    })

    return { message: CREATE_SUCCESS }
  }

  /** Danh sách nhà cung cấp mời thầu */
  async loadSupplierInvite2(
    user: UserDto,
    data: { bidId: string; supplierName?: string; lstStatus?: string[]; typeGetData: number; isMobile?: boolean },
  ) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    if (!data.bidId) throw new BadRequestException(`Vui lòng chọn gói thầu trước`)
    const bid: any = await this.repo.findOne({
      where: { id: data.bidId, isDeleted: false },
      relations: { prices: true, employeeAccess: true },
      select: {
        id: true,
        name: true,
        status: true,
        statusPrice: true,
        statusTech: true,
        statusTrade: true,
        statusChooseSupplier: true,
        employeeAccess: { id: true, employeeId: true, type: true },
      },
    })
    let whereCon: any = {}
    let lstExMatGr: any = []

    let lstMatGroup: any = bid.__prices__?.map((x) => x.materialGroupId)
    let serviceId: any = bid.__prices__?.map((x) => x.externalMaterialGroupId)

    const lstMaterialGroup = await this.materialGroupRepo.find({
      where: { id: In(lstMatGroup), isDeleted: false },
      select: { externalMaterialGroupId: true },
    })
    if (lstMaterialGroup.length > 0) lstExMatGr = lstMaterialGroup.map((x) => x.externalMaterialGroupId)

    whereCon.service = {}
    let lstId = lstExMatGr.concat(serviceId.filter((x) => x).map((id) => id.toLowerCase()))
    whereCon.service.externalMaterialGroupId = In(lstId)

    whereCon.status = enumData.SupplierServiceStatus.HoatDong.code
    whereCon.companyId = bid.companyId

    const res: any[] = await this.supplierServiceRepo.find({
      where: whereCon,
      relations: {
        service: true,
        supplier: true,
      },
      order: { createdAt: 'DESC', service: { code: 'ASC' } },
    })
    const listService = res.map((supService: any) => ({
      ...supService.__service__,
      supplierServiceId: supService.id,
      serviceId: supService.__service__.id,
      serviceName: supService.__service__.name,
      supplierId: supService.__supplier__.id,
      supplierName: supService.__supplier__.name,
      supplierCode: supService.__supplier__.code,
    }))

    const lstSupplier = await this.bidSupplierRepo.find({ where: { bidId: data.bidId } })
    // Tạo Set chứa supplierId đã chọn để so sánh nhanh
    const supplierIdSet = new Set(lstSupplier.map((s) => s.supplierId))

    // Gắn isChoose cho listService
    const result = listService.map((item) => ({
      ...item,
      isChosen: supplierIdSet.has(item.supplierId),
    }))

    return result
  }
}
