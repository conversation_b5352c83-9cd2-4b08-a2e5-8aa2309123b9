import { ConflictException, Injectable, NotFoundException } from '@nestjs/common'
import { PaginationDto, UserDto } from '../../dto'
import {
  CREATE_SUCCESS,
  DELETE_SUCCESS,
  enumData,
  ERROR_CODE_TAKEN,
  ERROR_NOT_FOUND_DATA,
  UPDATE_ACTIVE_SUCCESS,
  UPDATE_SUCCESS,
} from '../../constants'
import { EmployeeEntity, TitleEntity } from '../../entities'
import { In, LessThanOrEqual, Like, MoreThanOrEqual } from 'typeorm'
import { FilterOneDto } from '../../dto/filterOne.dto'
import { coreHelper } from '../../helpers'
import { SupplierRepository, TitleRepository } from '../../repositories'
import { ShipmentPlanNumberDetailRepository, ShipmentPlanNumberRepository, ShipmentPlanRepository } from '../../repositories/shipmentPlan.repository'
import { ShipmentConfigTemplateRepository } from '../../repositories/shipmentConfigTemplate.repository'
import { ShipmentConditionTypeTemplateRepository, ShipmentFeeConditionsRepository } from '../../repositories/shipmentTemplate.repository'
import { ShipmentPlanEntity } from '../../entities/shipmentPlan.entity'
import { ShipmentPlanNumberEntity } from '../../entities/shipmentPlanNumber.entity'
import { ShipmentPlanNumberDetailEntity } from '../../entities/shipmentPlanNumberDetail.entity'
import { v4 as uuidv4 } from 'uuid'
import { FlowApproveService } from '../flowApprove/flowApprove.service'

@Injectable()
export class ShipmentPlanService {
  constructor(
    private readonly shipmentPlanRepository: ShipmentPlanRepository,
    private readonly supplierRepository: SupplierRepository,
    private readonly shipmentConfigTemplateRepository: ShipmentConfigTemplateRepository,
    private readonly shipmentFeeConditionsRepository: ShipmentFeeConditionsRepository,
    private readonly shipmentPlanNumberDetailRepository: ShipmentPlanNumberDetailRepository,
    private readonly shipmentConditionTypeTemplateRepository: ShipmentConditionTypeTemplateRepository,
    private readonly shipmentPlanNumberRepository: ShipmentPlanNumberRepository,
    private readonly flowService: FlowApproveService,
  ) {}

  /* hàm load ra danh sách template vận chuyển */
  public async loadTemplateList(user: UserDto): Promise<any> {
    const dataRs = await this.shipmentConfigTemplateRepository.find({
      where: { isDeleted: false, status: enumData.ShipmentConfigTemplate.Active.code },
      order: { createdAt: 'DESC' },
    })
    return dataRs
  }

  /* tìm ra danh sách mapping data */
  private async loadMappingData(arrayConditionTypeId: string, searchingData: any): Promise<any> {
    let priceDetail = null
    if (searchingData.deliveryDate) {
      const deliveryDate = new Date(searchingData.deliveryDate)
      /* chuyển ngày deliveryDate thành  */
      /* tìm ra những bảng giá có thời gian deliveryDate nằm trong dateFrom và dateTo */

      const dataRs = await this.shipmentConditionTypeTemplateRepository.find({
        where: {
          shipmentConditionTypeId: arrayConditionTypeId,
          type: enumData.ShipmentConditionType.MONTH.code,
          status: enumData.ShipmentPriceStatus.APPROVED.code,
          dateFrom: LessThanOrEqual(deliveryDate),
          dateTo: MoreThanOrEqual(deliveryDate),
          isDeleted: false,
        },
      })
      /* for qua để kiểm tra conditionCodeCompact của deliveryDate */
      for (const item of dataRs) {
        const compactId = []
        if (item.conditionCodeCompact) {
          item.conditionCodeCompact.forEach((condition: any) => {
            if (searchingData.data[condition]) {
              compactId.push(searchingData.data[condition])
            }
          })
        }
        if (item.conditionValueIdCompact.join(',') == compactId.join(',')) {
          return item
        }
      }
    }
    return priceDetail
    /* tìm ra danh sách có ngày  */
  }

  public async copy(user: UserDto, data: any) {
    delete data.id
    /* kiểm tra xem template có tồn tại không ? */
    const lstTemplateId = data.shipmentPlanNumberDetails.map((item: any) => item.templateId)
    // lọc trùng lặp
    const uniqueTemplateId = new Set(lstTemplateId)
    // chuyển về mảng
    const lstTemplateIdFiltered = Array.from(uniqueTemplateId)
    if (lstTemplateId.length > 0) {
      const templateList = await this.shipmentConfigTemplateRepository.find({
        where: { id: In(lstTemplateIdFiltered), isDeleted: false },
      })
      if (templateList.length !== lstTemplateIdFiltered.length) {
        throw new NotFoundException(ERROR_NOT_FOUND_DATA)
      }
    }
    let targetShipmentPlan = new ShipmentPlanEntity()
    let lstPlanNumberDetailId = []
    let lstPlanNumberId = []

    if (data.id) {
      targetShipmentPlan = await this.shipmentPlanRepository.findOne({
        where: { id: data.id, isDeleted: false },
      })
      /* tìm ra danh sách planNumber dựa vào targetShipmentPlan.id  */
      const lstPlanNumber = await this.shipmentPlanNumberRepository.find({
        where: { shipmentPlanId: targetShipmentPlan.id, isDeleted: false },
        relations: { shipmentPlanNumberDetails: true },
      })
      /* map ra danh sáchId của lstPlanNumber */
      lstPlanNumberId = lstPlanNumber.map((item) => item.id)
      /* tìm ra danh sách planNumberDetail dựa vào lstPlanNumberId */
      const lstPlanNumberDetail = await this.shipmentPlanNumberDetailRepository.find({
        where: { shipmentPlanNumberId: In(lstPlanNumberId), isDeleted: false },
      })
      /* map ra danh sách Id */
      lstPlanNumberDetailId = lstPlanNumberDetail.map((item) => item.id)
      /* xóa lstPlanNumberDetailId rồi xóa lstPlanNumberId */
    }

    // tạo transaction
    await this.shipmentPlanRepository.manager.transaction(async (manager) => {
      const shipmentPlanRepository = manager.getRepository(ShipmentPlanEntity)
      const shipmentPlanNumberRepository = manager.getRepository(ShipmentPlanNumberEntity)
      const shipmentPlanNumberDetailRepository = manager.getRepository(ShipmentPlanNumberDetailEntity)

      if (data.id) {
        if (lstPlanNumberDetailId.length > 0) {
          await shipmentPlanNumberDetailRepository.delete({ id: In(lstPlanNumberDetailId) })
        }
        if (lstPlanNumberId.length > 0) {
          await shipmentPlanNumberRepository.delete({ id: In(lstPlanNumberId) })
        }
      }
      /* sau đó tạo lại như bình thưởng */
      targetShipmentPlan.name = data.name + ' - Copy'
      targetShipmentPlan.description = data.description || ''
      targetShipmentPlan.status = enumData.ShipmentPlanStatus.NEW.code
      targetShipmentPlan.createdBy = data.createdBy || user.id
      targetShipmentPlan.companyId = user.companyId
      targetShipmentPlan.createdAt = data.createdAt ? new Date(data.createdAt) : new Date()
      // lưu lại record
      targetShipmentPlan = await shipmentPlanRepository.save(targetShipmentPlan)
      const lstShipmentPlanNumberToSave = []
      const lstShipmentPlanNumberDetailToSave = []
      for (const item of data.shipmentPlanNumberDetails) {
        const shipmentPlanNumber = new ShipmentPlanNumberEntity()
        shipmentPlanNumber.id = uuidv4()
        shipmentPlanNumber.shipmentPlanId = targetShipmentPlan.id
        shipmentPlanNumber.title = item.title
        shipmentPlanNumber.totalValue = item.totalValue
        shipmentPlanNumber.deliveryDate = new Date(item.deliveryDate)
        shipmentPlanNumber.shipmentPlanNumberType = item.shipmentPlanNumberType || enumData.ShipmentPlanNumberType.Manual.code
        shipmentPlanNumber.shipmentConfigTemplateId = item.templateId
        /* lưu lại config của bảng */
        shipmentPlanNumber.shipmentPlanNumberConfigTable = item.tableConfig

        // sort lại giá trị của item.dataHeaderTemplate theo code
        // item.dataHeaderTemplate.sort((a: any, b: any) => {
        //   return a.code.localeCompare(b.code)
        // })
        /* map ra danh sách giá trị của compact code và id */
        shipmentPlanNumber.shipmentFeeConditionTypeCompactId = item.dataHeaderTemplate.map((item: any) => item.id) || []
        shipmentPlanNumber.shipmentFeeConditionTypeCompactCode = item.dataHeaderTemplate.map((item: any) => item.code) || []
        // map ra danh sách giá trị
        const lstCompactValueId = []
        item.dataHeaderTemplate.forEach((element: any) => {
          /* tạo mới detail  */
          const shipmentPlanNumberDetail = new ShipmentPlanNumberDetailEntity()
          shipmentPlanNumberDetail.id = uuidv4()
          shipmentPlanNumberDetail.shipmentPlanNumberId = shipmentPlanNumber.id
          shipmentPlanNumberDetail.shipmentFeeConditionId = element.id
          if (item.data[element.code]) {
            lstCompactValueId.push(item.data[element.code])
            shipmentPlanNumberDetail.idValue = item.data[element.code]
          } else {
            lstCompactValueId.push(null)
          }
          lstShipmentPlanNumberDetailToSave.push(shipmentPlanNumberDetail)
        })
        shipmentPlanNumber.shipmentFeeConditionTypeCompactValue = lstCompactValueId
        lstShipmentPlanNumberToSave.push(shipmentPlanNumber)
      }
      /* lưu lại */
      if (lstShipmentPlanNumberToSave.length > 0) await shipmentPlanNumberRepository.save(lstShipmentPlanNumberToSave)
      /* lưu lại danh sách detail */
      if (lstShipmentPlanNumberDetailToSave.length > 0) await shipmentPlanNumberDetailRepository.save(lstShipmentPlanNumberDetailToSave)

      // tạo ra danh sách shipmentPlanNumber
      await this.flowService.setRoleRule(
        user,
        {
          targetId: targetShipmentPlan.id,
          target: targetShipmentPlan,
          entityName: ShipmentPlanEntity.name,
          flowType: enumData.FlowCode.SHIPMENT_PLAN.code,
          type: enumData.FlowAppoveType.None.code,
        },
        true,
        manager,
      )
    })
    /* tạo luồng duyệt */

    return {
      id: targetShipmentPlan.id,
      message: CREATE_SUCCESS,
    }
  }
  public async save(user: UserDto, data: any) {
    /* kiểm tra xem template có tồn tại không ? */
    const lstTemplateId = data.shipmentPlanNumberDetails.map((item: any) => item.templateId)
    // lọc trùng lặp
    const uniqueTemplateId = new Set(lstTemplateId)
    // chuyển về mảng
    const lstTemplateIdFiltered = Array.from(uniqueTemplateId)
    if (lstTemplateId.length > 0) {
      const templateList = await this.shipmentConfigTemplateRepository.find({
        where: { id: In(lstTemplateIdFiltered), isDeleted: false },
      })
      if (templateList.length !== lstTemplateIdFiltered.length) {
        throw new NotFoundException(ERROR_NOT_FOUND_DATA)
      }
    }
    let targetShipmentPlan = new ShipmentPlanEntity()
    let lstPlanNumberDetailId = []
    let lstPlanNumberId = []

    if (data.id) {
      targetShipmentPlan = await this.shipmentPlanRepository.findOne({
        where: { id: data.id, isDeleted: false },
      })
      /* tìm ra danh sách planNumber dựa vào targetShipmentPlan.id  */
      const lstPlanNumber = await this.shipmentPlanNumberRepository.find({
        where: { shipmentPlanId: targetShipmentPlan.id, isDeleted: false },
        relations: { shipmentPlanNumberDetails: true },
      })
      /* map ra danh sáchId của lstPlanNumber */
      lstPlanNumberId = lstPlanNumber.map((item) => item.id)
      /* tìm ra danh sách planNumberDetail dựa vào lstPlanNumberId */
      const lstPlanNumberDetail = await this.shipmentPlanNumberDetailRepository.find({
        where: { shipmentPlanNumberId: In(lstPlanNumberId), isDeleted: false },
      })
      /* map ra danh sách Id */
      lstPlanNumberDetailId = lstPlanNumberDetail.map((item) => item.id)
      /* xóa lstPlanNumberDetailId rồi xóa lstPlanNumberId */
    }

    // tạo transaction
    await this.shipmentPlanRepository.manager.transaction(async (manager) => {
      const shipmentPlanRepository = manager.getRepository(ShipmentPlanEntity)
      const shipmentPlanNumberRepository = manager.getRepository(ShipmentPlanNumberEntity)
      const shipmentPlanNumberDetailRepository = manager.getRepository(ShipmentPlanNumberDetailEntity)

      if (data.id) {
        if (lstPlanNumberDetailId.length > 0) {
          await shipmentPlanNumberDetailRepository.delete({ id: In(lstPlanNumberDetailId) })
        }
        if (lstPlanNumberId.length > 0) {
          await shipmentPlanNumberRepository.delete({ id: In(lstPlanNumberId) })
        }
      }
      /* sau đó tạo lại như bình thưởng */
      targetShipmentPlan.name = data.name
      targetShipmentPlan.description = data.description || ''
      targetShipmentPlan.status = enumData.ShipmentPlanStatus.NEW.code
      targetShipmentPlan.createdBy = data.createdBy || user.id
      targetShipmentPlan.companyId = user.companyId
      targetShipmentPlan.createdAt = data.createdAt ? new Date(data.createdAt) : new Date()
      // lưu lại record
      targetShipmentPlan = await shipmentPlanRepository.save(targetShipmentPlan)
      const lstShipmentPlanNumberToSave = []
      const lstShipmentPlanNumberDetailToSave = []
      for (const item of data.shipmentPlanNumberDetails) {
        const shipmentPlanNumber = new ShipmentPlanNumberEntity()
        shipmentPlanNumber.id = uuidv4()
        shipmentPlanNumber.shipmentPlanId = targetShipmentPlan.id
        shipmentPlanNumber.title = item.title
        shipmentPlanNumber.totalValue = item.totalValue
        shipmentPlanNumber.uomIdQuantity = item?.uomIdQuantity
        shipmentPlanNumber.uomIdWeight = item?.uomIdWeight
        shipmentPlanNumber.uomIdVolume = item?.uomIdVolume
        shipmentPlanNumber.quantity = item?.quantity || 0
        shipmentPlanNumber.weight = item?.weight || 0
        shipmentPlanNumber.volume = item?.volume || 0
        shipmentPlanNumber.currencyId = item.currencyId || null
        shipmentPlanNumber.incotermId = item.incotermId || null
        shipmentPlanNumber.deliveryDate = new Date(item.deliveryDate)
        shipmentPlanNumber.shipmentPlanNumberType = item.shipmentPlanNumberType || enumData.ShipmentPlanNumberType.Manual.code
        shipmentPlanNumber.shipmentConfigTemplateId = item.templateId
        /* lưu lại config của bảng */
        shipmentPlanNumber.shipmentPlanNumberConfigTable = item.tableConfig
        shipmentPlanNumber.shipmentCostStreamConfigTable = item.lstCostConfigShipment

        // sort lại giá trị của item.dataHeaderTemplate theo code
        item.dataHeaderTemplate.sort((a: any, b: any) => {
          return a.code.localeCompare(b.code)
        })
        /* map ra danh sách giá trị của compact code và id */
        shipmentPlanNumber.shipmentFeeConditionTypeCompactId = item.dataHeaderTemplate.map((item: any) => item.id) || []
        shipmentPlanNumber.shipmentFeeConditionTypeCompactCode = item.dataHeaderTemplate.map((item: any) => item.code) || []
        // map ra danh sách giá trị
        const lstCompactValueId = []
        item.dataHeaderTemplate.forEach((element: any) => {
          /* tạo mới detail  */
          const shipmentPlanNumberDetail = new ShipmentPlanNumberDetailEntity()
          shipmentPlanNumberDetail.id = uuidv4()
          shipmentPlanNumberDetail.shipmentPlanNumberId = shipmentPlanNumber.id
          shipmentPlanNumberDetail.shipmentFeeConditionId = element.id
          if (item.data[element.code]) {
            lstCompactValueId.push(item.data[element.code])
            shipmentPlanNumberDetail.idValue = item.data[element.code]
          } else {
            lstCompactValueId.push(null)
          }
          lstShipmentPlanNumberDetailToSave.push(shipmentPlanNumberDetail)
        })
        shipmentPlanNumber.shipmentFeeConditionTypeCompactValue = lstCompactValueId
        lstShipmentPlanNumberToSave.push(shipmentPlanNumber)
      }
      /* lưu lại */
      if (lstShipmentPlanNumberToSave.length > 0) await shipmentPlanNumberRepository.save(lstShipmentPlanNumberToSave)
      /* lưu lại danh sách detail */
      if (lstShipmentPlanNumberDetailToSave.length > 0) await shipmentPlanNumberDetailRepository.save(lstShipmentPlanNumberDetailToSave)

      // tạo ra danh sách shipmentPlanNumber
      await this.flowService.setRoleRule(
        user,
        {
          targetId: targetShipmentPlan.id,
          target: targetShipmentPlan,
          entityName: ShipmentPlanEntity.name,
          flowType: enumData.FlowCode.SHIPMENT_PLAN.code,
          type: enumData.FlowAppoveType.None.code,
        },
        true,
        manager,
      )
    })
    /* tạo luồng duyệt */

    return {
      id: targetShipmentPlan.id,
      message: CREATE_SUCCESS,
    }
  }

  public async pagination(data: PaginationDto, user: UserDto) {
    const whereCon: any = {
      isDeleted: false,
    }
    const res: any[] = await this.shipmentPlanRepository.findAndCount({
      where: whereCon,
      select: {
        id: true,
        name: true,
        description: true,
        status: true,
        createdAt: true,
        createdBy: true,
        shipmentPlanNumbers: { id: true, shipmentConfigTemplateId: true },
      },
      skip: data.skip,
      take: data.take,
      relations: { shipmentPlanNumbers: true },
      order: { createdAt: 'DESC' },
    })
    // if (res[0].length == 0) return res
    /* lấy ra danh sách id của shipmentPlanNumber */
    const lstShipmentPlanNumberId = []
    res[0].forEach((item) => {
      if (item.__shipmentPlanNumbers__ && item.__shipmentPlanNumbers__.length > 0) {
        item.__shipmentPlanNumbers__.forEach((number: any) => {
          lstShipmentPlanNumberId.push(number.shipmentConfigTemplateId)
        })
      }
    })
    //  tìm chi tiết của template
    const lstShipmentPlanNumberDetail = await this.shipmentConfigTemplateRepository.find({
      select: { id: true, name: true },
      where: { id: In(lstShipmentPlanNumberId) },
    })
    /* map lại dữ liệu */
    const dicTemplateName = {}
    lstShipmentPlanNumberDetail.forEach((item) => {
      dicTemplateName[item.id] = item.name
    })
    /* map ra lấy id của createdBy */
    const dicCreatedBy = {}
    const lstCreatedBy = res[0].map((item) => item.createdBy)
    const lstUser = await this.shipmentPlanRepository.manager.getRepository(EmployeeEntity).find({
      where: { id: In(lstCreatedBy), isDeleted: false },
      select: { id: true, name: true },
    })
    lstUser.forEach((item) => {
      dicCreatedBy[item.id] = item.name
    })
    for (const item of res[0]) {
      item.templateName = []
      item.createdByName = dicCreatedBy[item.createdBy] || ''
      let numberCount = 1
      const dicContain = {}
      item.__shipmentPlanNumbers__.forEach((number: any) => {
        if (!dicContain[dicTemplateName[number.shipmentConfigTemplateId]]) {
          if (dicTemplateName[number.shipmentConfigTemplateId]) item.templateName.push(dicTemplateName[number.shipmentConfigTemplateId])
        }

        dicContain[dicTemplateName[number.shipmentConfigTemplateId]] = true
      })
      if (item.templateName.length > 0) item.templateName = item.templateName.join(', ')
      item.shipmentPlanNumberLength = item.__shipmentPlanNumbers__.length
      item.canShowProgress = item.status == enumData.ShipmentPlanStatus.IN_PROGRESS.code
      item.isCanSendApprove = item.status === enumData.ShipmentPriceStatus.NEW.code
      item.isCanShowEdit = item.status === enumData.ShipmentPlanStatus.NEW.code
      item.inApprover = item.status === enumData.ShipmentPriceStatus.IN_PROGRESS.code
      item.isApproved = item.status === enumData.ShipmentPriceStatus.APPROVED.code

      item.lstApprovalProgress = await this.flowService.getListApproveDetail(user, {
        targetId: item.id,
        entityName: ShipmentPlanEntity.name,
        type: enumData.FlowCode.SHIPMENT_PLAN.code,
      })

      item.objPermissionApprove = await this.flowService.checkCanApproveByType(user, {
        lsType: [enumData.FlowCode.SHIPMENT_PLAN.code],
        entityName: ShipmentPlanEntity.name,
        targetId: item.id,
      })
      item.isCanApprove = Object.values(item.objPermissionApprove).some((value) => value)

      const check = await this.flowService.checkCanRemoveApprove(user, {
        targetId: item.id,
        entityName: ShipmentPlanEntity.name,
        type: enumData.FlowCode.SHIPMENT_PLAN.code,
      })
      item.isRevert = check.canRevert && item.status == enumData.ShipmentPlanStatus.IN_PROGRESS.code
      item.revertReject = item.status == enumData.ShipmentPlanStatus.CANCEL.code
      item.statusName = enumData.ShipmentPlanStatus[item.status]?.name
      item.statusBorderColor = enumData.ShipmentPlanStatus[item.status]?.statusBorderColor
      item.statusColor = enumData.ShipmentPlanStatus[item.status]?.statusColor
      item.statusBgColor = enumData.ShipmentPlanStatus[item.status]?.statusBgColor
      const { canApprove, approvalProgress } = await this.flowService.getRoleApprove(user, {
        targetId: item.id,
        entityName: ShipmentPlanEntity.name,
      })
      Object.assign(item, { approvalProgress, canApprove })
    }

    return res
  }

  public async updateRemoveRule2(user: UserDto, data: FilterOneDto) {
    const entity = await this.shipmentPlanRepository.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    const rejectStatus = await this.flowService.revertApprove(user, {
      targetId: data.id,
      entityName: ShipmentPlanEntity.name,
      type: enumData.FlowCode.SHIPMENT_PLAN.code,
    })
    if (rejectStatus.isFist) {
      entity.status = enumData.ShipmentStatus.NEW.code
      entity.updatedBy = user.id
      entity.updatedAt = new Date()
      await this.shipmentPlanRepository.save(entity)
    }
    return { message: UPDATE_SUCCESS }
  }

  /* hàm lấy chi tiết */
  public async detail(data: FilterOneDto, user: UserDto): Promise<any> {
    /* kiểm tra xem tồn tại không */
    const targetShipmentPlan: any = await this.shipmentPlanRepository.findOne({
      where: { id: data.id, isDeleted: false },
      relations: { shipmentPlanNumbers: { shipmentPlanNumberDetails: true } },
    })
    /*  */
    if (!targetShipmentPlan) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    targetShipmentPlan.lstApprovalProgress = await this.flowService.getListApproveDetail(user, {
      targetId: targetShipmentPlan.id,
      entityName: ShipmentPlanEntity.name,
      type: enumData.FlowCode.SHIPMENT_PLAN.code,
    })
    if (!targetShipmentPlan) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    /* mapping data */
    targetShipmentPlan.shipmentPlanNumberDetails = targetShipmentPlan.__shipmentPlanNumbers__
    // sort theo createdAt
    targetShipmentPlan.shipmentPlanNumberDetails.sort((a: any, b: any) => {
      return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    })
    delete targetShipmentPlan.__shipmentPlanNumbers__
    /* Sort */
    targetShipmentPlan.shipmentPlanNumberDetails.sort((a, b) => {
      const getNumber = (title: string) => {
        const match = title.match(/\d+/)
        return match ? parseInt(match[0], 10) : 0
      }

      return getNumber(a.title) - getNumber(b.title)
    })
    // check xem nhân viên có quyền thao tác không ? duyệt/gỡ duyệt/từ chối duyệt/gỡ từ chối duyệt
    {
      targetShipmentPlan.isNew = targetShipmentPlan.status === enumData.ShipmentPriceStatus.NEW.code
      targetShipmentPlan.isInprogress = targetShipmentPlan.status === enumData.ShipmentPriceStatus.IN_PROGRESS.code
      targetShipmentPlan.isCancel = targetShipmentPlan.status === enumData.ShipmentPriceStatus.CANCEL.code
      targetShipmentPlan.isisDone = targetShipmentPlan.status === enumData.ShipmentPriceStatus.APPROVED.code

      targetShipmentPlan.objPermissionApprove = await this.flowService.checkCanApproveByType(user, {
        lsType: [enumData.FlowCode.SHIPMENT_PLAN.code],
        entityName: ShipmentPlanEntity.name,
        targetId: targetShipmentPlan.id,
      })

      targetShipmentPlan.lstApprovalProgress = await this.flowService.getListApproveDetail(user, {
        targetId: targetShipmentPlan.id,
        entityName: ShipmentPlanEntity.name,
        type: enumData.FlowCode.SHIPMENT_PLAN.code,
      })

      targetShipmentPlan.isCanApprove = Object.values(targetShipmentPlan.objPermissionApprove).some((value) => value)

      const check = await this.flowService.checkCanRemoveApprove(user, {
        targetId: targetShipmentPlan.id,
        entityName: ShipmentPlanEntity.name,
        type: enumData.FlowCode.SHIPMENT_PLAN.code,
      })
      targetShipmentPlan.isRevert = check.canRevert && targetShipmentPlan.status == enumData.ShipmentPlanStatus.IN_PROGRESS.code
      targetShipmentPlan.revertReject = targetShipmentPlan.status == enumData.ShipmentPlanStatus.CANCEL.code
    }
    /* hàm lấy ra danh sách cấp duyệt của data hiện tại */
    {
      const lstApprove = await this.flowService.getListApproveDetail(user, {
        targetId: targetShipmentPlan.id,
        entityName: ShipmentPlanEntity.name,
        type: enumData.FlowCode.SHIPMENT_PLAN.code,
      })
      targetShipmentPlan.lstApprove = lstApprove
      targetShipmentPlan.shipmentPlanNumberDetails.forEach((item: any) => {
        // for i trong lstApprove
        let step = 0
        for (const approveStep of targetShipmentPlan.lstApprove) {
          let employeeStep = 0
          for (const approveDetail of approveStep.lstApprove) {
            if (approveDetail.acceptedValueId === item.id) {
              item['isCheck' + step + employeeStep] = true
            } else {
              item['isCheck' + step + employeeStep] = false
            }
            employeeStep++
          }
          step++
        }
      })
      // for()
    }
    /* lấy ra danh sách id của shipmentPlanNumber */
    for (const item of targetShipmentPlan.shipmentPlanNumberDetails) {
      if (item.tableConfig) item.detailTemplate = item.tableConfig.detailTemplate
      /* tìm ra danh sách dựa theo  */
      item.dataHeaderTemplate = await this.findList(item.shipmentFeeConditionTypeCompactId)
      const dicNumberDetails: any = {}
      item.dataHeaderTemplate.forEach((element) => {
        dicNumberDetails[element.id] = element
      })

      item.data = {}
      item.__shipmentPlanNumberDetails__.forEach((detail: any) => {
        if (dicNumberDetails[detail.shipmentFeeConditionId] && dicNumberDetails[detail.shipmentFeeConditionId].code && detail.idValue) {
          item.data[dicNumberDetails[detail.shipmentFeeConditionId].code] = detail.idValue
        }
      })
    }
    return targetShipmentPlan
  }

  public async sendApprove(user: UserDto, data: FilterOneDto) {
    await this.shipmentPlanRepository.manager.transaction(async (transac) => {
      const repo = transac.getRepository(ShipmentPlanEntity)
      const entity = await repo.findOne({ where: { id: data.id, isDeleted: false } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
      // Nếu chưa phải lần duyệt cuối
      entity.status = enumData.ShipmentPriceStatus.IN_PROGRESS.code
      await repo.save(entity)
    })
    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  public async updateApproved(user: UserDto, data: { id: string; acceptedValueId: string }) {
    const approveStatus = await this.flowService.approveRule(user, {
      targetId: data.id,
      entityName: ShipmentPlanEntity.name,
      acceptedValueId: data.acceptedValueId,
    })
    await this.shipmentPlanRepository.manager.transaction(async (transac) => {
      const repo = transac.getRepository(ShipmentPlanEntity)

      const entity = await repo.findOne({ where: { id: data.id, isDeleted: false } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

      // Nếu chưa phải lần duyệt cuối
      if (approveStatus.status === enumData.APPROVE_TYPE.NOT_DONE.code) {
        entity.status = enumData.ShipmentPriceStatus.IN_PROGRESS.code
        await repo.save(entity)
        return { message: `Đã duyệt thành công, Vui lòng chờ cấp sau duyệt` }
      }
      entity.status = enumData.ShipmentPriceStatus.APPROVED.code
      entity.acceptedValueId = data.acceptedValueId
      await repo.save(entity)
    })
    return { message: UPDATE_ACTIVE_SUCCESS }
  }
  // hàm tìm danh sách theo lstId
  public async findList(lstId: string[]): Promise<any[]> {
    const lstShipmentFeeCondition: any = await this.shipmentFeeConditionsRepository.find({
      where: { id: In(lstId), isDeleted: false },
      relations: { shipmentFeeConditionsToList: { shipmentFeeConditionsList: true } },
      order: { code: 'ASC' },
    })
    let lstSupplier: any = []
    /* nếu như trong list có isRelatedToSupplier  thì tìm danh sách supplier*/
    const isHaveSupplier = lstShipmentFeeCondition.some((item) => item.isRelatedToSupplier)
    if (isHaveSupplier) {
      /* load danh sách nhà cung cấp */
      lstSupplier = await this.supplierRepository.find({
        select: { id: true, code: true, name: true },
        where: { isDeleted: false },
        order: { code: 'ASC' },
      })
    }
    for (const item of lstShipmentFeeCondition) {
      item.selectionBox = []
      if (item.__shipmentFeeConditionsToList__ && item.__shipmentFeeConditionsToList__.length > 0 && !item.isRelatedToSupplier)
        item.__shipmentFeeConditionsToList__.forEach((condition) => {
          if (condition.__shipmentFeeConditionsList__) {
            const shipmentFeeConditionsListDetail = condition.__shipmentFeeConditionsList__
            item.selectionBox.push({ id: shipmentFeeConditionsListDetail.id, name: shipmentFeeConditionsListDetail.name })
          }
        })
      if (item.__shipmentFeeConditionsToList__ && item.__shipmentFeeConditionsToList__.length > 0 && item.isRelatedToSupplier) {
        item.selectionBox = lstSupplier
      }
    }
    return lstShipmentFeeCondition
  }

  /* hàm tìm ra giác của condition type theo param truyền vào */
  public async loadloadTemplateDetail(user: UserDto, filterOne: string, searchingData: any): Promise<any> {
    // check trong searchingData.data, properties nào bằng null hoặc undfinend thì xóa đi
    if (searchingData && searchingData.data) {
      Object.keys(searchingData.data).forEach((key) => {
        if (searchingData.data[key] === null || searchingData.data[key] === undefined) {
          delete searchingData.data[key]
        }
      })
    }
    const dataDetail: any = await this.shipmentConfigTemplateRepository.findOne({
      where: { id: filterOne, isDeleted: false },
      relations: { shipmentConfigTemplateDetails: { shipmentConditionType: true } },
    })
    if (!dataDetail) throw new NotFoundException(ERROR_NOT_FOUND_DATA)
    const lstTemplateDetail = dataDetail.__shipmentConfigTemplateDetails__
    for (const item of lstTemplateDetail) {
      /* mapping giá */
      const valueData = await this.loadMappingData(item.shipmentConditionTypeId, searchingData)
      if (valueData) {
        item.price = +valueData.price
        item.value = +valueData.price
        item.valueData = valueData
        item.shipmentConditionTypeTemplateId = valueData.id
      }
      item.shipmentConditionTypeName = item.__shipmentConditionType__.name || ''
      item.shipmentConditionTypeCode = item.__shipmentConditionType__.code || ''
      delete item.__shipmentConditionType__
    }
    return lstTemplateDetail
  }

  public async updateRevertStatus(user: UserDto, data: any) {
    const entity = await this.shipmentPlanRepository.findOne({ where: { id: data.id } })
    await this.flowService.rejectRejectRulePR(user, { targetId: data.id, entityName: ShipmentPlanEntity.name, level: data.level })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (entity.status !== enumData.ShipmentPlanStatus.CANCEL.code) {
      throw new Error('Trạng thái không phù hợp. Vui lòng kiểm tra lại')
    }
    entity.status = enumData.ShipmentPlanStatus.IN_PROGRESS.code
    entity.updatedBy = user.id
    entity.updatedAt = new Date()
    await this.shipmentPlanRepository.save(entity)
    return { message: UPDATE_SUCCESS }
  }

  public async updateRejectRule(user: UserDto, data: any) {
    const entity = await this.shipmentPlanRepository.findOne({ where: { id: data.id } })
    await this.flowService.rejectRulePR(user, { targetId: data.id, entityName: ShipmentPlanEntity.name, level: data.level })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (entity.status !== enumData.ShipmentPlanStatus.IN_PROGRESS.code) {
      throw new Error('Trạng thái không phù hợp. Vui lòng kiểm tra lại')
    }
    entity.status = enumData.ShipmentPlanStatus.CANCEL.code
    entity.updatedBy = user.id
    entity.updatedAt = new Date()
    await this.shipmentPlanRepository.save(entity)
    return { message: UPDATE_SUCCESS }
  }

  public async deletePlan(user: UserDto, data: FilterOneDto) {
    await this.shipmentPlanRepository.update({ id: data.id }, { isDeleted: true })
    return { message: DELETE_SUCCESS }
  }
}
