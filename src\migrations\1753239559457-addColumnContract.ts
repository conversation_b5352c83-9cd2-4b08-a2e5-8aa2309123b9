import { MigrationInterface, QueryRunner } from "typeorm";

export class AddColumnContract1753239559457 implements MigrationInterface {
    name = 'AddColumnContract1753239559457'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "contract" ADD "authorizedLocationEN" nvarchar(450)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "contract" DROP COLUMN "authorizedLocationEN"`);
    }

}
