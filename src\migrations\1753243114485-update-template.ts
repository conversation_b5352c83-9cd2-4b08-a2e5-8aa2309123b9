import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateTemplate1753243114485 implements MigrationInterface {
  name = 'UpdateTemplate1753243114485'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_plan" ADD "title" varchar(255)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_plan" DROP COLUMN "title"`)
  }
}
