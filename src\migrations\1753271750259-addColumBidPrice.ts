import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddColumBidPrice1753271750259 implements MigrationInterface {
  name = 'AddColumBidPrice1753271750259'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "bid_price" ALTER COLUMN "number" int`)
    await queryRunner.query(`ALTER TABLE "bid_price" ALTER COLUMN "sort" int`)
    await queryRunner.query(`ALTER TABLE "bid_price" ALTER COLUMN "name" varchar(250)`)
    await queryRunner.query(`ALTER TABLE "bid_price" ALTER COLUMN "isRequired" bit`)
    await queryRunner.query(`ALTER TABLE "bid_price" ALTER COLUMN "isSetup" bit`)
    await queryRunner.query(`ALTER TABLE "bid_price" ALTER COLUMN "isTemplate" bit`)
    await queryRunner.query(`ALTER TABLE "bid_price" ALTER COLUMN "type" nvarchar(255)`)
    await queryRunner.query(`ALTER TABLE "bid_price" ALTER COLUMN "level" int`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "bid_price" ALTER COLUMN "level" int NOT NULL`)
    await queryRunner.query(`ALTER TABLE "bid_price" ALTER COLUMN "type" nvarchar(255) NOT NULL`)
    await queryRunner.query(`ALTER TABLE "bid_price" ALTER COLUMN "isTemplate" bit NOT NULL`)
    await queryRunner.query(`ALTER TABLE "bid_price" ALTER COLUMN "isSetup" bit NOT NULL`)
    await queryRunner.query(`ALTER TABLE "bid_price" ALTER COLUMN "isRequired" bit NOT NULL`)
    await queryRunner.query(`ALTER TABLE "bid_price" ALTER COLUMN "name" varchar(250) NOT NULL`)
    await queryRunner.query(`ALTER TABLE "bid_price" ALTER COLUMN "sort" int NOT NULL`)
    await queryRunner.query(`ALTER TABLE "bid_price" ALTER COLUMN "number" int NOT NULL`)
  }
}
