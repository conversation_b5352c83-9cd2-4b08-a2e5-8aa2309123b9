import { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateColum1753270124111 implements MigrationInterface {
  name = 'CreateColum1753270124111'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "bid" ADD "reference" varchar(500)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "bid" DROP COLUMN "reference"`)
  }
}
