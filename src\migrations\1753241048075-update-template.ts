import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateTemplate1753241048075 implements MigrationInterface {
  name = 'UpdateTemplate1753241048075'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_plan" ADD "paymentTermId" uniqueidentifier`)
    await queryRunner.query(
      `ALTER TABLE "business_template_plan" ADD CONSTRAINT "FK_a4bb8a76b350bf3e5b74c1661fd" FOREIGN KEY ("paymentTermId") REFERENCES "payment_term"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_plan" DROP CONSTRAINT "FK_a4bb8a76b350bf3e5b74c1661fd"`)
    await queryRunner.query(`ALTER TABLE "business_template_plan" DROP COLUMN "paymentTermId"`)
  }
}
