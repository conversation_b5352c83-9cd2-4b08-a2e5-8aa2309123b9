import { Injectable } from '@nestjs/common'
import {
  BusinessTemplateGroupPlanRepository,
  BusinessTemplatePlanRepository,
  BusinessTemplatePlanSetupRepository,
  BusinessTemplatePlanTypeRepository,
} from '../../repositories/businessTemplatePlan.repository'
import { BusinessTemplatePlanCreateTypeDto } from './dto/BusinessTemplatePlanCreateType.dto'
import { BusinessTemplatePlanSaveDto } from './dto/BusinessTemplatePlanSave.dto'
import { BusinessTemplatePlanUpdateDto } from './dto/BusinessTemplatePlanUpdate.dto'
import { BusinessTemplatePlanDetailResponseDto } from './dto/BusinessTemplatePlanDetailResponse.dto'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import { RfqService } from '../rfq/rfq.service'
import { Between, In, Like } from 'typeorm'
import { BusinessTemplatePlanEntity } from '../../entities/businessTemplatePlan.entity'
import { enumData, ERROR_NOT_FOUND_DATA, UPDATE_ACTIVE_SUCCESS, UPDATE_SUCCESS } from '../../constants'
import { FlowApproveService } from '../flowApprove/flowApprove.service'
import { CostEntity, EmployeeEntity, IncotermEntity, PurchasingOrgEntity } from '../../entities'
import { CostRepository } from '../../repositories'
import { BusinessTemplateGroupPlanEntity } from '../../entities/businessTemplateGroupPlan.entity'
import { v4 as uuidv4 } from 'uuid'
import { BusinessPlanTemplateCostListEntity } from '../../entities/businessTemplatePlanCostList.entity'
import { BusinessTemplatePlanExchangeEntity } from '../../entities/businessTemplatePlanExchange.entity'
import { BusinessTemplatePlanCostEntity } from '../../entities/businessTemplatePlanCost.entity'
import moment from 'moment'
import { ShipmentPlanNumberEntity } from '../../entities/shipmentPlanNumber.entity'
import { ShipmentPlanEntity } from '../../entities/shipmentPlan.entity'

@Injectable()
export class BusinessTemplatePlanService {
  constructor(
    private businessTemplateGroupRepository: BusinessTemplatePlanRepository,
    private businessTemplateGroupPlanRepository: BusinessTemplateGroupPlanRepository,
    private businessTemplatePlanSetupRepository: BusinessTemplatePlanSetupRepository,
    private costRepository: CostRepository,
    private businessTemplatePlanTypeRepository: BusinessTemplatePlanTypeRepository,
    private rfqService: RfqService, // Assuming RfqService is injected for RFQ related operations
    private readonly flowService: FlowApproveService,
  ) {}

  async getTypeConfig(user: UserDto, data: { id: string }) {
    const type = await this.businessTemplatePlanTypeRepository.findOne({ where: { id: data.id } })
    if (!type) throw new Error(ERROR_NOT_FOUND_DATA)
    return type
  }
  async createType(user: UserDto, data: any) {
    // code tự gen
    if (data.id) {
      const type = await this.businessTemplatePlanTypeRepository.findOne({ where: { id: data.id } })
      if (!type) throw new Error(ERROR_NOT_FOUND_DATA)
      return await this.businessTemplatePlanTypeRepository.update(data.id, {
        name: data.name,
        status: data.status,
        description: data.description,
        configTable: data.configTable,
        updatedBy: user.employeeId,
        updatedAt: new Date(),
      })
    }
    const code = await this.businessTemplatePlanTypeRepository.genCode()
    const newType = this.businessTemplatePlanTypeRepository.create({
      code: code,
      name: data.name,
      status: data.status,
      description: data.description,
      configTable: data.configTable,
      createdBy: user.employeeId,
    })
    return await this.businessTemplatePlanTypeRepository.save(newType)
  }
  // hàm ngưng hoạt động
  async deleteType(user: UserDto, data: FilterOneDto) {
    const type = await this.businessTemplatePlanTypeRepository.findOne({ where: { id: data.id } })
    if (!type) throw new Error(ERROR_NOT_FOUND_DATA)
    type.isDeleted = true
    type.updatedBy = user.employeeId
    type.updatedAt = new Date()
    return await this.businessTemplatePlanTypeRepository.save(type)
  }
  async updateType(user: UserDto, data: any) {
    const type = await this.businessTemplatePlanTypeRepository.findOne({ where: { id: data.id } })
    if (!type) throw new Error(ERROR_NOT_FOUND_DATA)
    type.name = data.name
    type.status = data.status
    type.description = data.description
    type.configTable = data.configTable
    type.updatedBy = user.employeeId
    type.updatedAt = new Date()
    return await this.businessTemplatePlanTypeRepository.save(type)
  }

  async getTypeList(user: UserDto) {
    const data = await this.businessTemplatePlanTypeRepository.find({
      select: { id: true, code: true, name: true },
      order: { createdAt: 'DESC' },
    })
    return data
  }

  async getRfqItem(
    user: UserDto,
    lstRfqId?: string[],
    lstMaterialId?: string[],
    lstMaterialGroupId?: string[],
    lstCollectiveId?: string[],
    createdAt?: string[],
  ) {
    return this.rfqService.getRfqItem(user, lstRfqId, lstMaterialId, lstMaterialGroupId, lstCollectiveId, createdAt)
  }

  async paginationTypeList(user: UserDto, data: any) {
    const whereCon: any = { isDeleted: false }
    if (data.where?.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where?.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted
    // tìm theo createdBy
    if (data.where?.employeeId) whereCon.createdBy = data.where.employeeId
    // tìm theo ngày tạo
    if (data.where?.createdAt && data.where.createdAt.length > 1) {
      const dateStart = new Date(data.where.createdAt[0])
      dateStart.setHours(0, 0, 0, 0)
      const dateEnd = new Date(data.where.createdAt[1])
      dateEnd.setHours(23, 59, 59, 999)
      whereCon.createdAt = Between(dateStart, dateEnd)
    }
    const [items, total]: any = await this.businessTemplatePlanTypeRepository.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
    })
    const dicCreatedBy = {}
    const lstCreatedBy = items.map((item) => item.createdBy)
    const lstUser = await this.businessTemplateGroupPlanRepository.manager.getRepository(EmployeeEntity).find({
      where: { id: In(lstCreatedBy), isDeleted: false },
      select: { id: true, name: true },
    })
    lstUser.forEach((item) => {
      dicCreatedBy[item.id] = item.name
    })
    for (const item of items) {
      item.createdByName = dicCreatedBy[item.createdBy] || ''
    }
    return [items, total]
  }

  public async getList(user: UserDto, data: PaginationDto) {
    const whereCon: any = {
      isDeleted: false,
    }
    const res: any[] = await this.businessTemplateGroupPlanRepository.find({
      where: whereCon,
      order: { createdAt: 'DESC' },
    })
    return res
  }

  public async pagination(data: PaginationDto, user: UserDto) {
    const whereCon: any = {
      isDeleted: false,
    }
    const res: any[] = await this.businessTemplateGroupPlanRepository.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      relations: {
        businessTemplatePlanType: true,
        businessTemplatePlan: true,
      },
      order: { createdAt: 'DESC' },
    })
    // if (res[0].length == 0) return res
    /* lấy ra danh sách id của shipmentPlanNumber */
    /* map lại dữ liệu */
    /* map ra lấy id của createdBy */
    const dicCreatedBy = {}
    const lstCreatedBy = res[0].map((item) => item.createdBy)
    const lstUser = await this.businessTemplateGroupPlanRepository.manager.getRepository(EmployeeEntity).find({
      where: { id: In(lstCreatedBy), isDeleted: false },
      select: { id: true, name: true },
    })
    lstUser.forEach((item) => {
      dicCreatedBy[item.id] = item.name
    })

    const dicPurOrg = {}
    const lstPurOrgId = res[0].map((item) => item.purchasingOrgId)
    const lstPurOrg = await this.businessTemplateGroupPlanRepository.manager.getRepository(PurchasingOrgEntity).find({
      where: { id: In(lstPurOrgId), isDeleted: false },
      select: { id: true, name: true },
    })
    lstPurOrg.forEach((item) => {
      dicPurOrg[item.id] = item.name
    })

    for (const item of res[0]) {
      item.templateName = []
      item.businessType = item?.__businessTemplatePlanType__?.name
      item.quantity = item?.__businessTemplatePlan__?.length || 0
      item.purOrg = dicPurOrg[item.purchasingOrgId] || ''
      item.createdByName = dicCreatedBy[item.createdBy] || ''
      item.canShowProgress = item.status == enumData.BusinessTemplatePlanStatus.W_A.code
      item.isCanSendApprove = item.status === enumData.BusinessTemplatePlanStatus.N.code
      item.isCanShowEdit = item.status === enumData.BusinessTemplatePlanStatus.N.code
      item.inApprover = item.status === enumData.BusinessTemplatePlanStatus.W_A.code
      item.isApproved = item.status === enumData.BusinessTemplatePlanStatus.A.code
      {
        item.isNew = item.status === enumData.BusinessTemplatePlanStatus.N.code
        item.isInprogress = item.status === enumData.BusinessTemplatePlanStatus.W_A.code
        item.isCancel = item.status === enumData.BusinessTemplatePlanStatus.R.code
        item.isisDone = item.status === enumData.BusinessTemplatePlanStatus.A.code
        item.objPermissionApprove = await this.flowService.checkCanApproveByType(user, {
          lsType: [enumData.FlowCode.BUSINESS_PLAN_TEMPLATE.code],
          entityName: BusinessTemplateGroupPlanEntity.name,
          targetId: item.id,
        })

        item.lstApprovalProgress = await this.flowService.getListApproveDetail(user, {
          targetId: item.id,
          entityName: BusinessTemplateGroupPlanEntity.name,
          type: enumData.FlowCode.BUSINESS_PLAN_TEMPLATE.code,
        })

        item.isCanApprove = Object.values(item.objPermissionApprove).some((value) => value)

        const check = await this.flowService.checkCanRemoveApprove(user, {
          targetId: item.id,
          entityName: BusinessTemplateGroupPlanEntity.name,
          type: enumData.FlowCode.BUSINESS_PLAN_TEMPLATE.code,
        })
        item.isRevert = check.canRevert && item.status == enumData.BusinessTemplatePlanStatus.W_A.code
        item.revertReject = item.status == enumData.BusinessTemplatePlanStatus.R.code
      }

      item.statusName = enumData.BusinessTemplatePlanStatus[item.status]?.name
      item.statusBorderColor = enumData.BusinessTemplatePlanStatus[item.status]?.borderColor
      item.statusColor = enumData.BusinessTemplatePlanStatus[item.status]?.color
      item.statusBgColor = enumData.BusinessTemplatePlanStatus[item.status]?.bgColor
      const { canApprove, approvalProgress } = await this.flowService.getRoleApprove(user, {
        targetId: item.id,
        entityName: BusinessTemplateGroupPlanEntity.name,
      })
      Object.assign(item, { approvalProgress, canApprove })
    }

    return res
  }

  public async find(data: PaginationDto, user: UserDto) {
    const whereCon: any = {
      isDeleted: false,
    }
    const res: any[] = await this.businessTemplateGroupPlanRepository.find({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'DESC' },
      select: {
        id: true,
        code: true,
      },
    })
    return res
  }

  /* hàm gửi duyệt */
  public async sendApprove(user: UserDto, data: FilterOneDto) {
    await this.businessTemplateGroupPlanRepository.manager.transaction(async (transac) => {
      const repo = transac.getRepository(BusinessTemplateGroupPlanEntity)
      const entity = await repo.findOne({ where: { id: data.id, isDeleted: false } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
      // Nếu chưa phải lần duyệt cuối
      entity.status = enumData.BusinessTemplatePlanStatus.W_A.code
      await repo.save(entity)
    })
    return { message: UPDATE_ACTIVE_SUCCESS }
  }
  // hàm duyệt
  public async updateApproved(user: UserDto, data: { id: string }) {
    const approveStatus = await this.flowService.approveRule(user, {
      targetId: data.id,
      entityName: BusinessTemplateGroupPlanEntity.name,
    })
    await this.businessTemplateGroupPlanRepository.manager.transaction(async (transac) => {
      const repo = transac.getRepository(BusinessTemplateGroupPlanEntity)

      const entity = await repo.findOne({ where: { id: data.id, isDeleted: false } })
      if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

      // Nếu chưa phải lần duyệt cuối
      if (approveStatus.status === enumData.APPROVE_TYPE.NOT_DONE.code) {
        entity.status = enumData.BusinessTemplatePlanStatus.W_A.code
        await repo.save(entity)
        return { message: `Đã duyệt thành công, Vui lòng chờ cấp sau duyệt` }
      }
      entity.status = enumData.BusinessTemplatePlanStatus.A.code
      await repo.save(entity)
    })
    return { message: UPDATE_ACTIVE_SUCCESS }
  }

  // hàm từ chối duyệt
  public async updateRejectRule(user: UserDto, data: any) {
    const entity = await this.businessTemplateGroupPlanRepository.findOne({ where: { id: data.id } })
    await this.flowService.rejectRulePR(user, { targetId: data.id, entityName: BusinessTemplateGroupPlanEntity.name, level: data.level })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (entity.status !== enumData.BusinessTemplatePlanStatus.W_A.code) {
      throw new Error('Trạng thái không phù hợp. Vui lòng kiểm tra lại')
    }
    entity.status = enumData.BusinessTemplatePlanStatus.R.code
    entity.updatedBy = user.id
    entity.updatedAt = new Date()
    await this.businessTemplateGroupPlanRepository.save(entity)
    return { message: UPDATE_SUCCESS }
  }
  // hàm gỡ duyệt
  public async updateRevertStatus(user: UserDto, data: any) {
    const entity = await this.businessTemplateGroupPlanRepository.findOne({ where: { id: data.id } })
    await this.flowService.rejectRejectRulePR(user, { targetId: data.id, entityName: BusinessTemplateGroupPlanEntity.name, level: data.level })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    if (entity.status !== enumData.BusinessTemplatePlanStatus.R.code) {
      throw new Error('Trạng thái không phù hợp. Vui lòng kiểm tra lại')
    }
    entity.status = enumData.BusinessTemplatePlanStatus.W_A.code
    entity.updatedBy = user.id
    entity.updatedAt = new Date()
    await this.businessTemplateGroupPlanRepository.save(entity)
    return { message: UPDATE_SUCCESS }
  }
  // hàm gỡ từ chối duyệt
  public async updateRemoveRule2(user: UserDto, data: FilterOneDto) {
    const entity = await this.businessTemplateGroupPlanRepository.findOne({ where: { id: data.id } })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)
    const rejectStatus = await this.flowService.revertApprove(user, {
      targetId: data.id,
      entityName: BusinessTemplateGroupPlanEntity.name,
      type: enumData.FlowCode.BUSINESS_PLAN_TEMPLATE.code,
    })
    if (rejectStatus.isFist) {
      entity.status = enumData.ShipmentStatus.NEW.code
      entity.updatedBy = user.id
      entity.updatedAt = new Date()
      await this.businessTemplateGroupPlanRepository.save(entity)
    }
    return { message: UPDATE_SUCCESS }
  }

  /* hàm load danh sách chi phí theo material */
  public async loadCostByMaterialGroup(user: UserDto, data: { lstMaterialGroupId: string[] }) {
    return await this.costRepository.loadCostByMaterialGroup(user, data.lstMaterialGroupId)
  }

  /* hàm lưu */
  public async save(user: UserDto, data: BusinessTemplatePlanSaveDto) {
    /*  */
    return await this.businessTemplateGroupPlanRepository.manager.transaction(async (transac) => {
      // tạo ra  businessTemplateGroupPlan
      const businessTemplateGroupPlanRepo = transac.getRepository(BusinessTemplateGroupPlanEntity)
      const newBusinessTemplateGroupPlan = new BusinessTemplateGroupPlanEntity()
      // Mã PAKD tự sinh
      /* number PAKD */
      const numberBTGP = await businessTemplateGroupPlanRepo.count({ where: { isDeleted: false } })
      newBusinessTemplateGroupPlan.code = 'PAKD' + (numberBTGP + 1).toString().padStart(4, '0')
      newBusinessTemplateGroupPlan.status = enumData.BusinessTemplatePlanStatus.N.code
      newBusinessTemplateGroupPlan.createdBy = user.employeeId
      newBusinessTemplateGroupPlan.title = data.title
      newBusinessTemplateGroupPlan.plantId = data.plantId
      newBusinessTemplateGroupPlan.businessTemplatePlanTypeId = data.businessTemplatePlanTypeId
      newBusinessTemplateGroupPlan.purchasingOrgId = data.purchasingOrgId
      newBusinessTemplateGroupPlan.rfqCompactId = data.rfqCompactId
      // lưu lại
      const businessTemplateGroupPlan = await businessTemplateGroupPlanRepo.save(newBusinessTemplateGroupPlan)
      // tạo luồng duyệt
      await this.flowService.setRoleRule(user, {
        targetId: businessTemplateGroupPlan.id,
        target: businessTemplateGroupPlan,
        entityName: BusinessTemplateGroupPlanEntity.name,
        flowType: enumData.FlowCode.BUSINESS_PLAN_TEMPLATE.code,
      })
      // tạo ra danh sách phương án
      /* for qua lstBusinessPlan để lưu */
      // danh sách phương án để lưu
      const lstBusinessPlanToSave = []
      // danh sách chi phí để lưu
      const lstBusinessPlanCostToSave = []
      // danh sách chuyển đổi đơn vị tiền tệ để lưu
      const lstBusinessPlanExchangeToSave = []
      // danh sách cấu hình costLst để lưu
      const lstBusinessPlanSetupToSave = []

      for (const businessPlan of data.lstBusinessPlan) {
        /* const  */
        const newBusinessPlan = new BusinessTemplatePlanEntity()
        // id
        newBusinessPlan.id = uuidv4()
        //code
        const numberPlan = await transac.getRepository(BusinessTemplatePlanEntity).count({ where: { isDeleted: false } })
        newBusinessPlan.code = 'NDPAKD' + (numberPlan + 1).toString().padStart(4, '0')

        // cấu hình lstCostConfig
        if (businessPlan.lstCostConfig) {
          businessPlan.lstCostConfig.forEach((item) => {
            const newLstCostConfig = new BusinessPlanTemplateCostListEntity()
            newLstCostConfig.businessTemplatePlanId = newBusinessPlan.id
            newLstCostConfig.costId = item.costId || item.id
            newLstCostConfig.type = item.type
            newLstCostConfig.value = item.value
            newLstCostConfig.code = item.code
            newLstCostConfig.valueVanilla = item.valueVanilla
            newLstCostConfig.typeConfig = enumData.CostConfigType.Overview.code
            lstBusinessPlanSetupToSave.push(newLstCostConfig)
          })
        }

        // cấu hình lstCostConfigDetail
        if (businessPlan.lstCostConfigDetail) {
          businessPlan.lstCostConfigDetail.forEach((item) => {
            const newLstCostConfig = new BusinessPlanTemplateCostListEntity()
            newLstCostConfig.businessTemplatePlanId = newBusinessPlan.id
            newLstCostConfig.costId = item.costId || item.id
            newLstCostConfig.type = item.type
            newLstCostConfig.value = item.value
            newLstCostConfig.code = item.code
            newLstCostConfig.valueVanilla = item.valueVanilla
            newLstCostConfig.typeConfig = enumData.CostConfigType.Detail.code
            lstBusinessPlanSetupToSave.push(newLstCostConfig)
          })
        }
        /* tỷ giá quy đổi (lstCurrencyExchage)*/
        if (businessPlan.lstCurrencyExchage) {
          businessPlan.lstCurrencyExchage.forEach((item) => {
            const newExchange = new BusinessTemplatePlanExchangeEntity()
            newExchange.businessTemplatePlanId = newBusinessPlan.id
            newExchange.fromCurrency = item.fromCurrency
            newExchange.fromCurrencyId = item.fromCurrencyId
            newExchange.toCurrency = item.toCurrency
            newExchange.toCurrencyId = item.toCurrencyId
            newExchange.exchangeRate = item.exchangeRate
            newExchange.exchangeRateInverse = item.exchangeRateInverse
            lstBusinessPlanExchangeToSave.push(newExchange)
          })
        }

        newBusinessPlan.businessTemplateGroupPlanId = businessTemplateGroupPlan.id
        // plant duyệt
        newBusinessPlan.plantId = businessTemplateGroupPlan.plantId
        // số chứng từ
        newBusinessPlan.code = 'NDPAKD' + (numberPlan + 1).toString().padStart(4, '0')
        //name
        newBusinessPlan.name = businessPlan.name

        // số tháng vay
        newBusinessPlan.loanTerm = businessPlan.loanTerm

        /* incoterm quy đổi*/
        newBusinessPlan.incotermId = businessPlan.incotermId
        newBusinessPlan.paymentTermId = businessPlan.paymentTermId

        /* ghi chú */
        newBusinessPlan.description = businessPlan.description
        /* tham chiếu */
        newBusinessPlan.referenceType = businessPlan.referenceType

        // PAVC
        newBusinessPlan.shipmentPlanId = businessPlan.shipmentPlanId

        //phương án PAVC
        newBusinessPlan.shipmentPlanPriceId = businessPlan.shipmentPlanPriceId

        // tổng chi phí cài đặt (lstBusinessPlanCostToSave)
        if (businessPlan.lstCostValue) {
          businessPlan.lstCostValue.forEach((item) => {
            const newCost = new BusinessTemplatePlanCostEntity()
            newCost.businessTemplatePlanId = newBusinessPlan.id
            newCost.name = item.name
            newCost.totalPrice = item.totalPrice
            newCost.lstRfqConfig = item.lstRfqConfig
            lstBusinessPlanCostToSave.push(newCost)
          })
        }

        // lưu án chọn tay shipment
        newBusinessPlan.shipmentFeeConditionTypeCompactId = businessPlan.shipmentFeeConditionTypeCompactId
        newBusinessPlan.shipmentFeeConditionTypeCompactCode = businessPlan.shipmentFeeConditionTypeCompactCode
        newBusinessPlan.currentcyId = businessPlan.currentcyId
        // incoterm PAVC
        newBusinessPlan.shipmentPlanIncotermId = businessPlan.shipmentPlanIncotermId
        // Tổng chu phí
        newBusinessPlan.shipmentPlanPrice = businessPlan.shipmentPlanPrice

        /* table config */
        newBusinessPlan.configTable = businessPlan.tableConfig
        // newBusinessPlan.currency = businessPlan.currency
        lstBusinessPlanToSave.push(newBusinessPlan)
      }
      // lưu danh sách phương án
      await transac.getRepository(BusinessTemplatePlanEntity).save(lstBusinessPlanToSave)
      // lưu danh sách chi phí
      await transac.getRepository(BusinessTemplatePlanCostEntity).save(lstBusinessPlanCostToSave)
      // lưu danh sách chuyển đổi đơn vị tiền tệ
      await transac.getRepository(BusinessTemplatePlanExchangeEntity).save(lstBusinessPlanExchangeToSave)
      // lưu danh sách cấu hình chi
      await transac.getRepository(BusinessPlanTemplateCostListEntity).save(lstBusinessPlanSetupToSave)

      return { message: 'Tạo mới thành công' }
    })
  }

  // hàm cập nhật
  public async update(user: UserDto, data: BusinessTemplatePlanUpdateDto) {
    return await this.businessTemplateGroupPlanRepository.manager.transaction(async (transac) => {
      const businessTemplateGroupPlanRepo = transac.getRepository(BusinessTemplateGroupPlanEntity)
      const newBusinessTemplateGroupPlan = await businessTemplateGroupPlanRepo.findOne({ where: { id: data.id, isDeleted: false } })
      if (!newBusinessTemplateGroupPlan) throw new Error(ERROR_NOT_FOUND_DATA)

      newBusinessTemplateGroupPlan.createdBy = user.id
      newBusinessTemplateGroupPlan.plantId = data.plantId
      newBusinessTemplateGroupPlan.title = data.title
      newBusinessTemplateGroupPlan.businessTemplatePlanTypeId = data.businessTemplatePlanTypeId
      newBusinessTemplateGroupPlan.purchasingOrgId = data.purchasingOrgId
      // lưu lại
      const businessTemplateGroupPlan = await businessTemplateGroupPlanRepo.save(newBusinessTemplateGroupPlan)
      const lstBusinessPlanRecord = await transac.getRepository(BusinessTemplatePlanEntity).find({
        where: { businessTemplateGroupPlanId: newBusinessTemplateGroupPlan.id },
      })
      // tạo luồng duyệt
      await this.flowService.setRoleRule(user, {
        targetId: businessTemplateGroupPlan.id,
        entityName: BusinessTemplateGroupPlanEntity.name,
        type: enumData.FlowCode.BUSINESS_PLAN_TEMPLATE.code,
      })
      // tạo ra danh sách phương án
      /* for qua lstBusinessPlan để lưu */
      // danh sách phương án để lưu
      const lstBusinessPlanToSave = []
      // danh sách chi phí để lưu
      const lstBusinessPlanCostToSave = []
      // danh sách chuyển đổi đơn vị tiền tệ để lưu
      const lstBusinessPlanExchangeToSave = []
      // danh sách cấu hình costLst để lưu
      const lstBusinessPlanSetupToSave = []
      //  xóa đi các record cũ trước khi thêm mới

      const lstBusinessPlanIds = lstBusinessPlanRecord.map((item) => item.id).filter(Boolean)
      if (lstBusinessPlanIds.length > 0) {
        await transac.getRepository(BusinessTemplatePlanCostEntity).delete({ businessTemplatePlanId: In(lstBusinessPlanIds) })
        // lưu danh sách chuyển đổi đơn vị tiền tệ
        await transac.getRepository(BusinessTemplatePlanExchangeEntity).delete({ businessTemplatePlanId: In(lstBusinessPlanIds) })
        // lưu danh sách cấu hình chi
        await transac.getRepository(BusinessPlanTemplateCostListEntity).delete({ businessTemplatePlanId: In(lstBusinessPlanIds) })
        // xóa đi các phương án cũ
        await transac.getRepository(BusinessTemplatePlanEntity).delete({
          id: In(lstBusinessPlanIds),
        })
      }

      for (const businessPlan of data.lstBusinessPlan) {
        /* const  */
        const newBusinessPlan = new BusinessTemplatePlanEntity()
        // id
        newBusinessPlan.id = uuidv4()

        // cấu hình lstCostConfig
        if (businessPlan.lstCostConfig) {
          businessPlan.lstCostConfig.forEach((item) => {
            const newLstCostConfig = new BusinessPlanTemplateCostListEntity()
            newLstCostConfig.businessTemplatePlanId = newBusinessPlan.id
            newLstCostConfig.costId = item.costId
            newLstCostConfig.type = item.type
            newLstCostConfig.code = item.code
            newLstCostConfig.value = item.value
            newLstCostConfig.valueVanilla = item.valueVanilla
            newLstCostConfig.typeConfig = enumData.CostConfigType.Overview.code
            lstBusinessPlanSetupToSave.push(newLstCostConfig)
          })
        }
        // cấu hình lstCostConfigDetail
        if (businessPlan.lstCostConfigDetail) {
          businessPlan.lstCostConfigDetail.forEach((item) => {
            const newLstCostConfig = new BusinessPlanTemplateCostListEntity()
            newLstCostConfig.businessTemplatePlanId = newBusinessPlan.id
            newLstCostConfig.costId = item.costId || item.id
            newLstCostConfig.type = item.type
            newLstCostConfig.code = item.code
            newLstCostConfig.value = item.value
            newLstCostConfig.valueVanilla = item.valueVanilla
            newLstCostConfig.typeConfig = enumData.CostConfigType.Detail.code
            lstBusinessPlanSetupToSave.push(newLstCostConfig)
          })
        }
        /* tỷ giá quy đổi (lstCurrencyExchage)*/
        if (businessPlan.lstCurrencyExchage) {
          businessPlan.lstCurrencyExchage.forEach((item) => {
            const newExchange = new BusinessTemplatePlanExchangeEntity()
            newExchange.businessTemplatePlanId = newBusinessPlan.id
            newExchange.fromCurrency = item.fromCurrency
            newExchange.fromCurrencyId = item.fromCurrencyId
            newExchange.toCurrency = item.toCurrency
            newExchange.toCurrencyId = item.toCurrencyId
            newExchange.exchangeRate = item.exchangeRate
            newExchange.exchangeRateInverse = item.exchangeRateInverse
            lstBusinessPlanExchangeToSave.push(newExchange)
          })
        }

        newBusinessPlan.businessTemplateGroupPlanId = businessTemplateGroupPlan.id
        // plant duyệt
        newBusinessPlan.plantId = businessTemplateGroupPlan.plantId
        // số chứng từ
        newBusinessPlan.code = businessPlan.code
        // số tháng vay
        newBusinessPlan.loanTerm = businessPlan.loanTerm

        /* incoterm quy đổi*/
        newBusinessPlan.incotermId = businessPlan.incotermId
        newBusinessPlan.paymentTermId = businessPlan.paymentTermId
        /* ghi chú */
        newBusinessPlan.description = businessPlan.description
        /* tham chiếu */
        newBusinessPlan.referenceType = businessPlan.referenceType
        /* tên */
        newBusinessPlan.name = businessPlan.name

        // PAVC
        newBusinessPlan.shipmentPlanId = businessPlan.shipmentPlanId

        //phương án PAVC
        newBusinessPlan.shipmentPlanPriceId = businessPlan.shipmentPlanPriceId

        // tổng chi phí cài đặt (lstBusinessPlanCostToSave)
        if (businessPlan.lstCostValue) {
          businessPlan.lstCostValue.forEach((item) => {
            const newCost = new BusinessTemplatePlanCostEntity()
            newCost.businessTemplatePlanId = newBusinessPlan.id
            newCost.name = item.name
            newCost.totalPrice = item.totalPrice
            newCost.lstRfqConfig = item.lstRfqConfig
            lstBusinessPlanCostToSave.push(newCost)
          })
        }
        // incoterm PAVC
        newBusinessPlan.shipmentPlanIncotermId = businessPlan.shipmentPlanIncotermId
        // Tổng chu phí
        newBusinessPlan.shipmentPlanPrice = businessPlan.shipmentPlanPrice

        /* table config */
        newBusinessPlan.configTable = businessPlan.tableConfig
        // newBusinessPlan.currency = businessPlan.currency
        lstBusinessPlanToSave.push(newBusinessPlan)
      }
      // lưu danh sách phương án
      await transac.getRepository(BusinessTemplatePlanEntity).save(lstBusinessPlanToSave)
      // lưu danh sách chi phí
      await transac.getRepository(BusinessTemplatePlanCostEntity).save(lstBusinessPlanCostToSave)
      // lưu danh sách chuyển đổi đơn vị tiền tệ
      await transac.getRepository(BusinessTemplatePlanExchangeEntity).save(lstBusinessPlanExchangeToSave)
      // lưu danh sách cấu hình chi
      await transac.getRepository(BusinessPlanTemplateCostListEntity).save(lstBusinessPlanSetupToSave)
      // Xử lý các phần khác nếu cần

      return { message: 'Cập nhật thành công' }
    })
  }
  // hàm load ra chi tiết của BusinessTemplateGroupPlan theo Id truyền vào
  public async loadDetailById(user: UserDto, data: FilterOneDto) {
    const entity = await this.businessTemplateGroupPlanRepository.findOne({
      where: { id: data.id, isDeleted: false },
    })
    if (!entity) throw new Error(ERROR_NOT_FOUND_DATA)

    // lấy luồng duyệt
    const lstApprovalProgress = await this.flowService.getListApproveDetail(user, {
      targetId: entity.id,
      entityName: BusinessTemplateGroupPlanEntity.name,
      type: enumData.FlowCode.BUSINESS_PLAN_TEMPLATE.code,
    })

    // Lấy ra danh sách phương án
    const lstBusinessPlan = await this.businessTemplateGroupRepository.find({
      where: { businessTemplateGroupPlanId: entity.id, isDeleted: false },
      order: { createdAt: 'ASC' },
    })

    const dicShipmentPlan = await this.businessTemplateGroupPlanRepository.manager.getRepository(ShipmentPlanEntity).find({
      where: { isDeleted: false },
    })

    const dicIncoterm = await this.businessTemplateGroupPlanRepository.manager.getRepository(IncotermEntity).find({
      where: { isDeleted: false },
    })

    const dicShipmentNumberPlan = await this.businessTemplateGroupPlanRepository.manager.getRepository(ShipmentPlanNumberEntity).find({
      where: { isDeleted: false },
    })

    // Lấy chi tiết cho từng phương án
    const lstBusinessPlanIds = lstBusinessPlan.map((item) => item.id)

    // Lấy danh sách cấu hình chi phí
    const toLowerCase = lstBusinessPlanIds.map((id: string) => id.toLowerCase())
    const lstCostConfigs: any = await this.businessTemplateGroupPlanRepository.manager.getRepository(BusinessPlanTemplateCostListEntity).find({
      where: { businessTemplatePlanId: In(toLowerCase), typeConfig: enumData.CostConfigType.Overview.code },
    })

    for (let item of lstCostConfigs) {
      item.id = item.id
      item.costId = item.costId
      item.type = item.type
      item.code = item.code
      item.value = item.value
      item.valueVanilla = item.valueVanilla
      const cost = await this.businessTemplateGroupPlanRepository.manager.getRepository(CostEntity).findOne({
        where: { id: item.costId },
      })
      item.name = cost.name
      item.checkMaster = item.type === enumData.BusinessTemplatePlanCostType.Master.code
      item.checkHand = item.type === enumData.BusinessTemplatePlanCostType.Hand.code
      item.checkRaw = item.type === enumData.BusinessTemplatePlanCostType.Raw.code
      item.checkOverview = item.type === enumData.BusinessTemplatePlanCostType.Overview.code
    }

    const lstCostConfigDetails: any = await this.businessTemplateGroupPlanRepository.manager.getRepository(BusinessPlanTemplateCostListEntity).find({
      where: { businessTemplatePlanId: In(toLowerCase), typeConfig: enumData.CostConfigType.Detail.code },
    })

    for (let item of lstCostConfigDetails) {
      item.id = item.id
      item.costId = item.costId
      item.type = item.type
      item.code = item.code
      item.value = item.value
      item.valueVanilla = item.valueVanilla
      const cost = await this.businessTemplateGroupPlanRepository.manager.getRepository(CostEntity).findOne({
        where: { id: item.costId },
      })
      item.name = cost.name
      item.checkMaster = item.type === enumData.BusinessTemplatePlanCostType.Master.code
      item.checkHand = item.type === enumData.BusinessTemplatePlanCostType.Hand.code
      item.checkRaw = item.type === enumData.BusinessTemplatePlanCostType.Raw.code
      item.checkOverview = item.type === enumData.BusinessTemplatePlanCostType.Overview.code
    }

    // Lấy danh sách tỷ giá
    const lstExchanges = await this.businessTemplateGroupPlanRepository.manager.getRepository(BusinessTemplatePlanExchangeEntity).find({
      where: { businessTemplatePlanId: In(lstBusinessPlanIds) },
    })

    // Lấy danh sách chi phí
    const lstCosts = await this.businessTemplateGroupPlanRepository.manager.getRepository(BusinessTemplatePlanCostEntity).find({
      where: { businessTemplatePlanId: In(lstBusinessPlanIds) },
    })

    // Map dữ liệu theo từng phương án
    const mappedBusinessPlans = lstBusinessPlan.map((plan) => ({
      id: plan.id,
      code: plan.code,
      loanTerm: plan.loanTerm,
      incotermId: plan.incotermId,
      paymentTermId: plan.paymentTermId,
      description: plan.description,
      referenceType: plan.referenceType,
      shipmentPlanId: plan.shipmentPlanId,
      shipmentPlanIncotermId: plan.shipmentPlanIncotermId,
      shipmentPlanPriceId: plan.shipmentPlanPriceId,
      shipmentPlanPrice: plan.shipmentPlanPrice,
      tableConfig: plan.configTable,
      name: plan.name,

      // Lấy cấu hình shipment
      shipmentPlanName: dicShipmentPlan.find((item) => item.id === plan.shipmentPlanId)?.name || '',
      shipmentPlanNumberName: dicShipmentNumberPlan.find((item) => item.id === plan.shipmentPlanPriceId)?.title || '',
      incotermName: dicIncoterm.find((item) => item.id === plan.shipmentPlanIncotermId)?.name || '',

      // Lấy cấu hình chi phí cho phương án này
      lstCostConfig: lstCostConfigs.map((config) => ({
        id: config.id,
        costId: config.costId,
        type: config.type,
        value: config.value,
        valueVanilla: config.valueVanilla,
        name: config.name,
        check: true,
        checkMaster: config.checkMaster,
        checkHand: config.checkHand,
        checkRaw: config.checkRaw,
      })),
      lstCostConfigDetail: lstCostConfigDetails.map((configDetail) => ({
        id: configDetail.id,
        costId: configDetail.costId,
        type: configDetail.type,
        value: configDetail.value,
        valueVanilla: configDetail.valueVanilla,
        name: configDetail.name,
        checkMaster: configDetail.checkMaster,
        checkHand: configDetail.checkHand,
        checkRaw: configDetail.checkRaw,
        checkOverview: configDetail.checkOverview,
      })),

      // Lấy tỷ giá cho phương án này
      lstCurrencyExchage: lstExchanges
        .filter((exchange) => exchange.businessTemplatePlanId === plan.id)
        .map((exchange) => ({
          id: exchange.id,
          fromCurrency: exchange.fromCurrency,
          fromCurrencyId: exchange.fromCurrencyId,
          toCurrency: exchange.toCurrency,
          toCurrencyId: exchange.toCurrencyId,
          exchangeRate: exchange.exchangeRate,
          exchangeRateInverse: exchange.exchangeRateInverse,
        })),

      // Lấy chi phí cho phương án này
      lstCostValue: lstCosts
        .filter((cost) => cost.businessTemplatePlanId === plan.id)
        .map((cost) => ({
          id: cost.id,
          name: cost.name,
          totalPrice: cost.totalPrice,
          lstRfqConfig: cost.lstRfqConfig,
        })),
    }))

    return {
      id: entity.id,
      lstApprovalProgress: lstApprovalProgress,
      code: entity.code,
      plantId: entity.plantId,
      title: entity.title,
      businessTemplatePlanTypeId: entity.businessTemplatePlanTypeId,
      purchasingOrgId: entity.purchasingOrgId,
      status: entity.status,
      createdAt: entity.createdAt,
      createdBy: entity.createdBy,
      rfqCompactId: entity.rfqCompactId,
      lstBusinessPlan: mappedBusinessPlans,
    }
  }

  calculateIncotermPaymentTerm(
    user: UserDto,
    data: { requestQuoteId: string[]; incotermId: string; currencyId: string; paymentTermId: string; lstCostValue: any[] },
  ) {
    return this.rfqService.loadReportSupplierDetailRFQ(user, data)
  }
}
