import { Body, Controller, Post, UseGuards } from '@nestjs/common'
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger'
import { JwtAuthGuard } from '../common/guards'
import { BusinessTemplatePlanService } from './businessTemplatePlan.service'
import { CurrentUser } from '../common/decorators'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import { BusinessTemplatePlanCreateTypeDto } from './dto/BusinessTemplatePlanCreateType.dto'
import { BusinessTemplatePlanSaveDto } from './dto'

@ApiBearerAuth()
@ApiTags('BusinessTemplatePlan')
@UseGuards(JwtAuthGuard)
@Controller('business_template_plan')
export class BusinessTemplatePlanController {
  constructor(private readonly service: BusinessTemplatePlanService) {}

  /* hàm tao type */
  @ApiOperation({ summary: 'Tạo mới một dữ liệu.' })
  @Post('create_type_data')
  async createType(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.createType(user, data)
  }
  // hàm lấy ra cấu hình
  @ApiOperation({ summary: 'Lấy cấu hình loại phương án' })
  @Post('get_type_config')
  async getTypeConfig(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.getTypeConfig(user, data)
  }

  @ApiOperation({ summary: 'Tạo mới một dữ liệu.' })
  @Post('update_type_data')
  async updateType(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.updateType(user, data)
  }
  // hàm ngưng hoạt động
  @ApiOperation({ summary: 'Ngưng hoạt động loại phương án' })
  @Post('delete_type_data')
  async deleteType(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.deleteType(user, data)
  }

  // danh sách type
  @ApiOperation({ summary: 'Lấy danh sách loại phương án' })
  @Post('get_type_list')
  async getTypeList(@CurrentUser() user: UserDto) {
    return await this.service.getTypeList(user)
  }
  // pagination type
  @ApiOperation({ summary: 'Lấy danh sách loại phương án' })
  @Post('pagination_type_list')
  async paginationTypeList(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.paginationTypeList(user, data)
  }

  @ApiOperation({ summary: 'Lấy danh sách material của RFQ' })
  @Post('get_rfq_item')
  async getRfqItem(
    @CurrentUser() user: UserDto,
    @Body() data: { lstRfqId: string[]; lstMaterialId?: string[]; lstMaterialGroupId?: string[]; lstCollectiveId?: string[]; createdAt?: string[] },
  ) {
    return await this.service.getRfqItem(user, data.lstRfqId, data.lstMaterialId, data.lstMaterialGroupId, data.lstCollectiveId, data.createdAt)
  }
  // hamg pagination
  @ApiOperation({ summary: 'Lấy danh sách phương án có phân trang' })
  @Post('pagination')
  async pagination(@Body() data: PaginationDto, @CurrentUser() user: UserDto) {
    return await this.service.pagination(data, user)
  }

  // hàm find
  @ApiOperation({ summary: 'Lấy danh sách phương án' })
  @Post('find')
  async find(@Body() data: PaginationDto, @CurrentUser() user: UserDto) {
    return await this.service.find(data, user)
  }

  // hàm load danh sách
  @ApiOperation({ summary: 'Lấy danh sách phương án' })
  @Post('get_list')
  public async getList(@CurrentUser() user: UserDto, @Body() data: PaginationDto) {
    return await this.service.getList(user, data)
  }

  /* hàm gửi duyệt */
  @ApiOperation({ summary: 'Gửi duyệt' })
  @Post('send_approve')
  public async sendApprove(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.sendApprove(user, data)
  }
  // hàm duyệt
  @ApiOperation({ summary: 'API Cập nhật duyệt' })
  @Post('update_approved_year')
  public async updateApproved(@CurrentUser() user: UserDto, @Body() data: { id: string }) {
    return await this.service.updateApproved(user, data)
  }
  // hàm từ chối duyệt
  @ApiOperation({ summary: 'API từ chối duyệt' })
  @Post('update_reject_rule')
  public async updateRejectRule(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateRejectRule(user, data)
  }
  // hàm gỡ duyệt
  @ApiOperation({ summary: 'Gỡ từ chối duyệt khi từ chối ' })
  @Post('update_revert_status')
  public async updateRevertStatus(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateRevertStatus(user, data)
  }
  // hàm gỡ từ chối duyệt
  @ApiOperation({ summary: 'API gở duyệt' })
  @Post('update_remove_rule_2')
  public async updateRemoveRule2(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.updateRemoveRule2(user, data)
  }
  // hàm load cost theo materialGr
  @ApiOperation({ summary: 'Load danh sách chi phí theo material' })
  @Post('load_cost_by_material_group')
  public async loadCostByMaterialGroup(@CurrentUser() user: UserDto, @Body() data: { lstMaterialGroupId: string[] }) {
    return await this.service.loadCostByMaterialGroup(user, data)
  }

  // /* hàm lưu */
  @Post('save')
  @ApiOperation({ summary: 'Lưu phương án' })
  public async save(@CurrentUser() user: UserDto, @Body() data: BusinessTemplatePlanSaveDto) {
    return await this.service.save(user, data)
  }

  // hàm cập nhật
  @Post('update')
  @ApiOperation({ summary: 'Cập nhật phương án' })
  public async update(@CurrentUser() user: UserDto, @Body() data: any) {
    return await this.service.update(user, data)
  }

  // hàm load detail theo id
  @Post('load_detail_by_id')
  @ApiOperation({ summary: 'Lấy chi tiết phương án theo id' })
  public async loadDetailById(@CurrentUser() user: UserDto, @Body() data: FilterOneDto) {
    return await this.service.loadDetailById(user, data)
  }

  // hàm tính toán Incoterm quy đổi// PaymentTerm
  @Post('calculate_incoterm_payment_term')
  @ApiOperation({ summary: 'Tính toán incoterm và payment term' })
  public async calculateIncotermPaymentTerm(
    @CurrentUser() user: UserDto,
    @Body() data: { requestQuoteId: string[]; incotermId: string; currencyId: string; paymentTermId: string; lstCostValue: any[] },
  ) {
    return await this.service.calculateIncotermPaymentTerm(user, data)
  }

  // /* hàm copy */
  // @Post('copy')
  // @ApiOperation({ summary: 'Sao chép phương án' })
  // public async copy(@CurrentUser() user: UserDto, @Body() data: any) {
  //   return await this.service.copy(user, data)
  // }
}
