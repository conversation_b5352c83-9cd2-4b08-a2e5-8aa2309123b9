import { BaseEntity } from './base.entity'
import { Entity, Column, ManyToOne, JoinColumn, OneToMany } from 'typeorm'
import { BidEntity } from './bid.entity'
import { SupplierEntity } from './supplier.entity'
import { BidSupplierTechValueEntity } from './bidSupplierTechValue.entity'
import { BidSupplierPriceValueEntity } from './bidSupplierPriceValue.entity'
import { BidSupplierTradeValueEntity } from './bidSupplierTradeValue.entity'
import { BidSupplierCustomPriceValueEntity } from './bidSupplierCustomPriceValue.entity'
import { BidSupplierPriceColValueEntity } from './bidSupplierPriceColValue.entity'
import { BidSupplierPriceEntity } from './bidSupplierPrice.entity'
import { BidPrItemEntity } from './bidPrItem.entity'
import { BidSupplierShipmentValueEntity } from './bidSupplierShipmentValue.entity'
import { ShipmentStageEntity } from './shipmentStage.entity'
import { MaterialGroupEntity } from './materialGroup.entity'

@Entity('bid_supplier')
export class BidSupplierEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    nullable: false,
  })
  bidId: string
  @ManyToOne(() => BidEntity, (p) => p.bidSuppliers)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid: Promise<BidEntity>

  /** Lý do */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  note: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  noteTrade: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  noteTech: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  notePrice: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  noteMPOLeader: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  noteTechLeader: string

  /** Điểm Kỹ thuật */
  @Column({
    type: 'float',
    nullable: false,
    default: 0,
  })
  scoreTech: number

  /** Điểm giá */
  @Column({
    type: 'float',
    nullable: false,
    default: 0,
  })
  scorePrice: number

  /** Điểm DKTM */
  @Column({
    type: 'float',
    nullable: false,
    default: 0,
  })
  scoreTrade: number

  /** Điểm HĐXT Kỹ thuật */
  @Column({
    type: 'float',
    nullable: false,
    default: 0,
  })
  scoreManualTech: number

  /** Điểm HĐXT giá */
  @Column({
    type: 'float',
    nullable: false,
    default: 0,
  })
  scoreManualPrice: number

  /** Điểm HĐXT DKTM */
  @Column({
    type: 'float',
    nullable: false,
    default: 0,
  })
  scoreManualTrade: number

  @Column({
    type: 'varchar',
    nullable: false,
  })
  supplierId: string
  @ManyToOne(() => SupplierEntity, (p) => p.bidSupplier)
  @JoinColumn({ name: 'supplierId', referencedColumnName: 'id' })
  supplier: Promise<SupplierEntity>

  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  status: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  statusFile: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  statusTech: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  statusTrade: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  statusPrice: string

  @Column({
    nullable: false,
    default: false,
  })
  isHighlight: boolean

  @Column({
    nullable: false,
    default: false,
  })
  isNotHaveMinValue: boolean

  /** Trạng thái hoàn tất thầu */
  @Column({
    nullable: false,
    default: false,
  })
  isSuccessBid: boolean

  /** Ghi chú cho Doanh nghiệp trúng thầu */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  noteSuccessBid: string

  /** Trạng thái hồ sơ kỹ thuật */
  @Column({
    nullable: false,
    default: true,
  })
  isTechValid: boolean

  /** Trạng thái hồ sơ thương mại */
  @Column({
    nullable: false,
    default: true,
  })
  isTradeValid: boolean

  /** Trạng thái hồ sơ giá */
  @Column({
    nullable: false,
    default: true,
  })
  isPriceValid: boolean

  /** Trạng thái nộp bảng giá hiệu chỉnh */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
    default: 'KhongYeuCau',
  })
  statusResetPrice: string

  /** Dữ kiệu kiểu JSON */
  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  dataJson: string

  /** File chi tiết giá (Nếu có) */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  filePriceDetail: string

  /** File chi tiết kỹ thuật (Nếu có) */
  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
  })
  fileTechDetail: string

  /** Ngày nộp chào giá */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  submitDate: Date

  /** Tổng doanh thu (tính khi trúng thầu hoặc qua hàm gen lại cho data cũ) */
  @Column({
    type: 'bigint',
    nullable: true,
  })
  totalPrice: number

  // /** Lĩnh vực mời thầu (lưu từ bid.serviceId khi tạo bidSupplier hoặc qua hàm gen lại cho data cũ, không refer) */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  serviceId: string

  /** Danh sách giá trị dữ liệu nhà cung cấp đấu thầu */
  @OneToMany(() => BidSupplierTechValueEntity, (p) => p.bidSupplier)
  bidSupplierTechValue: Promise<BidSupplierTechValueEntity[]>

  /** Danh sách giá trị dữ liệu nhà cung cấp đấu thầu */
  @OneToMany(() => BidSupplierShipmentValueEntity, (p) => p.bidSupplier)
  bidSupplierShipmentTechValue: Promise<BidSupplierShipmentValueEntity[]>

  /** Danh sách giá trị dữ liệu nhà cung cấp đấu thầu */
  @OneToMany(() => BidSupplierTradeValueEntity, (p) => p.bidSupplier)
  bidSupplierTradeValue: Promise<BidSupplierTradeValueEntity[]>

  /** Danh sách giá trị dữ liệu nhà cung cấp đấu thầu */
  @OneToMany(() => BidSupplierPriceValueEntity, (p) => p.bidSupplier)
  bidSupplierPriceValue: Promise<BidSupplierPriceValueEntity[]>

  /** Danh sách giá trị dữ liệu các cột động mà Doanh nghiệp nhập */
  @OneToMany(() => BidSupplierPriceColValueEntity, (p) => p.bidSupplier)
  bidSupplierPriceColValue: Promise<BidSupplierPriceColValueEntity[]>

  @OneToMany(() => BidSupplierCustomPriceValueEntity, (p) => p.bidSupplier)
  bidSupplierCustomPriceValue: Promise<BidSupplierCustomPriceValueEntity[]>

  @OneToMany(() => BidSupplierPriceEntity, (p) => p.bidSupplier)
  bidSupplierPrices: Promise<BidSupplierPriceEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  bidItemId: string
  @ManyToOne(() => BidPrItemEntity, (p) => p.bidSuppliers)
  @JoinColumn({ name: 'bidItemId', referencedColumnName: 'id' })
  bidItem: Promise<BidPrItemEntity>

  @OneToMany(() => ShipmentStageEntity, (p) => p.bidSupplier)
  stages: Promise<ShipmentStageEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialGroupId: string

  @ManyToOne(() => MaterialGroupEntity, (p) => p.bidSuppliers)
  @JoinColumn({ name: 'materialGroupId', referencedColumnName: 'id' })
  materialGroup: Promise<MaterialGroupEntity>
}
