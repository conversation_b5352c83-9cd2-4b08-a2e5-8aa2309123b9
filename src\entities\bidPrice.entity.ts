import { BaseEntity } from './base.entity'
import { En<PERSON><PERSON>, Column, ManyTo<PERSON>ne, Join<PERSON><PERSON><PERSON><PERSON>, OneToMany } from 'typeorm'
import { BidEntity } from './bid.entity'
import { BidSupplierPriceValueEntity } from './bidSupplierPriceValue.entity'
import { BidPriceListDetailEntity } from './bidPriceListDetail.entity'
import { BidDealPriceEntity } from './bidDealPrice.entity'
import { ServicePriceEntity } from './servicePrice.entity'
import { BidDealSupplierPriceValueEntity } from './bidDealSupplierPriceValue.entity'
import { BidAuctionSupplierPriceValueEntity } from './bidAuctionSupplierPriceValue.entity'
import { BidAuctionPriceEntity } from './bidAuctionPrice.entity'
import { BidSupplierPriceColValueEntity } from './bidSupplierPriceColValue.entity'
import { BidPriceColValueEntity } from './bidPriceColValue.entity'
import { BidSupplierPriceEntity } from './bidSupplierPrice.entity'
import { BidPrItemEntity } from './bidPrItem.entity'
import { BidExMatGroupEntity } from './bidExgroup.entity'
import { MaterialEntity } from './material.entity'
import { MaterialGroupEntity } from './materialGroup.entity'
import { PlantEntity } from './plant.entity'
import { ExternalMaterialGroupEntity } from './externalMaterialGroup.entity'
import { UomEntity } from './uom.entity'

@Entity('bid_price')
export class BidPriceEntity extends BaseEntity {
  /** Số lượng */
  @Column({
    nullable: true,
    default: 0,
  })
  number: number

  @Column({
    nullable: true,
    default: 0,
  })
  sort: number

  /** Tên  */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  name: string

  /** Có bắt buộc nhập hay không */
  @Column({
    nullable: true,
    default: false,
  })
  isRequired: boolean

  /** Có cấu hình giá hay không */
  @Column({
    nullable: true,
    default: false,
  })
  isSetup: boolean

  /** Có Theo template cơ cấu giá hay không */
  @Column({
    nullable: true,
    default: true,
  })
  isTemplate: boolean

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
    transformer: {
      to(value) {
        return value ? value.toString() : null
      },
      from(value) {
        return value
      },
    },
  })
  value: string

  /** Kiểu dữ liệu: string - number - cal. Nếu cal thì cho phép tạo công thức con*/
  @Column({
    nullable: true,
    default: 'string',
  })
  type: string

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  unit: string

  /** Đơn vị tính */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  currency: string

  /** % tỉ trọng */
  @Column({
    type: 'float',
    nullable: true,
    default: 100,
  })
  percent: number

  /** Cấp độ */
  @Column({
    nullable: true,
    default: 1,
  })
  level: number

  /** Mô tả */
  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  /** Id của công thức cha */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  parentId: string
  /** Cha */
  @ManyToOne(() => BidPriceEntity, (p) => p.childs)
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: BidPriceEntity

  /** Con - 1 công thức sẽ có thể có nhiều con */
  @OneToMany(() => BidPriceEntity, (p) => p.parent)
  childs: Promise<BidPriceEntity[]>

  /** Điểm chuẩn của công thức độ lệch chuẩn */
  @Column({
    nullable: true,
  })
  scoreDLC: number

  /** Giá trị nhỏ nhất */
  @Column({
    nullable: true,
  })
  requiredMin: number

  @Column({
    type: 'varchar',
    nullable: true,
  })
  bidId: string
  /** 1 công thức chỉ có 1 dịch vụ cha */
  @ManyToOne(() => BidEntity, (p) => p.prices)
  @JoinColumn({ name: 'bidId', referencedColumnName: 'id' })
  bid: Promise<BidEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  bidExgroupId: string
  /** 1 công thức chỉ có 1 dịch vụ cha */
  @ManyToOne(() => BidExMatGroupEntity, (p) => p.prices)
  @JoinColumn({ name: 'bidExgroupId', referencedColumnName: 'id' })
  bidExgroup: Promise<BidExMatGroupEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  servicePriceId: string
  @ManyToOne(() => ServicePriceEntity, (p) => p.bidPrices)
  @JoinColumn({ name: 'servicePriceId', referencedColumnName: 'id' })
  servicePrice: Promise<ServicePriceEntity>

  /** Danh sách giá trị dữ liệu nhà cung cấp đấu thầu */
  @OneToMany(() => BidSupplierPriceValueEntity, (p) => p.bidPrice)
  bidSupplierPriceValue: Promise<BidSupplierPriceValueEntity[]>

  /** Danh sách giá trị dữ liệu nhà cung cấp đấu thầu */
  @OneToMany(() => BidDealSupplierPriceValueEntity, (p) => p.bidPrice)
  bidDealSupplierPriceValue: Promise<BidDealSupplierPriceValueEntity[]>

  /** Danh sách giá trị dữ liệu nhà cung cấp đấu thầu */
  @OneToMany(() => BidAuctionSupplierPriceValueEntity, (p) => p.bidPrice)
  bidAuctionSupplierPriceValue: Promise<BidAuctionSupplierPriceValueEntity[]>

  /** Con - 1 công thức sẽ có thể có nhiều detail list */
  @OneToMany(() => BidPriceListDetailEntity, (p) => p.bidPrice)
  bidPriceListDetails: Promise<BidPriceListDetailEntity[]>

  @OneToMany(() => BidDealPriceEntity, (p) => p.bidPrice)
  bidDealPrices: Promise<BidDealPriceEntity[]>

  @OneToMany(() => BidAuctionPriceEntity, (p) => p.bidPrice)
  bidAuctionPrices: Promise<BidAuctionPriceEntity[]>

  @OneToMany(() => BidPriceColValueEntity, (p) => p.bidPrice)
  bidPriceColValue: Promise<BidPriceColValueEntity[]>

  @OneToMany(() => BidSupplierPriceColValueEntity, (p) => p.bidPrice)
  bidSupplierPriceColValue: Promise<BidSupplierPriceColValueEntity[]>

  /** Giá Doanh nghiệp chào */
  @OneToMany(() => BidSupplierPriceEntity, (p) => p.bidPrice)
  bidSupplierPrices: Promise<BidSupplierPriceEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  bidItemId: string
  @ManyToOne(() => BidPrItemEntity, (p) => p.trades)
  @JoinColumn({ name: 'bidItemId', referencedColumnName: 'id' })
  bidItem: Promise<BidPrItemEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  baseItemId: string
  @ManyToOne(() => BidPrItemEntity, (p) => p.trades)
  @JoinColumn({ name: 'baseItemId', referencedColumnName: 'id' })
  baseItem: Promise<BidPrItemEntity>

  /** Item Line */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  itemNo: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialCode: string

  /** Category */
  @Column({
    type: 'varchar',
    nullable: true,
    length: 1,
  })
  category: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialId: string
  @ManyToOne(() => MaterialEntity, (p) => p.bidPrices)
  @JoinColumn({ name: 'materialId', referencedColumnName: 'id' })
  material: Promise<MaterialEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  materialGroupId: string

  @ManyToOne(() => MaterialGroupEntity, (p) => p.bidPrices)
  @JoinColumn({ name: 'materialGroupId', referencedColumnName: 'id' })
  materialGroup: Promise<MaterialGroupEntity>

  /** Order */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  orderCode: string

  /** Order */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  orderName: string

  /** asset */
  @Column({
    length: 'max',
    nullable: true,
  })
  assetCode: string

  /** asset Desc */
  @Column({
    length: 'max',
    nullable: true,
  })
  assetDesc: string

  /** Công ty */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string
  @ManyToOne(() => PlantEntity, (p) => p.bidPrices)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  /** Kho hàng */
  @Column({
    length: 'max',
    nullable: true,
  })
  sloc: string

  @Column({ nullable: true, default: 0, type: 'float' })
  quantity: number

  @Column({
    nullable: true,
    type: 'datetime',
  })
  deliveryDate: Date

  /** tên hàng */
  @Column({
    type: 'varchar',
    length: 1000,
    nullable: true,
  })
  shortText: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  externalMaterialGroupId: string
  @ManyToOne(() => ExternalMaterialGroupEntity, (p) => p.bidPrices)
  @JoinColumn({ name: 'externalMaterialGroupId', referencedColumnName: 'id' })
  externalMaterialGroup: Promise<ExternalMaterialGroupEntity>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  uomId: string
  @ManyToOne(() => UomEntity, (p) => p.bidPrices)
  @JoinColumn({ name: 'uomId', referencedColumnName: 'id' })
  uom: Promise<UomEntity>
}
