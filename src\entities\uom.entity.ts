import { <PERSON><PERSON><PERSON>, <PERSON>umn, OneToMany, ManyToOne, Join<PERSON><PERSON>um<PERSON> } from 'typeorm'
import { BaseEntity } from './base.entity'
import { PrItemEntity } from './prItem.entity'
import { MaterialEntity } from './material.entity'
import { OfferServiceEntity } from './offerService.entity'
import { ContractInspectionItemEntity } from './contractInspectionItem.entity'
import { ContractItemEntity } from './contractItem.entity'
import { BidPrItemEntity } from './bidPrItem.entity'
import { POProductEntity } from './poProduct.entity'
import { MaterialUomEntity } from './materialUom.entity'
import { ReservationNormEntity } from './reservationNorm.entity'
import { ReservationItemEntity } from './reservationItem.entity'
import { PrItemCompomentEntity } from './prItemComponent.entity'
import { ReservationNormDetailEntity } from './reservationNormDetail.entity'
import { RequestQuoteDetailEntity } from './requestQuoteDetail.entity'
import { RfqDetailsEntity } from './rfqDetails.entity'
import { ContractAppendixItemEntity } from './contractAppendixItem.entity'
import { BidPriceEntity } from './bidPrice.entity'

/** Unit of Measure - Đơn vị tính */
@Entity('uom')
export class UomEntity extends BaseEntity {
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 250,
    nullable: true,
  })
  description: string

  @OneToMany(() => PrItemEntity, (p) => p.unit)
  prItems: Promise<PrItemEntity[]>

  @OneToMany(() => BidPrItemEntity, (p) => p.unit)
  bidItems: Promise<BidPrItemEntity[]>

  @OneToMany(() => OfferServiceEntity, (p) => p.unit)
  offerService: Promise<OfferServiceEntity[]>

  @OneToMany(() => OfferServiceEntity, (p) => p.oun)
  offerServices: Promise<OfferServiceEntity[]>

  @OneToMany(() => PrItemEntity, (p) => p.oun)
  prItem: Promise<PrItemEntity[]>

  @OneToMany(() => MaterialEntity, (p) => p.unit)
  materials: Promise<MaterialEntity[]>

  @OneToMany(() => MaterialEntity, (p) => p.unit)
  auctionSupplierPrice: Promise<MaterialEntity[]>

  @Column({
    type: 'varchar',
    nullable: true,
  })
  baseUnitId?: string
  @ManyToOne(() => UomEntity, (p) => p.alternativeUnits)
  @JoinColumn({ name: 'baseUnitId', referencedColumnName: 'id' })
  baseUnit?: Promise<UomEntity>

  @OneToMany(() => UomEntity, (p) => p.baseUnit)
  alternativeUnits?: Promise<UomEntity[]>

  @OneToMany(() => ContractInspectionItemEntity, (p) => p.unit)
  contractInspectionItems: Promise<ContractInspectionItemEntity[]>

  @OneToMany(() => ContractItemEntity, (p) => p.unit)
  contractItems: Promise<ContractItemEntity[]>

  /**PO item */
  @OneToMany(() => POProductEntity, (p) => p.unit)
  poProducts: Promise<POProductEntity[]>

  @OneToMany(() => MaterialUomEntity, (p) => p.uom)
  materialUoms: Promise<MaterialUomEntity[]>

  @OneToMany(() => ReservationNormEntity, (p) => p.unit)
  reservationNorms: Promise<ReservationNormEntity[]>

  @OneToMany(() => ReservationItemEntity, (p) => p.uom)
  reservationItems: Promise<ReservationItemEntity[]>

  @OneToMany(() => ReservationItemEntity, (p) => p.uomAlternative)
  alternativeReservationItems: Promise<ReservationItemEntity[]>

  @OneToMany(() => MaterialUomEntity, (p) => p.uomAlternative)
  materialUom?: Promise<MaterialUomEntity[]>

  @OneToMany(() => MaterialEntity, (p) => p.unitOfMass)
  unitOfMassMaterials: Promise<MaterialEntity[]>

  @OneToMany(() => MaterialEntity, (p) => p.unitOfVolume)
  unitOfVolumeMaterials: Promise<MaterialEntity[]>

  @OneToMany(() => MaterialEntity, (p) => p.unitOfMeasurement)
  unitOfMeasurementMaterials: Promise<MaterialEntity[]>

  @OneToMany(() => ReservationNormEntity, (p) => p.baseUnit)
  reservationBaseUnitNorms: Promise<ReservationNormEntity[]>

  @OneToMany(() => ReservationItemEntity, (p) => p.baseUnit)
  reservationItemBaseUnit: Promise<ReservationItemEntity[]>

  @OneToMany(() => PrItemEntity, (p) => p.baseUnit)
  prItemBaseUnits: Promise<PrItemEntity[]>

  @OneToMany(() => PrItemCompomentEntity, (p) => p.unit)
  prItemCompoments: Promise<PrItemCompomentEntity[]>

  @OneToMany(() => ReservationNormDetailEntity, (p) => p.baseUnit)
  reservationNormDetails: Promise<ReservationNormDetailEntity[]>

  @OneToMany(() => ReservationNormDetailEntity, (p) => p.unit)
  reservationNormUnitDetails: Promise<ReservationNormDetailEntity[]>

  @OneToMany(() => RequestQuoteDetailEntity, (p) => p.unit)
  requestQuoteDetails: Promise<RequestQuoteDetailEntity[]>

  @OneToMany(() => ContractItemEntity, (p) => p.oun)
  contractItemUons: Promise<ContractItemEntity[]>

  @OneToMany(() => RfqDetailsEntity, (p) => p.unit)
  rfqDetails: Promise<RfqDetailsEntity[]>

  @OneToMany(() => ContractAppendixItemEntity, (p) => p.unit)
  contractAppendixItems: Promise<ContractAppendixItemEntity[]>

  @OneToMany(() => ContractAppendixItemEntity, (p) => p.oun)
  contractAppendixItemUons: Promise<ContractAppendixItemEntity[]>

  @OneToMany(() => BidPriceEntity, (p) => p.uom)
  bidPrices: Promise<BidPriceEntity[]>
}
