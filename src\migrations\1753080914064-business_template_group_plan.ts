import { MigrationInterface, QueryRunner } from 'typeorm'

export class BusinessTemplateGroupPlan1753080914064 implements MigrationInterface {
  name = 'BusinessTemplateGroupPlan1753080914064'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_group_plan" ADD "rfqCompactId" varchar(max)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_group_plan" DROP COLUMN "rfqCompactId"`)
  }
}
