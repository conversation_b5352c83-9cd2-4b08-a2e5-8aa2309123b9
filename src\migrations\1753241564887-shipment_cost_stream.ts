import { MigrationInterface, QueryRunner } from 'typeorm'

export class ShipmentCostStream1753241564887 implements MigrationInterface {
  name = 'ShipmentCostStream1753241564887'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "shipment_cost_stream" ("id" uniqueidentifier NOT NULL CONSTRAINT "DF_04028bdcb1bf14b3fba8e257ac8" DEFAULT NEWSEQUENTIALID(), "createdAt" datetime, "createdBy" varchar(255), "updatedAt" datetime, "updatedBy" varchar(255), "isDeleted" bit NOT NULL CONSTRAINT "DF_382e2a626793c09d5d9fc39e375" DEFAULT 0, "companyId" varchar(255), "purchasingOrgId" varchar(255), "purchasingGroupId" varchar(255), "prTypeCodeRef" varchar(255), "poTypeCodeRef" varchar(255), "code" varchar(50) NOT NULL, "status" varchar(50), "name" varchar(max), "description" varchar(4000), "price" int, CONSTRAINT "PK_04028bdcb1bf14b3fba8e257ac8" PRIMARY KEY ("id"))`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "shipment_cost_stream"`)
  }
}
