import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany, JoinColumn, ManyToOne } from 'typeorm'
import { MaterialEntity } from './material.entity'
import { PrItemEntity } from './prItem.entity'
import { ExternalMaterialGroupEntity } from './externalMaterialGroup.entity'
import { OfferServiceEntity } from './offerService.entity'
import { CostEntity } from './cost.entity'
import { LeadTimeEntity } from './leadtime.entity'
import { LeadTimeDetailEntity } from './leadtimeDetail.entity'
import { PaymentTermEntity } from './paymentTerm.entity'
import { IncotermEntity } from './incoterm.entity'
import { RequestQuoteDetailEntity } from './requestQuoteDetail.entity'
import { ContractItemEntity } from './contractItem.entity'
import { RfqDetailsEntity } from './rfqDetails.entity'
import { ContractAppendixItemEntity } from './contractAppendixItem.entity'
import { BidPriceEntity } from './bidPrice.entity'

@Entity('material_group')
export class MaterialGroupEntity extends BaseEntity {
  /** Tên nhóm vật tư 1 */
  @Column({
    type: 'nvarchar',
    length: 500,
    nullable: false,
  })
  name: string

  /** Tên nhóm vật tư 2 */
  @Column({
    type: 'nvarchar',
    length: 500,
    nullable: true,
  })
  name2: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @Column({
    type: 'varchar',
    nullable: true,
  })
  externalMaterialGroupId: string
  @ManyToOne(() => ExternalMaterialGroupEntity, (p) => p.materialGroups)
  @JoinColumn({ name: 'externalMaterialGroupId', referencedColumnName: 'id' })
  externalMaterialGroup: Promise<ExternalMaterialGroupEntity>

  @OneToMany(() => MaterialEntity, (p) => p.materialGroup)
  materials: Promise<MaterialEntity[]>

  @OneToMany(() => PrItemEntity, (p) => p.materialGroup)
  prItems: Promise<PrItemEntity[]>

  @OneToMany(() => PrItemEntity, (p) => p.materialGroup)
  bidItems: Promise<PrItemEntity[]>

  @OneToMany(() => OfferServiceEntity, (p) => p.materialGroup)
  offerService: Promise<OfferServiceEntity[]>

  @OneToMany(() => CostEntity, (p) => p.materialGroup)
  costs: Promise<CostEntity[]>

  @OneToMany(() => LeadTimeEntity, (p) => p.materialGroup)
  leadtime: Promise<LeadTimeEntity[]>

  @OneToMany(() => LeadTimeDetailEntity, (p) => p.materialGroup)
  leadtimeDetails: Promise<LeadTimeDetailEntity[]>

  /** Thời hạn thanh toán (Defautl) */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  paymentTermId: string
  @ManyToOne(() => PaymentTermEntity, (p) => p.materialGroups)
  @JoinColumn({ name: 'paymentTermId', referencedColumnName: 'id' })
  paymentTerm: Promise<PaymentTermEntity>

  /** Điều kiện thương mại (Defautl) */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  incotermId: string
  @ManyToOne(() => IncotermEntity, (p) => p.materialGroups)
  @JoinColumn({ name: 'incotermId', referencedColumnName: 'id' })
  incoterm: Promise<IncotermEntity>

  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  lstPaymentTermId: string

  @Column({ type: 'nvarchar', length: 'max', nullable: true })
  lstIncotermId: string

  @OneToMany(() => RequestQuoteDetailEntity, (p) => p.materialGroup)
  requestQuoteDetails: Promise<RequestQuoteDetailEntity[]>

  @OneToMany(() => ContractItemEntity, (p) => p.materialGroup)
  contractItems: Promise<ContractItemEntity[]>

  @OneToMany(() => RfqDetailsEntity, (p) => p.materialGroup)
  rfqDetails: Promise<RfqDetailsEntity[]>

  @OneToMany(() => ContractAppendixItemEntity, (p) => p.materialGroup)
  contractAppendixItems: Promise<ContractAppendixItemEntity[]>

  @OneToMany(() => BidPriceEntity, (p) => p.materialGroup)
  bidPrices: Promise<BidPriceEntity[]>
}
