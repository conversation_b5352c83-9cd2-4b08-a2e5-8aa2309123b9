import { ShipmentConfigTemplateService } from './shipmentConfigTemplate.service'
import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import { FlowApproveModule } from '../flowApprove/flowApprove.module'
import { ShipmentConfigTemplateDetailRepository, ShipmentConfigTemplateRepository } from '../../repositories/shipmentConfigTemplate.repository'
import { EmployeeRepository, SupplierRepository } from '../../repositories'
import { ShipmentConfigTemplateController } from './shipmentConfigTemplate.controller'
import { ShipmentFeeConditionsRepository } from '../../repositories/shipmentTemplate.repository'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      ShipmentConfigTemplateRepository,
      ShipmentFeeConditionsRepository,
      ShipmentConfigTemplateDetailRepository,
      SupplierRepository,
      EmployeeRepository,
    ]),
    FlowApproveModule,
  ],
  controllers: [ShipmentConfigTemplateController],
  providers: [ShipmentConfigTemplateService],
  exports: [ShipmentConfigTemplateService],
})
export class ShipmentConfigTemplateModule {}
