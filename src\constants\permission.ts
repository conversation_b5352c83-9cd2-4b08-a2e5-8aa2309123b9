export const RoleDataPermission = {
  ReportSupplier: {
    code: 'ReportSupplier',
    name: 'BC NCC',
    path: '/bid/report-supplier',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  ReportExpertise: {
    code: 'ReportExpertise',
    name: 'BC thẩm định',
    path: '/bid/report-expertise',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  ReportBid: {
    code: 'ReportBid',
    name: 'BC gói thầu',
    path: '/bid/report-bid',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  PurchaseRequestList: {
    code: 'PurchaseRequestList',
    name: '<PERSON> y<PERSON><PERSON> c<PERSON>u mua hàng',
    path: '/pr/pr',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  PurchaseRequestPRList: {
    code: 'PurchaseRequestPRList',
    name: '<PERSON><PERSON> s<PERSON>ch PR tổng hợp',
    path: '/pr/pr-total',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  PurchaseRequestPRAll: {
    code: 'PurchaseRequestPRAll',
    name: 'Tổng hợp PR',
    path: '/pr/pr-total-create',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  PurchaseRequestPRSync: {
    code: 'PurchaseRequestPRSync',
    name: 'Đồng bộ PR',
    path: '/pr/pr-sap',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  Reservation: {
    code: 'Reservation',
    name: 'Nhu cầu sử dụng',
    path: '/reservation/reservation',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  ReservationNorm: {
    code: 'ReservationNorm',
    name: 'Định mức nhu cầu sử dụng',
    path: '/reservation/reservation-norm',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  PurchaseRequestReservationList: {
    code: 'PurchaseRequestReservationList',
    name: 'Danh sách NCSD tổng hợp',
    path: '/reservation/reservation-total',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  PurchaseRequestReservationAll: {
    code: 'PurchaseRequestReservationAll',
    name: 'Tổng hợp NCSD',
    path: 'reservation/reservation-total-create',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  ManagementSupSupplier: {
    code: 'ManagementSupSupplier',
    name: 'Quản lý nhà cung cấp',
    path: '/bid/supplier-manage',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  SupplierCapacity: {
    code: 'SupplierCapacity',
    name: 'Pháp lý/Năng Lực',
    path: '/bid/supplier-capacity',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  SupplierNumberSettingRole: {
    code: 'SupplierNumberSettingRole',
    name: 'Phân quyền nhập liệu',
    path: '/bid/supplier-number-setting-role',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  SupplierCreateCode: {
    code: 'SupplierCreateCode',
    name: 'Tạo mã SAP',
    path: '/bid/supplier-create-code',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  ApproveRequestUpdateLawSupplier: {
    code: 'ApproveRequestUpdateLawSupplier',
    name: 'Thông tin chung',
    path: '/bid/approve-request-update-law-supplier',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  approveRequestUpdateCapacitySupplier: {
    code: 'approveRequestUpdateCapacitySupplier',
    name: 'Lĩnh vực kinh doanh',
    path: '/bid/approve-request-update-capacity-supplier',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  ApproveRequestActiveSupplierService: {
    code: 'ApproveRequestActiveSupplierService',
    name: 'Mở/khóa LVKD',
    path: '/bid/approve-request-active-supplier-service',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  ApproveRequestActiveSupplier: {
    code: 'ApproveRequestActiveSupplier',
    name: 'Mở/khóa NCC',
    path: '/bid/approve-request-active-supplier',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  // SupplierPotentialUpgrade: {
  //   code: 'SupplierPotentialUpgrade',
  //   name: 'Nâng cấp lên NCC tiềm năng',
  //   path: '/bid/supplier-potential-upgrade',
  //   permissionGroupIds: [],
  //   view: false,
  //   edit: false,
  //   delete: false,
  //   add: false,
  //   watchAnother: false,
  //   editAnother: false,
  // },

  SupplierOfficialUpgrade: {
    code: 'SupplierOfficialUpgrade',
    name: 'Nâng cấp lên NCC chính thức',
    path: '/bid/supplier-official-upgrade',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  SiteAssessment: {
    code: 'SiteAssessment',
    name: 'Đánh giá hiện trường',
    path: '/bid/site-assessment',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  EvaluationHistoryPurchase: {
    code: 'EvaluationHistoryPurchase',
    name: 'Lịch sử mua hàng',
    path: '/bid/evaluation-history-purchase',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  BidNew: {
    code: 'BidNew',
    name: 'Tạo gói thầu',
    path: '/bid/bid-new',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  BidRate: {
    code: 'BidRate',
    name: 'Đánh giá gói thầu',
    path: '/bid/bid-rate',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  BidInfo: {
    code: 'BidInfo',
    name: 'Truy vấn gói thầu',
    path: '/bid/bid-info',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  BidNewSurvey: {
    code: 'BidNewSurvey',
    name: 'Gói thầu khảo sát',
    path: '/bid/bid-new-survey',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  PriceQuoteList: {
    code: 'PriceQuoteList',
    name: 'Quản lý yêu cầu báo giá',
    path: '/price-quote/list',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  BidAuction: {
    code: 'BidAuction',
    name: 'Đấu giá',
    path: '/bid/auction',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  BusinessPlan: {
    code: 'BusinessPlan',
    name: 'Phương án kinh doanh',
    path: '/business-plan',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  RecommendedPurchase: {
    code: 'RecommendedPurchase',
    name: 'Đề nghị mua hàng',
    path: '/recommended-purchase',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  RoundUpCont: {
    code: 'RoundUpCont',
    name: 'Làm tròn cont',
    path: '/round-up-cont',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  Contract: {
    code: 'Contract',
    name: 'Hợp đồng',
    path: '/contract/contract',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  Po: {
    code: 'Po',
    name: 'PO',
    path: '/po/po',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  Inbound: {
    code: 'Inbound',
    name: 'Danh sách Inbound',
    path: '/inbound/inbound',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  ShipmentSchedule: {
    code: 'ShipmentSchedule',
    name: 'Lịch giao hàng',
    path: '/inbound/shipment-schedule',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  Bill: {
    code: 'Bill',
    name: 'Hóa đơn',
    path: '/bill',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  Payment: {
    code: 'Payment',
    name: 'Thanh toán',
    path: '/payment',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  Complaint: {
    code: 'Complaint',
    name: 'Danh sách khiếu nại',
    path: '/complaint/complaint',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  Shipment: {
    code: 'Shipment',
    name: 'Shipment',
    path: '/shipment/shipment',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  ShipmentSap: {
    code: 'ShipmentSap',
    name: 'Đồng bộ shipment sap',
    path: '/shipment/shipment/shipment-sap',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  ShipmentCost: {
    code: 'ShipmentCost',
    name: 'Shipment cost',
    path: '/shipment/shipment-cost',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  Budget: {
    code: 'Budget',
    name: 'Đề xuất điều chỉnh ngân sách',
    path: '/budget',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  TicketEvaluationKpi: {
    code: 'TicketEvaluationKpi',
    name: 'Phiếu đánh giá KPI mua hàng',
    path: '/purchase-kpi/ticket-evaluation-kpi',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  Country: {
    code: 'Country',
    name: 'Quốc gia',
    path: '/setting/country',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  Region: {
    code: 'Region',
    name: 'Tỉnh thành',
    path: '/setting/region',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  ServiceTemplate: {
    code: 'ServiceTemplate',
    name: 'Template mua hàng',
    path: '/setting/service-template',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  SupplierTemplate: {
    code: 'SupplierTemplate',
    name: 'Template đánh giá NCC',
    path: '/setting/supplier-template',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  RoundUpContTemplate: {
    code: 'RoundUpContTemplate',
    name: 'Template Làm Tròn Cont',
    path: '/setting/round-up-cont-template',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  BusinessPlanTemplate: {
    code: 'BusinessPlanTemplate',
    name: 'Template PAKD',
    path: '/setting/business-plan-template',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  RecommendedPurchaseTemplate: {
    code: 'RecommendedPurchaseTemplate',
    name: 'Template ĐNMH',
    path: '/setting/recommended-purchase-template',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  PriceList: {
    code: 'PriceList',
    name: 'Thiết lập bảng giá PO',
    path: '/setting/price-list',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  PurchaseKpi: {
    code: 'PurchaseKpi',
    name: 'Template KPI mua hàng',
    path: '/purchase-kpi/purchase-kpi',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  Procedure: {
    code: 'Procedure',
    name: 'Procedure',
    path: '/setting/procedure',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  ConditionTypeMaster: {
    code: 'ConditionTypeMaster',
    name: 'Condition type',
    path: '/setting/condition-type-master',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  SupplierSchema: {
    code: 'SupplierSchema',
    name: 'Supplier Schema',
    path: '/setting/supplier-schema',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  PurchasingOrgSchema: {
    code: 'PurchasingOrgSchema',
    name: 'Purchasing Org Schema',
    path: '/setting/purchasing-org-schema',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  Schema: {
    code: 'Schema',
    name: 'Bảng Schema PO',
    path: '/setting/schema',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  Company: {
    code: 'Company',
    name: 'Danh sách công ty',
    path: '/setting/company',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  Plant: {
    code: 'Plant',
    name: 'Danh sách nhà máy',
    path: '/setting/plant',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  Block: {
    code: 'Block',
    name: 'Danh sách khối',
    path: '/setting/block',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  Department: {
    code: 'Department',
    name: 'Danh sách phòng ban',
    path: '/setting/department',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  Part: {
    code: 'Part',
    name: 'Danh sách bộ phận',
    path: '/setting/part',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  Position: {
    code: 'Position',
    name: 'Danh sách vị trí',
    path: '/setting/position',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  Organizational: {
    code: 'Organizational',
    name: 'Sơ đồ tổ chức',
    path: '/setting/organizational',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  PurchasingOrg: {
    code: 'PurchasingOrg',
    name: 'Tổ chức mua hàng',
    path: '/setting/purchasing-org',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  PurchasingGroup: {
    code: 'PurchasingGroup',
    name: 'Nhóm mua hàng',
    path: '/setting/purchasing-group',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  currency: {
    code: 'Currency',
    name: 'Tiền tệ',
    path: '/setting/currency',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  Incoterm: {
    code: 'Incoterm',
    name: 'Điều kiện thương mại',
    path: '/setting/incoterm',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  PaymentTerm: {
    code: 'PaymentTerm',
    name: 'Thời hạn thanh toán',
    path: '/setting/payment-term',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  PaymentMethod: {
    code: 'PaymentMethod',
    name: 'Phương thức thanh toán',
    path: '/setting/payment-method',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  PlanningGroup: {
    code: 'PlanningGroup',
    name: 'Dòng tiền',
    path: '/setting/planning-group',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  ShareholderCategory: {
    code: 'ShareholderCategory',
    name: 'Danh mục cổ đông',
    path: '/setting/shareholder-category',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  BusinessPartnerGroup: {
    code: 'BusinessPartnerGroup',
    name: 'Nhóm đối tác kinh doanh',
    path: '/setting/business-partner-group',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  BusinessType: {
    code: 'BusinessType',
    name: 'Loại hình doanh nghiệp',
    path: '/setting/business-type',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  BillLookup: {
    code: 'BillLookup',
    name: 'Tra cứu hóa đơn',
    path: '/setting/bill-lookup',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  Employee: {
    code: 'Employee',
    name: 'Nhân viên',
    path: '/setting/employee',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  PermissionAdditional: {
    code: 'PermissionAdditional',
    name: 'Phân quyền bổ sung',
    path: '/setting/permission-additional',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  FlowApprove: {
    code: 'FlowApprove',
    name: 'Chiến lược duyệt',
    path: '/setting/flow-approve',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  OrganizationalPermission: {
    code: 'OrganizationalPermission',
    name: 'Phân quyền mặc định',
    path: '/setting/organizational-permission',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  Uom: {
    code: 'Uom',
    name: 'UOM',
    path: '/setting/uom',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  MaterialType: {
    code: 'MaterialType',
    name: 'Material type',
    path: '/setting/material-type',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  ExternalMaterialGroup: {
    code: 'ExternalMaterialGroup',
    name: 'External Material Group',
    path: '/setting/external-material-group',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  MatGroup: {
    code: 'MatGroup',
    name: 'Material Group',
    path: '/setting/mat-group',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  Material: {
    code: 'Material',
    name: 'Material',
    path: '/pr/material',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  MaterialUOM: {
    code: 'MaterialUOM',
    name: 'MaterialUOM',
    path: '/setting/material-uom',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  Service: {
    code: 'Service',
    name: 'Lĩnh vực mua hàng',
    path: '/setting/service',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  ShipmentRoute: {
    code: 'ShipmentRoute',
    name: 'Shipment route',
    path: '/shipment/shipment-route',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  ShipmentCostType: {
    code: 'ShipmentCostType',
    name: 'Shipment cost type',
    path: '/shipment/shipment-cost-type',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  Bank: {
    code: 'Bank',
    name: 'Ngân hàng',
    path: '/setting/bank',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  BankBranch: {
    code: 'BankBranch',
    name: 'Chi nhánh ngân hàng',
    path: '/setting/bank-branch',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  GlAccount: {
    code: 'GlAccount',
    name: 'GL account',
    path: '/setting/gl-account',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  CurrencyExchange: {
    code: 'CurrencyExchange',
    name: 'CurrencyExchange',
    path: '/setting/currency-exchange',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  MasterBidForm: {
    code: 'MasterBidForm',
    name: 'Hình thức đấu thầu',
    path: '/setting/master-bid-form',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  MasterBidGuaranteeForm: {
    code: 'MasterBidGuaranteeForm',
    name: 'Bảo lãnh dự thầu',
    path: '/setting/master-bid-guarantee-form',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  Client: {
    code: 'Client',
    name: 'Trang tạo HS thầu',
    path: '/setting/client',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  EmailTemplate: {
    code: 'EmailTemplate',
    name: 'Template email',
    path: '/setting/email-template',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  FaqCategory: {
    code: 'FaqCategory',
    name: 'Danh mục FAQ',
    path: '/setting/faq-category',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  Faq: {
    code: 'Faq',
    name: 'FAQ',
    path: '/setting/faq',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  LanguageKey: {
    code: 'LanguageKey',
    name: 'Thiết lập ngôn ngữ',
    path: '/setting/language-key',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  CompanyInvite: {
    code: 'CompanyInvite',
    name: 'Công ty mời thầu',
    path: '/setting/company-invite',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  CompanyAddress: {
    code: 'CompanyAddress',
    name: 'Địa chỉ',
    path: '/setting/company-address',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  SettingString: {
    code: 'SettingString',
    name: 'Thiết lập động',
    path: '/setting/setting-string',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  PlanSiteAssessment: {
    code: 'PlanSiteAssessment',
    name: 'Kế hoạch đánh giá hiện trường',
    path: '/bid/plan-site-assessment',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
  PlanSiteAssessmentTab2: {
    code: 'PlanSiteAssessmentTab2',
    name: 'Điều chỉnh thông tin',
    path: '/bid/plan-site-assessment-tab2',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  PlanSiteAssessmentTab3: {
    code: 'PlanSiteAssessmentTab3',
    name: 'Khóa/Mở khóa Thông tin',
    path: '/bid/plan-site-assessment-tab3',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  serviceManager: {
    code: 'serviceManager',
    name: 'Danh sách LVKD',
    path: '/bid/service-manager',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  shipmentCondition: {
    code: 'shipmentCondition',
    name: 'Template shipment',
    path: '/shipment/shipment-condition',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  SynchronizingLog: {
    code: 'SynchronizingLog',
    name: 'Synchronizing Log',
    path: '/setting/synchronizin-log',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },

  TemplateContract: {
    code: 'TemplateContract',
    name: 'Danh sách template hợp đồng',
    path: '/template/template',
    permissionGroupIds: [],
    view: false,
    edit: false,
    delete: false,
    add: false,
    watchAnother: false,
    editAnother: false,
  },
}
