/*
https://docs.nestjs.com/providers#services
*/

import { Injectable } from '@nestjs/common'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import { ShipmentCostStreamEntity } from '../../entities/shipmentCostStream.entity'
import { Like } from 'typeorm'
import { ShipmentCostStreamRepository } from '../../repositories/shipmentCostStream.repository'
import { enumData } from '../../constants'

@Injectable()
export class ShipmentCostStreamService {
  constructor(private readonly repo: ShipmentCostStreamRepository) {}
  async save(user: UserDto, data: any) {
    const countCode = (await this.repo.count()) + 1
    const entity = new ShipmentCostStreamEntity()
    entity.name = data.name
    entity.description = data.description
    entity.code = 'SCT' + countCode
    entity.price = data.price
    entity.createdBy = user?.id
    entity.status = enumData.ShipmentCostStreamStatus.Active.code
    const res = await this.repo.save(entity)
    return res
  }

  async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { isDeleted: false }
    // if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    let res: any = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'ASC' },
    })
    for (const item of res[0]) {
      if ((item.status = enumData.ShipmentCostStreamStatus.Active.code)) {
        item.statusName = enumData.ShipmentCostStreamStatus.Active.name
        item.color = enumData.ShipmentCostStreamStatus.Active.color
        item.statusBgColor = enumData.ShipmentCostStreamStatus.Active.backgroundColor
        item.statusBorderColor = enumData.ShipmentCostStreamStatus.Active.statusBorderColor
      }
    }

    return res
  }

  async find(user: UserDto, data: any) {
    const whereCon: any = { isDeleted: false }
    let res: any = await this.repo.find({
      where: whereCon,
      order: { createdAt: 'ASC' },
    })
    return res
  }

  async update(user: UserDto, data: any) {
    const existingEntity = await this.repo.findOne({ where: { id: data.id } })
    if (!existingEntity) {
      throw new Error(`Không tìm thấy phí với ID: ${data.id}`)
    }
    const dataToUpdate = new ShipmentCostStreamEntity()
    dataToUpdate.status = data.status
    dataToUpdate.code = data.code
    dataToUpdate.price = data.price
    dataToUpdate.description = data.description
    dataToUpdate.updatedBy = user.id
    await this.repo.update(data.id, dataToUpdate)
    return { message: 'Cập nhật thành công' }
  }

  async delete(user: UserDto, data: FilterOneDto) {
    const existingEntity: any = await this.repo.findOne({ where: { id: data.id } })
    if (!existingEntity) {
      throw new Error(`Không tìm thấy điều kiện vận chuyển với ID: ${data.id}`)
    }
    /* Cập nhật trạng thái isDeleted */
    await this.repo.update(data.id, { isDeleted: true, updatedBy: user.id })
    return { message: 'Xóa thành công' }
  }
}
