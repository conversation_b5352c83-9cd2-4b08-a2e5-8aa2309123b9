import { Module } from '@nestjs/common'
import { TypeOrmModule } from '@nestjs/typeorm'
import { ShipmentFeeConditionsListEntity } from '../../entities/shipmentFeeConditionsList.entity'
import { ShipmentFeeConditionsListService } from './shipmentFeeConditionsList.service'
import { ShipmentFeeConditionsListController } from './shipmentFeeConditionsList.controller'
import { TypeOrmExModule } from '../../typeorm'
import {
  ShipmentFeeConditionsListRepository,
  ShipmentFeeConditionsRepository,
  ShipmentFeeConditionsToListRepository,
} from '../../repositories/shipmentTemplate.repository'
import { ShipmentPlanNumberEntity } from '../../entities/shipmentPlanNumber.entity'
import { ShipmentPlanNumberRepository } from '../../repositories/shipmentPlan.repository'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      ShipmentFeeConditionsListRepository,
      ShipmentFeeConditionsRepository,
      ShipmentFeeConditionsToListRepository,
      ShipmentPlanNumberRepository,
    ]),
  ],
  providers: [ShipmentFeeConditionsListService],
  controllers: [ShipmentFeeConditionsListController],
  exports: [ShipmentFeeConditionsListService],
})
export class ShipmentFeeConditionsListModule {}
