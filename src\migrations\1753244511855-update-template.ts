import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateTemplate1753244511855 implements MigrationInterface {
  name = 'UpdateTemplate1753244511855'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_group_plan" ADD "title" varchar(255)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_group_plan" DROP COLUMN "title"`)
  }
}
