import { Injectable, NotFoundException, BadRequestException, MethodNotAllowedException, NotAcceptableException } from '@nestjs/common'
import { v4 as uuidv4 } from 'uuid'
import { ERROR_YOU_DO_NOT_HAVE_PERMISSION, ERROR_NOT_FOUND_DATA, UPDATE_SUCCESS, CREATE_SUCCESS } from '../../constants'
import { EmailService } from '../email/email.service'
import {
  EmployeeRepository,
  BidTypeRepository,
  SettingStringRepository,
  BidRepository,
  BidEmployeeAccessRepository,
  BidSupplierRepository,
} from '../../repositories'
import * as moment from 'moment'
import { coreHelper } from '../../helpers'
import { enumData } from '../../constants/enumData'
import { UserDto } from '../../dto'
import { In, IsNull, Like, Repository } from 'typeorm'
import { Bid<PERSON>reateDto, BidUpdateDto } from './dto'
import {
  BidCustomPriceEntity,
  BidEmployeeAccessEntity,
  BidEntity,
  BidHistoryEntity,
  BidPriceColEntity,
  BidPriceColValueEntity,
  BidPriceEntity,
  BidPriceListDetailEntity,
  BidSupplierEntity,
  BidTechEntity,
  BidTechListDetailEntity,
  BidTradeEntity,
  BidTradeListDetailEntity,
  EmployeeEntity,
  MediaFileEntity,
  PrEntity,
  PrItemEntity,
  ServiceEntity,
  ServiceTechEntity,
} from '../../entities'

import { BidPrEntity } from '../../entities/bidPr.entity'
import { BidPrItemEntity } from '../../entities/bidPrItem.entity'
import { FlowApproveService } from '../flowApprove/flowApprove.service'
import { BidShipmentPriceEntity } from '../../entities/bidShipmentPrice.entity'
import { BidExMatGroupEntity } from '../../entities/bidExgroup.entity'

@Injectable()
export class BidCreateService {
  constructor(
    private readonly repo: BidRepository,
    private readonly emailService: EmailService,
    private readonly employeeRepo: EmployeeRepository,
    private readonly settingStringRepo: SettingStringRepository,
    private readonly bidTypeRepo: BidTypeRepository,
    private readonly bidEmployeeAccessRepo: BidEmployeeAccessRepository,

    private readonly bidSupplierRepo: BidSupplierRepository,
    private flowService: FlowApproveService,
  ) {}

  //#region create Bid

  /** Tạo gói thầu */
  async createBid(user: UserDto, data: BidCreateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const dateNow = new Date()
    // if (data.listItem.length == 0) throw new BadRequestException('Vui lòng thiết lập danh sách Item.')

    const codeTemp = moment(dateNow).format('YYYYMM')
    const lastSort = await this.findLastSort(user, this.repo, codeTemp + '_')
    const sortString = ('00' + (lastSort + 1)).slice(-3)
    data.code = codeTemp + '_' + sortString
    data.publicDate = dateNow
    data.startBidDate = new Date(data.submitEndDate)
    data.startBidDate.setDate(data.startBidDate.getDate() + 1)
    data.startBidDate.setHours(8, 0, 0, 0)
    if (data.startBidDate >= data.timeCheckTechDate && !data.isSurvey) {
      throw new BadRequestException('Thời điểm mở thầu phải sớm hơn thời hạn đánh giá yêu cầu kỹ thuật, năng lực.')
    }

    return this.repo.manager.transaction(async (manager) => {
      const bidRepo = manager.getRepository(BidEntity)
      const bidPrRepo = manager.getRepository(BidPrEntity)
      const bidExGroupRepo = manager.getRepository(BidExMatGroupEntity)
      const serviceRepo = manager.getRepository(ServiceEntity)
      const bidItemRepo = manager.getRepository(BidPrItemEntity)
      const bidShipmentPriceRepo = manager.getRepository(BidShipmentPriceEntity)
      const bidEmployeeAccessRepo = manager.getRepository(BidEmployeeAccessEntity)
      const bidHistoryRepo = manager.getRepository(BidHistoryEntity)
      const prRepo = manager.getRepository(PrEntity)
      const mediaFileRepo = manager.getRepository(MediaFileEntity)

      let bidId = null

      let lstPrItem = []
      data.prId = [data.prId]
      if (data.prId?.length > 0 && !data.isSurvey) {
        const lstPr: any = await prRepo.find({
          // where: { id: In(data.prId), prItems: { isDeleted: false } },
          where: { id: In(data.prId), prItems: { isDeleted: false } },
          relations: { prItems: true },
        })
        if (
          data.prId.length > 0 &&
          lstPr.length !== data.prId.length &&
          data.biddingTypeCode !== enumData.BiddingType.SHIPPING.code &&
          data.prId[0] !== null
        )
          throw new Error(`Không tìm thấy PR.`)
        for (const pr of lstPr) {
          if (pr.isDeleted) throw new Error(`PR [${pr.code}] đã ngưng hoạt động.`)
          lstPrItem = pr.__prItems__ || []
          if (lstPrItem.length == 0 && !data.isSurvey) throw new Error(`PR [${pr.code}] không có Item`)
        }
      }

      const bid = new BidEntity()
      bid.id = uuidv4()
      bid.companyId = data.companyId
      bid.createdBy = user.id
      bid.code = data.code
      bid.name = data.name
      // TODO
      // từ exmatgroup
      let serviceId: string
      let serviceObj: any
      if (data.exMatGroupId) {
        const serviceEn = await serviceRepo.find({ where: { externalMaterialGroupId: data.exMatGroupId.toLowerCase(), isDeleted: false } })
        if (serviceEn[0]) {
          serviceId = serviceEn[0].id
          serviceObj = serviceEn[0]
        }
      }

      if (serviceId) bid.serviceId = serviceId
      // bid.prId = data.prId
      if (data.isSurvey) bid.isSurvey = data.isSurvey
      bid.exMatGroupId = data.exMatGroupId
      bid.serviceInvite = data.serviceInvite
      bid.skipApprove = data.skipApprove
      bid.shipmentId = data.shipmentId
      bid.acceptEndDate = data.acceptEndDate
      bid.isCompleteAll = data.isCompleteAll
      bid.biddingTypeCode = data.biddingTypeCode
      bid.plantId = data.plantId
      bid.pmOrder = data.pmOrder
      bid.businessPlantId = data.businessPlanId

      bid.isLoadFromBusinessPlan = data.isLoadFromBusinessPlan
      bid.refType = data.refType
      bid.isGetFromPr = data.isGetFromPr
      bid.shipmentId = data.shipmentId
      bid.submitEndDate = data.submitEndDate
      bid.addressSubmit = data.addressSubmit ? data.addressSubmit : ''
      bid.companyInvite = data.companyInvite
      bid.companyInviteId = data.companyInviteId
      bid.serviceInvite = data.serviceInvite
      bid.listAddress = data.listAddress ? data.listAddress : ''
      bid.publicDate = data.publicDate
      bid.bidTypeId = data.bidTypeId
      bid.timeserving = data.timeserving
      bid.scoreDLC = data.scoreDLC
      bid.startBidDate = data.startBidDate
      bid.moneyGuarantee = data.moneyGuarantee
      bid.timeGuarantee = data.timeGuarantee
      bid.masterBidGuaranteeId = data.masterBidGuaranteeId
      bid.timeTechDate = data.timeTechDate
      bid.timePriceDate = data.timePriceDate
      bid.timeCheckTechDate = data.timeCheckTechDate
      bid.timeCheckPriceDate = data.timeCheckPriceDate
      bid.status = data.isSurvey ? enumData.BidStatus.DangCauHinhGoiThau.code : enumData.BidStatus.GoiThauTam.code
      if (bid.skipApprove || data.isSurvey) bid.status = enumData.BidStatus.DangCauHinhGoiThau.code
      bid.isShowHomePage = data.isShowHomePage
      bid.isSendEmailInviteBid = data.isSendEmailInviteBid
      bid.isAutoBid = data.isAutoBid
      bid.fileDrawing = data.fileDrawing
      bid.fileJD = data.fileJD
      bid.fileKPI = data.fileKPI
      bid.fileRule = data.fileRule
      bid.fileDocument = data.fileDocument
      bid.fileAnother = data.fileAnother

      bid.statusTech = enumData.BidTechStatus.DangTao.code
      bid.statusTrade = enumData.BidTradeStatus.DangTao.code
      bid.statusPrice = enumData.BidPriceStatus.DangTao.code
      bid.statusRateTech = enumData.BidTechRateStatus.ChuaTao.code
      bid.statusRateTrade = enumData.BidTradeRateStatus.ChuaTao.code
      bid.statusRatePrice = enumData.BidPriceRateStatus.ChuaTao.code
      bid.status = enumData.BidStatusNew.N.code

      const bidEntity = await bidRepo.save(bid)
      bidId = bidEntity.id
      //#region "Language"
      const createLanData: any = bidEntity
      createLanData.nameEN = data.nameEN

      const languageObj = await coreHelper.checkENValue([createLanData], BidEntity.name)

      await this.repo.saveOrUpdateTranslate(languageObj)
      //#endregion "Language"

      if (data.prId) {
        for (const pr of data.prId) {
          const newPr = new BidPrEntity()
          newPr.bidId = bidEntity.id
          newPr.prId = pr
          await bidPrRepo.save(newPr)
        }
      }
      /* Xử lý khi có lstBidPriceId */
      if (data.lstPriceShipment && data.lstPriceShipment.length > 0 && data.biddingTypeCode === enumData.BiddingType.SHIPPING.code) {
        bid.statusPrice = enumData.BidPriceStatus.DaDuyet.code
        for (const shipment of data.lstPriceShipment) {
          const bidShipment = new BidShipmentPriceEntity()
          bidShipment.bidId = bidEntity.id
          bidShipment.shipmentPriceId = shipment.id
          bidShipment.value = 0
          await bidShipmentPriceRepo.insert(bidShipment)
        }
      }
      // Xử lý khi có data.listItem, mỗi item là 1 bid mới có parentId là bidEntity.id
      let lastSort = 0

      if (data.biddingTypeCode === enumData.BiddingType.SHIPPING.code) {
        const itemEx = new BidExMatGroupEntity()
        itemEx.bidId = bidEntity.id

        const bidExGroup = await bidExGroupRepo.save(itemEx)

        if (data.lstPriceShipment.length !== 0) {
          for (const item of data.lstPriceShipment) {
            // Copy thông tin gói thầu
            const bidItem = new BidPrItemEntity()

            bidItem.bidId = bidEntity.id
            if (serviceId) {
              bidItem.serviceId = serviceId
            }
            bidItem.conditionType = item.conditionType
            bidItem.description = item.description
            bidItem.amount = item.amount
            bidItem.crcy = item.crcy
            bidItem.per = item.per
            bidItem.conditionValue = item.conditionValue
            bidItem.curr = item.curr
            bidItem.currency = item.crcy
            bidItem.cConDe = item.cConDe
            bidItem.bidExgroupId = bidExGroup.id
            bidItem.numCCo = item.numCCo
            // bidItem.p = item.serviceId
            bidItem.quantityItem = item.amount
            bidItem.quantity = item.amount
            bidItem.percentTech = item.percentTech || 0
            bidItem.percentTrade = item.percentTrade || 0
            bidItem.percentPrice = item.percentPrice || 0
            // bidItem.prItemId = item.prItemId || item.id
            if (data.biddingTypeCode === enumData.BiddingType.SHIPPING.code) bidItem.shipmentPriceId = item.shipmentPriceId

            if (lastSort == 0) {
              //  lastSort = await this.findLastSort(user, bidRepo, bid.code + '/')

              const objData = await bidItemRepo.findOne({
                // where: { code: Like(`%${bid.code }%`), parentId: IsNull() },
                where: { code: Like(`%${bid.code}%`), bidId: bid.id },

                order: { code: 'DESC' },
                select: { id: true, code: true },
              })

              if (!objData) {
                lastSort = 0
              } else {
                const sortString = objData.code.slice(-3)
                lastSort = +sortString
              }
            }
            lastSort++
            const sortString = ('00' + lastSort).slice(-3)
            bidItem.code = bid.code + '/' + sortString
            bidItem.createdBy = user.id
            const bidItemNew = bidItemRepo.create({ ...bidItem })
            const itemCreate = await bidItemRepo.save(bidItemNew)

            /* tạo price */
            const bidPriceRepo = manager.getRepository(BidPriceEntity)
            const itemRepo = manager.getRepository(BidPrItemEntity)
            await itemRepo.update(bidExGroup.id, { currency: item.curr })
            const itemLv1 = new BidPriceEntity()
            itemLv1.companyId = user.companyId
            itemLv1.createdBy = user.id
            itemLv1.bidItemId = itemCreate.id
            itemLv1.bidId = bidId

            if (item.curr) itemLv1.currency = item.curr
            itemLv1.name = item.description
            itemLv1.type = enumData.DataType.String.code
            itemLv1.number = item.amount
            itemLv1.level = 1
            // itemLv1.servicePriceId = servicePrice1.id
            const bidPriceLv1 = await bidPriceRepo.save(itemLv1)
          }
        }
      }

      const dicInsert: any = {}
      if (data.listItem.length !== 0) {
        for (const item of data.listItem) {
          /* tạo ra bid exGr trước */
          if (!dicInsert[item.externalMaterialGroupId]) {
            dicInsert[item.externalMaterialGroupId] = {
              bidId: bidId,
              externalMaterialGroupId: item.externalMaterialGroupId,
              listItem: [],
            }
          }
          // Copy thông tin gói thầu
          const bidItem = new BidPrItemEntity()

          bidItem.bidId = bidEntity.id
          if (serviceId) {
            bidItem.serviceId = serviceId
          }
          bidItem.category = item.category
          bidItem.categoryName = item.categoryName
          bidItem.materialCode = item.materialCode
          bidItem.materialId = item.materialId
          bidItem.shortText = item.shortText
          bidItem.unitName = item.unitName
          bidItem.unitId = item.unitId
          bidItem.materialGroupName = item.materialGroupName
          bidItem.materialGroupId = item.materialGroupId
          // bidItem.p = item.serviceId
          bidItem.quantityItem = item.quantity
          bidItem.quantity = item.quantity
          bidItem.percentTech = item.percentTech || 0
          bidItem.percentTrade = item.percentTrade || 0
          bidItem.percentPrice = item.percentPrice || 0
          bidItem.prItemId = item.prItemId || item.id
          if (data.biddingTypeCode === enumData.BiddingType.SHIPPING.code) bidItem.shipmentPriceId = item.id
          bidItem.code = bid.code + '/' + item.itemNo
          bidItem.createdBy = user.id
          const bidItemNew = bidItemRepo.create({ ...bidItem })
          dicInsert[item.externalMaterialGroupId].listItem.push(bidItemNew)
          //
        }
      }
      /* lưu lại */
      for (const item of Object.values(dicInsert) as { bidId: string; externalMaterialGroupId: string; listItem: any[] }[]) {
        const itemEx = new BidExMatGroupEntity()
        itemEx.bidId = item.bidId
        itemEx.externalMaterialGroupId = item.externalMaterialGroupId

        const bidExGroup = await bidExGroupRepo.save(itemEx)
        for (const dataItem of item.listItem) {
          dataItem.bidExgroupId = bidExGroup.id
          await bidItemRepo.save(dataItem)
        }
      }
      // throw new Error('asdasd')

      //#region PERMISSION
      // MPO
      const mpoAccess = new BidEmployeeAccessEntity()
      mpoAccess.companyId = user.companyId
      mpoAccess.createdBy = user.id
      mpoAccess.bidId = bidEntity.id
      mpoAccess.employeeId = data.mpoId
      mpoAccess.type = enumData.BidRuleType.MPO.code
      await bidEmployeeAccessRepo.save(mpoAccess)

      // Tech
      // for (const id of data.techId) {
      //   const techAccess = new BidEmployeeAccessEntity()
      //   techAccess.companyId = user.companyId
      //   techAccess.createdBy = user.id
      //   techAccess.bidId = bidEntity.id
      //   techAccess.employeeId = id
      //   techAccess.type = enumData.BidRuleType.Tech.code
      //   await bidEmployeeAccessRepo.save(techAccess)
      // }

      data.techMemberId = data.techMemberId || []

      for (const id of data.techMemberId) {
        const techAccess = new BidEmployeeAccessEntity()
        techAccess.companyId = user.companyId
        techAccess.isMember = true
        techAccess.createdBy = user.id
        techAccess.bidId = bidEntity.id
        techAccess.employeeId = id
        techAccess.type = enumData.BidRuleType.Tech.code
        await bidEmployeeAccessRepo.save(techAccess)
      }
      // Member
      data.anotherRoleIds = data.anotherRoleIds || []
      for (const item of data.anotherRoleIds) {
        const anotherAccess = new BidEmployeeAccessEntity()
        anotherAccess.companyId = user.companyId
        anotherAccess.createdBy = user.id
        anotherAccess.bidId = bidEntity.id
        anotherAccess.employeeId = item
        anotherAccess.type = enumData.BidRuleType.Memmber.code
        await bidEmployeeAccessRepo.save(anotherAccess)
      }

      const lstBidPermission = [enumData.FlowCode.BID.code]
      if (!data.isSurvey)
        for (const item of lstBidPermission) {
          let flowType: string
          flowType = item

          await this.flowService.setRoleRule(user, {
            targetId: bidEntity.id,
            target: bidEntity,
            entityName: BidEntity.name,
            flowType: flowType,
          })
        }

      // Other
      data.otherRoleIds = data.otherRoleIds || []
      for (const item of data.otherRoleIds) {
        const otherAccess = new BidEmployeeAccessEntity()
        otherAccess.companyId = user.companyId
        otherAccess.createdBy = user.id
        otherAccess.bidId = bidEntity.id
        otherAccess.employeeId = item
        otherAccess.type = enumData.BidRuleType.Other.code
        await bidEmployeeAccessRepo.save(otherAccess)
      }
      //#endregion

      // Bid History
      const bidHistory = new BidHistoryEntity()
      bidHistory.companyId = user.companyId
      bidHistory.createdBy = user.id
      bidHistory.bidId = bidEntity.id
      bidHistory.employeeId = user.employeeId
      bidHistory.status = enumData.BidHistoryStatus.TaoGoiThau.code
      await bidHistoryRepo.save(bidHistory)

      if (data.lstMediaFile && data.lstMediaFile.length > 0) {
        for (const file of data.lstMediaFile) {
          const newImg = new MediaFileEntity()
          newImg.bidId = bidEntity.id
          newImg.fileUrl = file.fileUrl
          newImg.fileName = file.fileName
          newImg.createdAt = new Date()
          newImg.createdBy = user.id
          await mediaFileRepo.insert(newImg)
        }
      }

      return { message: CREATE_SUCCESS }
    })
  }

  async loadTrade(user: UserDto, bidItemId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    return { message: CREATE_SUCCESS }
  }

  /** Chỉnh sửa thông tin gói thầu */
  async updateBid(user: UserDto, data: BidUpdateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    // const objPermission = await this.checkPermissionMpoEdit(user, data.id)
    // if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    // if (data.listItem.length == 0) throw new BadRequestException('Vui lòng thiết lập danh sách Item.')

    data.startBidDate = new Date(data.submitEndDate)
    data.startBidDate.setDate(data.startBidDate.getDate() + 1)
    data.startBidDate.setHours(8, 0, 0, 0)
    if (data.startBidDate >= data.timeCheckTechDate) {
      throw new BadRequestException('Thời điểm mở thầu phải sớm hơn thời hạn đánh giá yêu cầu kỹ thuật, năng lực.')
    }

    await this.repo.manager.transaction(async (manager) => {
      const bidRepo = manager.getRepository(BidEntity)
      const bidHistoryRepo = manager.getRepository(BidHistoryEntity)
      const prRepo = manager.getRepository(PrEntity)
      const serviceTechRepo = manager.getRepository(ServiceTechEntity)
      const bidShipmentPriceRepo = manager.getRepository(BidShipmentPriceEntity)
      const prItemRepo = manager.getRepository(PrItemEntity)
      const bidItemRepo = manager.getRepository(BidPrItemEntity)
      const serviceRepo = manager.getRepository(ServiceEntity)
      const mediaFileRepo = manager.getRepository(MediaFileEntity)

      const bid = await this.repo.findOne({ where: { id: data.id } })
      if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

      let serviceId: string
      let serviceObj: any
      if (data.exMatGroupId) {
        const serviceEn = await serviceRepo.find({
          where: { externalMaterialGroupId: data.exMatGroupId.toLowerCase(), isDeleted: false },
        })
        if (serviceEn[0]) {
          serviceId = serviceEn[0].id
          serviceObj = serviceEn[0]
        }
      }

      if (serviceId) bid.serviceId = serviceId

      bid.isShowHomePage = data.isShowHomePage
      bid.isSendEmailInviteBid = data.isSendEmailInviteBid
      bid.isAutoBid = data.isAutoBid
      bid.name = data.name
      bid.serviceInvite = data.serviceInvite
      bid.acceptEndDate = data.acceptEndDate
      bid.biddingTypeCode = data.biddingTypeCode
      bid.shipmentId = data.shipmentId
      bid.isLoadFromBusinessPlan = data.isLoadFromBusinessPlan
      bid.submitEndDate = data.submitEndDate
      bid.addressSubmit = data.addressSubmit ?? ''
      bid.companyInvite = data.companyInvite
      bid.isGetFromPr = data.isGetFromPr
      bid.companyId = data.companyId
      bid.listAddress = data.listAddress ?? ''
      bid.plantId = data.plantId
      bid.publicDate = data.publicDate
      bid.pmOrder = data.pmOrder
      bid.businessPlantId = data.businessPlanId
      bid.refType = data.refType
      bid.bidTypeId = data.bidTypeId
      bid.timeserving = data.timeserving
      bid.scoreDLC = data.scoreDLC
      bid.startBidDate = data.startBidDate
      bid.moneyGuarantee = data.moneyGuarantee
      bid.timeGuarantee = data.timeGuarantee
      bid.masterBidGuaranteeId = data.masterBidGuaranteeId
      bid.timeTechDate = data.timeTechDate
      bid.timePriceDate = data.timePriceDate
      bid.timeCheckTechDate = data.timeCheckTechDate
      bid.timeCheckPriceDate = data.timeCheckPriceDate
      bid.serviceInvite = data.serviceInvite
      bid.fileDrawing = data.fileDrawing
      bid.fileJD = data.fileJD
      bid.fileKPI = data.fileKPI
      bid.fileRule = data.fileRule
      bid.fileDocument = data.fileDocument
      bid.fileAnother = data.fileAnother

      bid.updatedBy = user.id

      const bidEntity = await bidRepo.save(bid)
      await mediaFileRepo.delete({ bidId: bid.id })
      if (data.lstMediaFile && data.lstMediaFile.length > 0) {
        for (const file of data.lstMediaFile) {
          const newImg = new MediaFileEntity()
          newImg.bidId = bidEntity.id
          newImg.fileUrl = file.fileUrl
          newImg.fileName = file.fileName
          newImg.createdAt = new Date()
          newImg.createdBy = user.id
          await mediaFileRepo.insert(newImg)
        }
      }

      //#region "Language"
      const createLanData: any = bidEntity
      createLanData.nameEN = data.nameEN

      const languageObj = await coreHelper.checkENValue([createLanData], BidEntity.name)

      await this.repo.saveOrUpdateTranslate(languageObj)
      //#endregion "Language"

      let lastSort = 0

      // /* lấy danh sách tech của bid */
      // const lstBidTech = await manager.getRepository(BidTechEntity).find({ where: { bidId: bid.id } })
      // /* lấy danh sách trade */
      // const lstBidTrade = await manager.getRepository(BidTradeEntity).find({ where: { bidId: bid.id } })
      // /* lấy danh sách price */
      // const lstBidPrice = await manager.getRepository(BidPriceEntity).find({ where: { bidId: bid.id } })
      // /* nếu có thì quăng lỗi đã cấu hình k dc sửa */
      // if (lstBidTech.length > 0 || lstBidTrade.length > 0 || lstBidPrice.length > 0) {
      //   /* xóa hết tech trade */

      // }

      /* xóa hết item  */
      await bidItemRepo.delete({ bidId: bid.id })

      if (data.lstPriceShipment && data.lstPriceShipment.length > 0 && data.biddingTypeCode === enumData.BiddingType.SHIPPING.code) {
        bid.statusPrice = enumData.BidPriceStatus.DaDuyet.code
        for (const shipment of data.lstPriceShipment) {
          const bidShipment = new BidShipmentPriceEntity()
          bidShipment.bidId = bidEntity.id
          bidShipment.shipmentPriceId = shipment.id
          bidShipment.value = 0
          await bidShipmentPriceRepo.insert(bidShipment)
        }
      }
      // Xử lý khi có data.listItem, mỗi item là 1 bid mới có parentId là bidEntity.id

      if (data.biddingTypeCode === enumData.BiddingType.SHIPPING.code) {
        const bidItem = new BidPrItemEntity()

        bidItem.bidId = bidEntity.id
        // bidItem.p = item.serviceId
        bidItem.quantityItem = 0
        if (serviceId) {
          bidItem.serviceId = serviceId
        }
        bidItem.percentTech = serviceObj?.percentTech || 0
        bidItem.percentTrade = serviceObj?.percentTrade || 0
        bidItem.isExmatgroup = true
        bidItem.percentPrice = serviceObj?.percentPrice || 0

        // bidItem.prItemId = serviceObj.id

        if (lastSort == 0) {
          //  lastSort = await this.findLastSort(user, bidRepo, bid.code + '/')

          const objData = await bidItemRepo.findOne({
            // where: { code: Like(`%${bid.code }%`), parentId: IsNull() },
            where: { code: Like(`%${bid.code}%`), bidId: bid.id },
            order: { code: 'DESC' },
            select: { id: true, code: true },
          })

          if (!objData) {
            lastSort = 0
          } else {
            const sortString = objData.code.slice(-3)
            lastSort = +sortString
          }
        }
        lastSort++
        const sortString = ('00' + lastSort).slice(-3)
        bidItem.code = bid.code + '/' + sortString
        bidItem.createdBy = user.id
        const bidItemNew = bidItemRepo.create({ ...bidItem })

        const needToCreate = await bidItemRepo.save(bidItemNew)

        if (data.lstPriceShipment.length !== 0) {
          for (const item of data.lstPriceShipment) {
            // Copy thông tin gói thầu
            const bidItem = new BidPrItemEntity()

            bidItem.bidId = bidEntity.id
            if (serviceId) {
              bidItem.serviceId = serviceId
            }
            bidItem.conditionType = item.conditionType
            bidItem.description = item.description
            bidItem.amount = item.amount
            bidItem.crcy = item.crcy
            bidItem.per = item.per
            bidItem.conditionValue = item.conditionValue
            bidItem.curr = item.curr
            bidItem.cConDe = item.cConDe
            bidItem.numCCo = item.numCCo
            // bidItem.p = item.serviceId
            bidItem.quantityItem = item.amount
            bidItem.quantity = item.amount
            bidItem.percentTech = item.percentTech || 0
            bidItem.percentTrade = item.percentTrade || 0
            bidItem.percentPrice = item.percentPrice || 0
            bidItem.prItemId = item.prItemId || item.id
            if (data.biddingTypeCode === enumData.BiddingType.SHIPPING.code) bidItem.shipmentPriceId = item.shipmentPriceId

            if (lastSort == 0) {
              //  lastSort = await this.findLastSort(user, bidRepo, bid.code + '/')

              const objData = await bidItemRepo.findOne({
                // where: { code: Like(`%${bid.code }%`), parentId: IsNull() },
                where: { code: Like(`%${bid.code}%`), bidId: bid.id },

                order: { code: 'DESC' },
                select: { id: true, code: true },
              })

              if (!objData) {
                lastSort = 0
              } else {
                const sortString = objData.code.slice(-3)
                lastSort = +sortString
              }
            }
            lastSort++
            const sortString = ('00' + lastSort).slice(-3)
            bidItem.code = bid.code + '/' + sortString
            bidItem.createdBy = user.id
            const bidItemNew = bidItemRepo.create({ ...bidItem })
            await bidItemRepo.save(bidItemNew)

            // tạo tech
            {
              const bidTechRepo = manager.getRepository(BidTechEntity)
              const bidTechLstRepo = manager.getRepository(BidTechListDetailEntity)

              const bid = await bidItemRepo.findOne({ where: { id: needToCreate.id, isDeleted: false } })
              let lstServiceTech = await serviceTechRepo.find({
                where: { serviceId: bid.serviceId, parentId: IsNull(), isDeleted: false },
                relations: { childs: { serviceTechListDetails: true }, serviceTechListDetails: true },
                order: { sort: 'ASC', createdAt: 'ASC', childs: { sort: 'ASC', createdAt: 'ASC' } },
              })

              // Tạo danh sách yêu cầu kỹ thuật cho gói thầu theo cấu hình kỹ thuật
              for (const a of lstServiceTech) {
                const item = new BidTechEntity()
                item.companyId = user.companyId
                item.createdBy = user.id
                // item.bidId = bidItemId
                item.sort = a.sort
                item.bidId = bid.bidId
                item.bidItemId = bid.id
                item.name = a.name
                item.isRequired = a.isRequired
                item.type = a.type
                item.percent = a.percent
                item.percentRule = a.percentRule
                item.isCalUp = a.isCalUp
                item.percentDownRule = a.percentDownRule
                item.level = a.level
                item.description = a.description
                item.parentId = a.parentId
                item.scoreDLC = a.scoreDLC
                item.requiredMin = a.requiredMin
                item.isHighlight = a.isHighlight
                item.hightlightValue = a.hightlightValue
                const bidTechEntity = await bidTechRepo.save(item)

                const lstChild = (await a.childs).filter((c) => !c.isDeleted)

                if (lstChild && lstChild.length > 0) {
                  for (const b of lstChild) {
                    const itemChild = new BidTechEntity()
                    itemChild.companyId = user.companyId
                    itemChild.createdBy = user.id
                    itemChild.sort = b.sort
                    item.bidId = bid.bidId
                    item.bidItemId = bid.id
                    itemChild.name = b.name
                    itemChild.isRequired = b.isRequired
                    itemChild.type = b.type
                    itemChild.percent = b.percent
                    itemChild.percentRule = b.percentRule
                    itemChild.isCalUp = b.isCalUp
                    itemChild.percentDownRule = b.percentDownRule
                    itemChild.level = b.level
                    itemChild.description = b.description
                    itemChild.parentId = bidTechEntity.id
                    itemChild.scoreDLC = b.scoreDLC
                    itemChild.requiredMin = b.requiredMin
                    itemChild.isHighlight = b.isHighlight
                    itemChild.hightlightValue = b.hightlightValue
                    const bidTechChildEntity = await bidTechRepo.save(itemChild)

                    const lstDataTypeList = (await b.serviceTechListDetails).filter((c) => !c.isDeleted)
                    if (lstDataTypeList && lstDataTypeList.length > 0) {
                      for (const c of lstDataTypeList) {
                        const itemListDetail = new BidTechListDetailEntity()
                        itemListDetail.companyId = user.companyId
                        itemListDetail.createdBy = user.id
                        itemListDetail.bidTechId = bidTechChildEntity.id
                        itemListDetail.name = c.name
                        itemListDetail.value = c.value
                        await bidTechLstRepo.save(itemListDetail)
                      }
                    }
                  }
                }

                const lstDataTypeList = (await a.serviceTechListDetails).filter((c) => !c.isDeleted)
                if (lstDataTypeList && lstDataTypeList.length > 0) {
                  for (const c of lstDataTypeList) {
                    const itemListDetail = new BidTechListDetailEntity()
                    itemListDetail.companyId = user.companyId
                    itemListDetail.createdBy = user.id
                    itemListDetail.bidTechId = bidTechEntity.id
                    itemListDetail.name = c.name
                    itemListDetail.value = c.value
                    await bidTechLstRepo.save(itemListDetail)
                  }
                }
              }
            }
          }
        }
      }

      if (data.listItem.length !== 0) {
        for (const item of data.listItem) {
          // Copy thông tin gói thầu
          const bidItem = new BidPrItemEntity()

          bidItem.bidId = bid.id
          if (serviceId) {
            bidItem.serviceId = serviceId
          }
          bidItem.category = item.category
          bidItem.categoryName = item.categoryName
          bidItem.materialCode = item.materialCode
          bidItem.materialId = item.materialId
          bidItem.shortText = item.shortText
          bidItem.unitName = item.unitName
          bidItem.unitId = item.unitId
          bidItem.materialGroupName = item.materialGroupName
          bidItem.materialGroupId = item.materialGroupId
          // bidItem.p = item.serviceId
          bidItem.quantityItem = item.quantityCreatedBid
          bidItem.quantity = item.quantity
          bidItem.percentTech = item.percentTech || 0
          bidItem.percentTrade = item.percentTrade || 0
          bidItem.percentPrice = item.percentPrice || 0
          bidItem.prItemId = item.prItemId || item.id

          if (lastSort == 0) {
            //  lastSort = await this.findLastSort(user, bidRepo, bid.code + '/')

            const objData = await bidItemRepo.findOne({
              // where: { code: Like(`%${bid.code }%`), parentId: IsNull() },
              where: { code: Like(`%${bid.code}%`), bidId: bid.id },

              order: { code: 'DESC' },
              select: { id: true, code: true },
            })

            if (!objData) {
              lastSort = 0
            } else {
              const sortString = objData.code.slice(-3)
              lastSort = +sortString
            }
          }
          lastSort++
          const sortString = ('00' + lastSort).slice(-3)
          bidItem.code = bid.code + '/' + sortString
          bidItem.createdBy = user.id
          const bidItemNew = bidItemRepo.create({ ...bidItem })
          await bidItemRepo.save(bidItemNew)

          // for (const pr of data.prId) {
          //   // update lại số lượng đã tạo thầu của item
          //   const lstPrItem = await prItemRepo.find({ where: { prId: pr } })
          //   for (const item of lstPrItem) {
          //     await prItemRepo.update(item.id, { quantityBid: item.quantity, updatedBy: user.id })
          //     // }
          //   }
          // }
        }
        // tạo thêm 1 serviceItem dựa theo ExMatGr
        const bidItem = new BidPrItemEntity()

        bidItem.bidId = bid.id
        // bidItem.p = item.serviceId
        bidItem.quantityItem = 0
        if (serviceId) {
          bidItem.serviceId = serviceId
        }
        bidItem.percentTech = serviceObj?.percentTech || 0
        bidItem.percentTrade = serviceObj?.percentTrade || 0
        bidItem.isExmatgroup = true
        bidItem.percentPrice = serviceObj?.percentPrice || 0
        // bidItem.prItemId = serviceObj.id

        if (lastSort == 0) {
          //  lastSort = await this.findLastSort(user, bidRepo, bid.code + '/')

          const objData = await bidItemRepo.findOne({
            // where: { code: Like(`%${bid.code }%`), parentId: IsNull() },
            where: { code: Like(`%${bid.code}%`), bidId: bid.id },
            order: { code: 'DESC' },
            select: { id: true, code: true },
          })

          if (!objData) {
            lastSort = 0
          } else {
            const sortString = objData.code.slice(-3)
            lastSort = +sortString
          }
        }
        lastSort++
        const sortString = ('00' + lastSort).slice(-3)
        bidItem.code = bid.code + '/' + sortString
        bidItem.createdBy = user.id
        const bidItemNew = bidItemRepo.create({ ...bidItem })
        const needToCreate = await bidItemRepo.save(bidItemNew)
      }

      // Nếu có thay đổi HĐ thầu
      const bidEmployeeAccessRepo = manager.getRepository(BidEmployeeAccessEntity)
      if (data.isChangeEmployee) {
        // Xoá hết hội đồng gói thầu và tạo lại
        await bidEmployeeAccessRepo.delete({ bidId: bid.id })

        // MPO
        const mpoAccess = new BidEmployeeAccessEntity()
        mpoAccess.companyId = user.companyId
        mpoAccess.createdBy = user.id
        mpoAccess.bidId = bid.id
        mpoAccess.employeeId = data.mpoId
        mpoAccess.type = enumData.BidRuleType.MPO.code
        await bidEmployeeAccessRepo.save(mpoAccess)

        // MPO Lead
        // const mpoLeadAccess = new BidEmployeeAccessEntity()
        // mpoLeadAccess.companyId = user.companyId
        // mpoLeadAccess.createdBy = user.id
        // mpoLeadAccess.bidId = bid.id
        // mpoLeadAccess.employeeId = data.mpoLeadId
        // mpoLeadAccess.type = enumData.BidRuleType.MPOLeader.code
        // await bidEmployeeAccessRepo.save(mpoLeadAccess)

        // Tech
        // for (const id of data.techId) {
        //   const techAccess = new BidEmployeeAccessEntity()
        //   techAccess.companyId = user.companyId
        //   techAccess.createdBy = user.id
        //   techAccess.bidId = bid.id
        //   techAccess.employeeId = id
        //   techAccess.type = enumData.BidRuleType.Tech.code
        //   await bidEmployeeAccessRepo.save(techAccess)
        // }

        for (const id of data.techMemberId) {
          const techAccess = new BidEmployeeAccessEntity()
          techAccess.companyId = user.companyId
          techAccess.isMember = true
          techAccess.createdBy = user.id
          techAccess.bidId = bidEntity.id
          techAccess.employeeId = id
          techAccess.type = enumData.BidRuleType.Tech.code
          await bidEmployeeAccessRepo.save(techAccess)
        }

        // Tech Lead
        // const techLeadAccess = new BidEmployeeAccessEntity()
        // techLeadAccess.companyId = user.companyId
        // techLeadAccess.createdBy = user.id
        // techLeadAccess.bidId = bid.id
        // techLeadAccess.employeeId = data.techLeadId
        // techLeadAccess.type = enumData.BidRuleType.TechLeader.code
        // await bidEmployeeAccessRepo.save(techLeadAccess)

        data.anotherRoleIds = data.anotherRoleIds || []
        for (const item of data.anotherRoleIds) {
          const anotherAccess = new BidEmployeeAccessEntity()
          anotherAccess.companyId = user.companyId
          anotherAccess.createdBy = user.id
          anotherAccess.bidId = bid.id
          anotherAccess.employeeId = item
          anotherAccess.type = enumData.BidRuleType.Memmber.code
          await bidEmployeeAccessRepo.save(anotherAccess)
        }

        // Other
        data.otherRoleIds = data.otherRoleIds || []
        for (const item of data.otherRoleIds) {
          const otherAccess = new BidEmployeeAccessEntity()
          otherAccess.companyId = user.companyId
          otherAccess.createdBy = user.id
          otherAccess.bidId = bid.id
          otherAccess.employeeId = item
          otherAccess.type = enumData.BidRuleType.Other.code
          await bidEmployeeAccessRepo.save(otherAccess)
        }
      }

      // Bid History
      const bidHistory = new BidHistoryEntity()
      bidHistory.companyId = user.companyId
      bidHistory.createdBy = user.id
      bidHistory.bidId = bid.id
      bidHistory.employeeId = user.employeeId
      bidHistory.status = enumData.BidHistoryStatus.SuaTaoGoiThau.code
      await bidHistoryRepo.save(bidHistory)

      return { message: UPDATE_SUCCESS }
    })

    // gửi email thông báo Chỉnh sửa thông tin chung của gói thầu thành công (trừ trường hợp gói thầu tạm)
    const bid = await this.repo.findOne({ where: { id: data.id }, select: { status: true } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    if (bid.status != enumData.BidStatus.GoiThauTam.code && bid.status != enumData.BidStatus.ChoDuyetGoiThauTam.code) {
      await this.emailService.updateBidSuccess(data.id)
    }

    return { message: UPDATE_SUCCESS }
  }

  /** Copy gói thầu */
  async copyBid(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    return this.repo.manager.transaction(async (manager) => {
      const bidRepo = manager.getRepository(BidEntity)
      const bidEmployeeAccessRepo = manager.getRepository(BidEmployeeAccessEntity)
      const bidHistoryRepo = manager.getRepository(BidHistoryEntity)
      const bidTechRepo = manager.getRepository(BidTechEntity)
      const bidTechListDetailRepo = manager.getRepository(BidTechListDetailEntity)
      const bidTradeRepo = manager.getRepository(BidTradeEntity)
      const bidTradeListDetailRepo = manager.getRepository(BidTradeListDetailEntity)
      const bidPriceRepo = manager.getRepository(BidPriceEntity)
      const bidPriceListDetailRepo = manager.getRepository(BidPriceListDetailEntity)
      const bidPriceColRepo = manager.getRepository(BidPriceColEntity)
      const bidPriceColValueRepo = manager.getRepository(BidPriceColValueEntity)
      const bidCustomPriceRepo = manager.getRepository(BidCustomPriceEntity)
      const bidSupplierRepo = manager.getRepository(BidSupplierEntity)

      // Gói thầu gốc
      const bidSrc = await bidRepo.findOne({ where: { id: bidId }, relations: { bidPr: true } })
      if (!bidSrc) throw new Error('Không tìm thấy gói thầu cần copy.')
      const prId = await bidSrc.bidPr
      if (prId[0]?.prId) throw new Error('Gói thầu theo PR, không được copy.')

      const dateNow = new Date()

      const codeTemp = moment(dateNow).format('YYYYMM')
      const lastSort = await this.findLastSort(user, bidRepo, codeTemp + '_')
      const sortString = ('00' + (lastSort + 1)).slice(-3)

      let bidCopy: any = bidRepo.create({ ...bidSrc })
      bidCopy.publicDate = new Date()
      // gen lại mã gói thầu mới
      bidCopy.code = codeTemp + '_' + sortString
      // reset lại các trạng thái đánh giá của gói thầu
      bidCopy.statusResetPrice = enumData.BidResetPriceStatus.ChuaTao.code
      bidCopy.statusRateTech = enumData.BidTechRateStatus.ChuaTao.code
      bidCopy.statusRateTrade = enumData.BidTradeRateStatus.ChuaTao.code
      bidCopy.statusRatePrice = enumData.BidPriceRateStatus.ChuaTao.code
      bidCopy.statusChooseSupplier = enumData.BidChooseSupplierStatus.ChuaChon.code
      if (
        bidSrc.statusChooseSupplier == enumData.BidChooseSupplierStatus.TuChoi.code ||
        bidSrc.statusChooseSupplier == enumData.BidChooseSupplierStatus.DaDuyet.code
      ) {
        bidCopy.statusChooseSupplier = enumData.BidChooseSupplierStatus.DaChon.code
      }
      // gán lại các thông tin người tạo gói thầu
      delete bidCopy.id
      delete bidCopy.createdAt
      delete bidCopy.updatedAt
      delete bidCopy.updatedBy
      bidCopy.createdBy = user.id
      // Các trạng thái sau khi gói thầu đã mở
      const lstStatus = [
        enumData.BidStatus.DangNhanBaoGia.code,
        enumData.BidStatus.DangDanhGia.code,
        enumData.BidStatus.DangDuyetDanhGia.code,
        enumData.BidStatus.HoanTatDanhGia.code,
        enumData.BidStatus.DangDamPhanGia.code,
        enumData.BidStatus.DongDamPhanGia.code,
        enumData.BidStatus.DangDauGia.code,
        enumData.BidStatus.DongDauGia.code,
        enumData.BidStatus.DongThau.code,
        enumData.BidStatus.HoanTat.code,
      ]
      if (lstStatus.includes(bidSrc.status)) {
        bidCopy.status = enumData.BidStatus.DangDuyetGoiThau.code
      }
      delete bidCopy.__bidPr__
      bidCopy = await bidRepo.save(bidCopy)

      const lstBidPermission = [
        enumData.FlowCode.BID.code,
        // enumData.FlowCode.BIDTECH.code,
        // enumData.FlowCode.BIDTRADE.code,
        // enumData.FlowCode.BIDPRICE.code,
      ]
      if (!bidCopy.isSurvey)
        for (const item of lstBidPermission) {
          let flowType: string
          flowType = item
          await this.flowService.setRoleRule(user, {
            targetId: bidCopy.id,
            target: bidCopy,
            entityName: BidEntity.name,
            flowType: flowType,
            companyId: user.orgCompanyId,
          })
        }

      //#region Employee Access
      const lstEmployeeAccessSrc = await bidSrc.employeeAccess
      for (const employeeAccessSrc of lstEmployeeAccessSrc) {
        const employeeAccessCopy = bidEmployeeAccessRepo.create({ ...employeeAccessSrc })
        // gán lại gói thầu
        employeeAccessCopy.bidId = bidCopy.id
        delete employeeAccessCopy.id
        delete employeeAccessCopy.createdAt
        delete employeeAccessCopy.updatedAt
        employeeAccessCopy.createdBy = user.id
        await bidEmployeeAccessRepo.save(employeeAccessCopy)
      }
      //#endregion

      //#region Bid Supplier
      const lstBidSupplierSrc = await bidSrc.bidSuppliers
      for (const bidSupplierSrc of lstBidSupplierSrc) {
        const bidSupplierCopy = bidSupplierRepo.create({ ...bidSupplierSrc })
        delete bidSupplierCopy.id
        delete bidSupplierCopy.createdAt
        delete bidSupplierCopy.updatedAt
        delete bidSupplierCopy.updatedBy
        delete bidSupplierCopy.dataJson
        // gán lại gói thầu
        bidSupplierCopy.bidId = bidCopy.id
        // reset các biến đánh giá
        bidSupplierCopy.isTechValid = true
        bidSupplierCopy.isTradeValid = true
        bidSupplierCopy.isPriceValid = true
        bidSupplierCopy.isSuccessBid = false
        bidSupplierCopy.isHighlight = false
        bidSupplierCopy.isNotHaveMinValue = false
        bidSupplierCopy.note = ''
        bidSupplierCopy.noteTech = ''
        bidSupplierCopy.noteTrade = ''
        bidSupplierCopy.notePrice = ''
        bidSupplierCopy.noteTechLeader = ''
        bidSupplierCopy.noteMPOLeader = ''
        bidSupplierCopy.noteSuccessBid = ''
        bidSupplierCopy.scoreTech = 0
        bidSupplierCopy.scoreTrade = 0
        bidSupplierCopy.scorePrice = 0
        bidSupplierCopy.scoreManualTech = 0
        bidSupplierCopy.scoreManualTrade = 0
        bidSupplierCopy.scoreManualPrice = 0
        bidSupplierCopy.status = enumData.BidSupplierStatus.DaDuocChon.code
        bidSupplierCopy.statusTech = enumData.BidSupplierTechStatus.ChuaXacNhan.code
        bidSupplierCopy.statusTrade = enumData.BidSupplierTradeStatus.ChuaXacNhan.code
        bidSupplierCopy.statusPrice = enumData.BidSupplierPriceStatus.ChuaXacNhan.code
        bidSupplierCopy.statusResetPrice = enumData.BidSupplierResetPriceStatus.KhongYeuCau.code
        bidSupplierCopy.createdBy = user.id
        await bidSupplierRepo.save(bidSupplierCopy)
      }
      //#endregion

      //#region Bid History
      const bidHistory = new BidHistoryEntity()
      bidHistory.companyId = user.companyId
      bidHistory.createdBy = user.id
      bidHistory.bidId = bidCopy.id
      bidHistory.employeeId = user.employeeId
      bidHistory.status = enumData.BidHistoryStatus.SaoChepGoiThau.code
      bidHistory.description = `Gói thầu gốc ${bidSrc.code}`
      await bidHistoryRepo.save(bidHistory)
      //#endregion

      //#region Bid Item
      const lstBidItemSrc = await bidSrc.bidPrItem
      let lastSortItem = 0
      for (const bidItemSrc of lstBidItemSrc) {
        lastSortItem++
        const sortString = ('00' + lastSortItem).slice(-3)
        let bidItemCopy = bidRepo.create({ ...bidItemSrc })
        // gen lại mã gói thầu mới
        bidItemCopy.code = bidCopy.code + '_' + sortString
        bidItemCopy.createdBy = user.id
        bidItemCopy.parentId = bidCopy.id
        delete bidItemCopy.id
        delete bidItemCopy.createdAt
        delete bidItemCopy.updatedAt
        delete bidItemCopy.updatedBy
        bidItemCopy = await bidRepo.save(bidItemCopy)

        if (bidItemSrc.isExmatgroup) {
          //#region Bid Tech
          const lstBidTechSrc = await bidItemSrc.techs
          if (lstBidTechSrc.length > 0) {
            const lstTechSrc1 = lstBidTechSrc.filter((c) => c.level == 1)
            for (const tech1Src of lstTechSrc1) {
              let tech1Copy = bidTechRepo.create({ ...tech1Src })
              // gán lại gói thầu
              tech1Copy.bidItemId = bidItemSrc.id
              tech1Copy.createdBy = user.id
              delete tech1Copy.id
              delete tech1Copy.createdAt
              delete tech1Copy.updatedAt
              tech1Copy = await bidTechRepo.save(tech1Copy)

              if (tech1Copy.type === enumData.DataType.List.code) {
                const lstDetailSrc = await tech1Src.bidTechListDetails
                if (lstDetailSrc.length > 0) {
                  for (const detailSrc of lstDetailSrc) {
                    const detailCopy = bidTechListDetailRepo.create({ ...detailSrc })
                    // gán lại bidTechId
                    detailCopy.bidTechId = tech1Copy.id
                    detailCopy.createdBy = user.id
                    delete detailCopy.id
                    delete detailCopy.createdAt
                    delete detailCopy.updatedAt
                    await bidTechListDetailRepo.save(detailCopy)
                  }
                }
              }

              const lstTechSrc2 = lstBidTechSrc.filter((c) => c.parentId == tech1Src.id)
              for (const tech2Src of lstTechSrc2) {
                let tech2Copy = bidTechRepo.create({ ...tech2Src })
                // gán lại gói thầu
                tech2Copy.bidItemId = bidItemSrc.id
                // gán lại parent
                tech2Copy.parentId = tech1Copy.id
                tech2Copy.createdBy = user.id
                delete tech2Copy.id
                delete tech2Copy.createdAt
                delete tech2Copy.updatedAt
                tech2Copy = await bidTechRepo.save(tech2Copy)

                if (tech2Copy.type === enumData.DataType.List.code) {
                  const lstDetailSrc = await tech2Src.bidTechListDetails
                  if (lstDetailSrc.length > 0) {
                    for (const detailSrc of lstDetailSrc) {
                      const detailCopy = bidTechListDetailRepo.create({ ...detailSrc })
                      // gán lại bidTechId
                      detailCopy.bidTechId = tech2Copy.id
                      detailCopy.createdBy = user.id
                      delete detailCopy.id
                      delete detailCopy.createdAt
                      delete detailCopy.updatedAt
                      await bidTechListDetailRepo.save(detailCopy)
                    }
                  }
                }
              }
            }
          }
          //#endregion

          //#region Bid Trade
          const lstBidTradeSrc = await bidItemSrc.trades
          if (lstBidTradeSrc.length > 0) {
            const lstTradeSrc1 = lstBidTradeSrc.filter((c) => c.level == 1)
            for (const trade1Src of lstTradeSrc1) {
              let trade1Copy = bidTradeRepo.create({ ...trade1Src })
              // gán lại gói thầu
              trade1Copy.bidItemId = bidItemSrc.id
              trade1Copy.createdBy = user.id
              delete trade1Copy.id
              delete trade1Copy.createdAt
              delete trade1Copy.updatedAt
              trade1Copy = await bidTradeRepo.save(trade1Copy)

              if (trade1Copy.type === enumData.DataType.List.code) {
                const lstDetailSrc = await trade1Src.bidTradeListDetails
                if (lstDetailSrc.length > 0) {
                  for (const detailSrc of lstDetailSrc) {
                    const detailCopy = bidTradeListDetailRepo.create({ ...detailSrc })
                    // gán lại bidTradeId
                    detailCopy.bidTradeId = trade1Copy.id
                    detailCopy.createdBy = user.id
                    delete detailCopy.id
                    delete detailCopy.createdAt
                    delete detailCopy.updatedAt
                    await bidTradeListDetailRepo.save(detailCopy)
                  }
                }
              }

              const lstTradeSrc2 = lstBidTradeSrc.filter((c) => c.parentId == trade1Src.id)
              for (const trade2Src of lstTradeSrc2) {
                let trade2Copy = bidTradeRepo.create({ ...trade2Src })
                // gán lại gói thầu
                trade2Copy.bidItemId = bidItemSrc.id
                // gán lại parent
                trade2Copy.parentId = trade1Copy.id
                trade2Copy.createdBy = user.id
                delete trade2Copy.id
                delete trade2Copy.createdAt
                delete trade2Copy.updatedAt
                trade2Copy = await bidTradeRepo.save(trade2Copy)

                if (trade2Copy.type === enumData.DataType.List.code) {
                  const lstDetailSrc = await trade2Src.bidTradeListDetails
                  if (lstDetailSrc.length > 0) {
                    for (const detailSrc of lstDetailSrc) {
                      const detailCopy = bidTradeListDetailRepo.create({ ...detailSrc })
                      // gán lại bidTradeId
                      detailCopy.bidTradeId = trade2Copy.id
                      detailCopy.createdBy = user.id
                      delete detailCopy.id
                      delete detailCopy.createdAt
                      delete detailCopy.updatedAt
                      await bidTradeListDetailRepo.save(detailCopy)
                    }
                  }
                }
              }
            }
          }
          //#endregion

          //#region Bid Price
          const lstBidPriceSrc = await bidItemSrc.prices
          const dicPrice: any = {}
          if (lstBidPriceSrc.length > 0) {
            const lstPriceSrc1 = lstBidPriceSrc.filter((c) => c.level == 1)
            for (const price1Src of lstPriceSrc1) {
              let price1Copy = bidPriceRepo.create({ ...price1Src })
              // gán lại gói thầu
              price1Copy.bidItemId = bidItemSrc.id
              price1Copy.createdBy = user.id
              delete price1Copy.id
              delete price1Copy.createdAt
              delete price1Copy.updatedAt
              price1Copy = await bidPriceRepo.save(price1Copy)
              dicPrice[price1Src.id] = price1Copy.id

              // BLOCK tạo bidPriceListDetail
              {
                const lstDetailSrc = await price1Src.bidPriceListDetails
                if (lstDetailSrc.length > 0) {
                  for (const detailSrc of lstDetailSrc) {
                    const detailCopy = bidPriceListDetailRepo.create({ ...detailSrc })
                    // gán lại bidPriceId
                    detailCopy.bidPriceId = price1Copy.id
                    detailCopy.createdBy = user.id
                    delete detailCopy.id
                    delete detailCopy.createdAt
                    delete detailCopy.updatedAt
                    await bidPriceListDetailRepo.save(detailCopy)
                  }
                }
              }

              const lstPriceSrc2 = lstBidPriceSrc.filter((c) => c.parentId == price1Src.id)
              for (const price2Src of lstPriceSrc2) {
                let price2Copy = bidPriceRepo.create({ ...price2Src })
                // gán lại gói thầu
                price2Copy.bidItemId = bidItemSrc.id
                // gán lại parent
                price2Copy.parentId = price1Copy.id
                price2Copy.createdBy = user.id
                delete price2Copy.id
                delete price2Copy.createdAt
                delete price2Copy.updatedAt
                price2Copy = await bidPriceRepo.save(price2Copy)
                dicPrice[price2Src.id] = price2Copy.id

                // BLOCK tạo bidPriceListDetail
                {
                  const lstDetailSrc = await price2Src.bidPriceListDetails
                  if (lstDetailSrc.length > 0) {
                    for (const detailSrc of lstDetailSrc) {
                      const detailCopy = bidPriceListDetailRepo.create({ ...detailSrc })
                      // gán lại bidPriceId
                      detailCopy.bidPriceId = price2Copy.id
                      detailCopy.createdBy = user.id
                      delete detailCopy.id
                      delete detailCopy.createdAt
                      delete detailCopy.updatedAt
                      await bidPriceListDetailRepo.save(detailCopy)
                    }
                  }
                }

                const lstPriceSrc3 = lstBidPriceSrc.filter((c) => c.parentId == price2Src.id)
                for (const price3Src of lstPriceSrc3) {
                  let price3Copy = bidPriceRepo.create({ ...price3Src })
                  // gán lại gói thầu
                  price3Copy.bidItemId = bidItemSrc.id
                  // gán lại parent
                  price3Copy.parentId = price2Copy.id
                  price3Copy.createdBy = user.id
                  delete price3Copy.id
                  delete price3Copy.createdAt
                  delete price3Copy.updatedAt
                  price3Copy = await bidPriceRepo.save(price3Copy)
                  dicPrice[price3Src.id] = price3Copy.id

                  // BLOCK tạo bidPriceListDetail
                  {
                    const lstDetailSrc = await price3Src.bidPriceListDetails
                    if (lstDetailSrc.length > 0) {
                      for (const detailSrc of lstDetailSrc) {
                        const detailCopy = bidPriceListDetailRepo.create({ ...detailSrc })
                        // gán lại bidPriceId
                        detailCopy.bidPriceId = price3Copy.id
                        detailCopy.createdBy = user.id
                        delete detailCopy.id
                        delete detailCopy.createdAt
                        delete detailCopy.updatedAt
                        await bidPriceListDetailRepo.save(detailCopy)
                      }
                    }
                  }
                }
              }
            }
          }
          //#endregion

          //#region Bid Price Col
          const lstColSrc = await bidItemSrc.bidPriceCols
          if (lstColSrc.length > 0) {
            for (const colSrc of lstColSrc) {
              let colCopy = bidPriceColRepo.create({ ...colSrc })
              // gán lại gói thầu
              colCopy.bidItemId = bidItemSrc.id
              colCopy.createdBy = user.id
              delete colCopy.id
              delete colCopy.createdAt
              delete colCopy.updatedAt
              colCopy = await bidPriceColRepo.save(colCopy)

              const lstColValueSrc = await colSrc.bidPriceColValue
              for (const colValue of lstColValueSrc) {
                const colValueCopy = bidPriceColValueRepo.create({ ...colValue })
                // gán lại gói thầu
                colValueCopy.bidPriceColId = colCopy.id
                colValueCopy.bidPriceId = dicPrice[colValueCopy.bidPriceId]
                colValueCopy.createdBy = user.id
                delete colValueCopy.id
                delete colValueCopy.createdAt
                delete colValueCopy.updatedAt
                await bidPriceColValueRepo.save(colValueCopy)
              }
            }
          }
          //#endregion

          //#region Bid Custom Price
          const lstCustomPriceSrc = await bidItemSrc.customPrices
          for (const customPriceSrc of lstCustomPriceSrc) {
            const customPriceCopy = bidCustomPriceRepo.create({ ...customPriceSrc })
            // gán lại gói thầu
            customPriceCopy.bidItemId = bidItemSrc.id
            customPriceCopy.createdBy = user.id
            delete customPriceCopy.id
            delete customPriceCopy.createdAt
            delete customPriceCopy.updatedAt
            await bidCustomPriceRepo.save(customPriceCopy)
          }
          //#endregion

          //#region Bid Supplier
          const lstBidSupplierSrc = await bidItemSrc.bidSuppliers
          for (const bidSupplierSrc of lstBidSupplierSrc) {
            const bidSupplierCopy = bidSupplierRepo.create({ ...bidSupplierSrc })
            delete bidSupplierCopy.id
            delete bidSupplierCopy.createdAt
            delete bidSupplierCopy.updatedAt
            // gán lại gói thầu
            bidSupplierCopy.bidItemId = bidItemSrc.id
            // reset các biến đánh giá
            bidSupplierCopy.isTechValid = true
            bidSupplierCopy.isTradeValid = true
            bidSupplierCopy.isPriceValid = true
            bidSupplierCopy.isSuccessBid = false
            bidSupplierCopy.isHighlight = false
            bidSupplierCopy.isNotHaveMinValue = false
            bidSupplierCopy.note = ''
            bidSupplierCopy.noteTech = ''
            bidSupplierCopy.noteTrade = ''
            bidSupplierCopy.notePrice = ''
            bidSupplierCopy.noteTechLeader = ''
            bidSupplierCopy.noteMPOLeader = ''
            bidSupplierCopy.noteSuccessBid = ''
            bidSupplierCopy.scoreTech = 0
            bidSupplierCopy.scoreTrade = 0
            bidSupplierCopy.scorePrice = 0
            bidSupplierCopy.scoreManualTech = 0
            bidSupplierCopy.scoreManualTrade = 0
            bidSupplierCopy.scoreManualPrice = 0
            bidSupplierCopy.status = enumData.BidSupplierStatus.DaDuocChon.code
            bidSupplierCopy.statusTech = enumData.BidSupplierTechStatus.ChuaXacNhan.code
            bidSupplierCopy.statusTrade = enumData.BidSupplierTradeStatus.ChuaXacNhan.code
            bidSupplierCopy.statusPrice = enumData.BidSupplierPriceStatus.ChuaXacNhan.code
            bidSupplierCopy.createdBy = user.id
            await bidSupplierRepo.save(bidSupplierCopy)
          }
          //#endregion
        }
      }
      //#endregion

      return { message: `Sao chép thành công, gói thầu có mã TBMT [${bidCopy.code}] được tạo ra từ gói thầu có mã TBMT [${bidSrc.code}].` }
    })
  }

  /** Kiểm tra quyền sửa thông tin gói thầu */
  async checkPermissionMpoEdit(user: UserDto, bidId: string) {
    let result = false
    let message = ''
    const lstStatusCanEdit = [
      enumData.BidStatus.GoiThauTam.code,
      enumData.BidStatus.ChoDuyetGoiThauTam.code,
      enumData.BidStatus.DangCauHinhGoiThau.code,
      enumData.BidStatus.DangChonNCC.code,
      enumData.BidStatus.TuChoiGoiThau.code,
      enumData.BidStatus.DangDuyetGoiThau.code,
      enumData.BidStatus.DangNhanBaoGia.code,
    ]
    const bid = await this.repo.findOne({ where: { id: bidId, parentId: IsNull(), isDeleted: false } })
    if (bid) {
      if (lstStatusCanEdit.includes(bid.status)) {
        result = await this.bidEmployeeAccessRepo.isMPO(user, bid.id)
        if (!result) {
          message = 'Bạn không có quyền chỉnh sửa thông tin gói thầu.'
        }
      } else {
        result = false
        message = 'Chỉ được phép chỉnh sửa thông tin gói thầu khi chưa mở thầu.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Kiểm tra trước khi vào transaction import */
  private async checkImportBids(user: UserDto, data: any) {
    const checkResult: { isCheckError: boolean; lstError: any[]; message: string } = {
      isCheckError: false,
      lstError: [],
      message: '',
    }

    const lstBidType = await this.bidTypeRepo.find({ where: { companyId: user.companyId, isDeleted: false } }) //get bidTypeId from bidTypeName
    const lstEmployee = await this.employeeRepo.find({ where: { companyId: user.companyId, isDeleted: false } }) //get employeeIds from employeeNames
    const lstEmployeeName = lstEmployee.map((c) => c.name)
    const lstCompanyInvite = await this.settingStringRepo.find({
      where: {
        companyId: user.companyId,
        type: enumData.SettingStringType.company,
        isDeleted: false,
      },
    })
    const lstAddressSubmit = await this.settingStringRepo.find({
      where: {
        companyId: user.companyId,
        type: enumData.SettingStringType.address,
        isDeleted: false,
      },
    })
    const lstMasterBidGuarantee = await this.settingStringRepo.find({
      where: {
        companyId: user.companyId,
        type: enumData.SettingStringType.masterBidGuarantee,
        isDeleted: false,
      },
    })

    // Kiểm tra từng row
    const lstErr = []
    for (const row of data.lstData) {
      // zenId: 'STT *' đã check ở FE

      // companyInvite: 'Công ty mời thầu *'
      if (row.companyInvite != null) row.companyInvite = (row.companyInvite + '').trim()
      if (row.companyInvite == null || row.companyInvite === '') {
        const errorMessage = '[Công ty mời thầu *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else if (!lstCompanyInvite.some((c) => c.name == row.companyInvite)) {
        const errorMessage = `[Công ty mời thầu *] có tên [${row.companyInvite}] không tồn tại trong hệ thống`
        lstErr.push({ ...row, errorMessage })
        continue
      }

      // addressSubmit: 'Địa chỉ nộp hồ sơ thầu'
      if (row.addressSubmit != null) row.addressSubmit = (row.addressSubmit + '').trim()
      if (row.addressSubmit != null && row.addressSubmit !== '' && !lstAddressSubmit.some((c) => c.name == row.addressSubmit)) {
        const errorMessage = `[Địa chỉ nộp hồ sơ thầu] có tên [${row.addressSubmit}] không tồn tại trong hệ thống`
        lstErr.push({ ...row, errorMessage })
        continue
      }

      // listAddress: 'Các địa điểm thực hiện gói thầu'
      if (row.listAddress != null) row.listAddress = (row.listAddress + '').trim()

      // anotherRoleNames: 'Các thành viên hội đồng xét thầu *'
      if (row.anotherRoleNames != null) row.anotherRoleNames = (row.anotherRoleNames + '').trim()
      if (row.anotherRoleNames == null || row.anotherRoleNames === '') {
        const errorMessage = '[Các thành viên hội đồng xét thầu *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const lstAnotherRoleName = row.anotherRoleNames.split(',').map((c: string) => c.trim())
        const lstAnotherRoleNameNotIn = lstAnotherRoleName.filter((c: any) => !lstEmployeeName.includes(c))
        if (lstAnotherRoleNameNotIn.length > 0) {
          const errorMessage = `[Các thành viên hội đồng xét thầu *] có các nhân viên [${lstAnotherRoleNameNotIn.join()}] không tồn tại trong hệ thống`
          lstErr.push({ ...row, errorMessage })
          continue
        }
      }

      // otherRoleNames: 'Các thành viên khác'
      if (row.otherRoleNames != null) row.otherRoleNames = (row.otherRoleNames + '').trim()
      if (row.otherRoleNames != null && row.otherRoleNames !== '') {
        const lstOtherRoleName = row.otherRoleNames.split(',').map((c: string) => c.trim())
        const lstOtherRoleNameNotIn = lstOtherRoleName.filter((c: any) => !lstEmployeeName.includes(c))
        if (lstOtherRoleNameNotIn.length > 0) {
          const errorMessage = `[Các thành viên khác] có các nhân viên [${lstOtherRoleNameNotIn.join()}] không tồn tại trong hệ thống`
          lstErr.push({ ...row, errorMessage })
          continue
        }
      }

      // name: 'Tên gói thầu *'
      if (row.name != null) row.name = (row.name + '').trim()
      if (row.name == null || row.name === '') {
        const errorMessage = '[Tên gói thầu *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      }

      // scoreDLC: 'Điểm tối đa cho các hạng mục chào giá *'
      if (row.scoreDLC != null) row.scoreDLC = (row.scoreDLC + '').trim()
      if (row.scoreDLC == null || row.scoreDLC === '') {
        const errorMessage = '[Điểm tối đa cho các hạng mục chào giá *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const scoreDLC = +row.scoreDLC
        if (isNaN(scoreDLC) || !isFinite(scoreDLC)) {
          const errorMessage = '[Điểm tối đa cho các hạng mục chào giá *] không hợp lệ'
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.scoreDLC = scoreDLC
      }

      // bidTypeName: 'Hình thức đấu thầu *'
      if (row.bidTypeName != null) row.bidTypeName = (row.bidTypeName + '').trim()
      if (row.bidTypeName == null || row.bidTypeName === '') {
        const errorMessage = '[Hình thức đấu thầu *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const bidTypeObj = lstBidType.find((c) => c.name == row.bidTypeName)
        if (!bidTypeObj) {
          const errorMessage = `[Hình thức đấu thầu *] có tên [${row.bidTypeName}] không tồn tại trong hệ thống`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.bidTypeId = bidTypeObj.id
      }

      // acceptEndDate: '3.Ngày hết hạn xác nhận tham gia đấu thầu *'
      if (row.acceptEndDate != null) row.acceptEndDate = (row.acceptEndDate + '').trim()
      if (row.acceptEndDate == null || row.acceptEndDate === '') {
        const errorMessage = '[3.Ngày hết hạn xác nhận tham gia đấu thầu *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const acceptEndDate = new Date(row.acceptEndDate)
        if (isNaN(acceptEndDate.getTime())) {
          const errorMessage = `[3.Ngày hết hạn xác nhận tham gia đấu thầu *] là [${row.acceptEndDate}] không hợp lệ, ngày phải có định dạng yyyy/mm/dd HH:mm hoặc yyyy-mm-dd HH:mm`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.acceptEndDate = acceptEndDate
      }

      // submitEndDate: '4.Ngày hết hạn nộp hồ sơ thầu *'
      if (row.submitEndDate != null) row.submitEndDate = (row.submitEndDate + '').trim()
      if (row.submitEndDate == null || row.submitEndDate === '') {
        const errorMessage = '[4.Ngày hết hạn nộp hồ sơ thầu *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const submitEndDate = new Date(row.submitEndDate)
        if (isNaN(submitEndDate.getTime())) {
          const errorMessage = `[4.Ngày hết hạn nộp hồ sơ thầu *] là [${row.submitEndDate}, ngày phải có định dạng yyyy/mm/dd HH:mm hoặc yyyy-mm-dd HH:mm`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.submitEndDate = submitEndDate
      }

      // timeserving: 'Hiệu lực hợp đồng (tháng) *'
      if (row.timeserving != null) row.timeserving = (row.timeserving + '').trim()
      if (row.timeserving == null || row.timeserving === '') {
        const errorMessage = '[Hiệu lực hợp đồng (tháng) *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const timeserving = +row.timeserving
        if (isNaN(timeserving) || !isFinite(timeserving)) {
          const errorMessage = '[Hiệu lực hợp đồng (tháng) *] không hợp lệ'
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.timeserving = timeserving
      }

      // masterBidGuaranteeName: 'Hình thức bảo lãnh dự thầu'
      if (row.masterBidGuaranteeName != null) row.masterBidGuaranteeName = (row.masterBidGuaranteeName + '').trim()
      if (row.masterBidGuaranteeName != null && row.masterBidGuaranteeName !== '') {
        const masterBidGuaranteeObj = lstMasterBidGuarantee.find((c) => c.name == row.masterBidGuaranteeName)
        if (!masterBidGuaranteeObj) {
          const errorMessage = `[Hình thức bảo lãnh dự thầu] có tên [${row.masterBidGuaranteeName}] không tồn tại trong hệ thống`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.masterBidGuaranteeId = masterBidGuaranteeObj.id
      }

      // moneyGuarantee: 'Số tiền bảo lãnh dự thầu (VNĐ)'
      if (row.moneyGuarantee != null) row.moneyGuarantee = (row.moneyGuarantee + '').trim()
      if (row.moneyGuarantee != null || row.moneyGuarantee !== '') {
        const moneyGuarantee = +row.moneyGuarantee
        if (isNaN(moneyGuarantee) || !isFinite(moneyGuarantee)) {
          const errorMessage = '[Số tiền bảo lãnh dự thầu (VNĐ)] không hợp lệ'
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.moneyGuarantee = moneyGuarantee
      }

      // timeGuarantee: 'Thời hạn bảo lãnh dự thầu (tháng)'
      if (row.timeGuarantee != null) row.timeGuarantee = (row.timeGuarantee + '').trim()
      if (row.timeGuarantee != null || row.timeGuarantee !== '') {
        const timeGuarantee = +row.timeGuarantee
        if (isNaN(timeGuarantee) || !isFinite(timeGuarantee)) {
          const errorMessage = '[Thời hạn bảo lãnh dự thầu (tháng)] không hợp lệ'
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.timeGuarantee = timeGuarantee
      }

      // mpoName: 'Thành viên phụ trách mua hàng *'
      if (row.mpoName != null) row.mpoName = (row.mpoName + '').trim()
      if (row.mpoName == null || row.mpoName === '') {
        const errorMessage = '[Thành viên phụ trách mua hàng *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const employeeObj = lstEmployee.find((c) => c.name == row.mpoName)
        if (!employeeObj) {
          const errorMessage = `[Thành viên phụ trách mua hàng *] có tên [${row.mpoName}] không tồn tại trong hệ thống`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.mpoId = employeeObj.id
      }

      // mpoLeadName: 'Người duyệt nội dung mua hàng *'
      if (row.mpoLeadName != null) row.mpoLeadName = (row.mpoLeadName + '').trim()
      if (row.mpoLeadName == null || row.mpoLeadName === '') {
        const errorMessage = '[Người duyệt nội dung mua hàng *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const employeeObj = lstEmployee.find((c) => c.name == row.mpoLeadName)
        if (!employeeObj) {
          const errorMessage = `[Người duyệt nội dung mua hàng *] có tên [${row.mpoLeadName}] không tồn tại trong hệ thống`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.mpoLeadId = employeeObj.id
      }

      // techName: 'Thành viên phụ trách yêu cầu kỹ thuật *'
      if (row.techName != null) row.techName = (row.techName + '').trim()
      if (row.techName == null || row.techName === '') {
        const errorMessage = '[Thành viên phụ trách yêu cầu kỹ thuật *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const employeeObj = lstEmployee.find((c) => c.name == row.techName)
        if (!employeeObj) {
          const errorMessage = `[Thành viên phụ trách yêu cầu kỹ thuật *] có tên [${row.techName}] không tồn tại trong hệ thống`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.techId = employeeObj.id
      }

      // techLeadName: 'Người duyệt yêu cầu kỹ thuật *'
      if (row.techLeadName != null) row.techLeadName = (row.techLeadName + '').trim()
      if (row.techLeadName == null || row.techLeadName === '') {
        const errorMessage = '[Người duyệt yêu cầu kỹ thuật *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const employeeObj = lstEmployee.find((c) => c.name == row.techLeadName)
        if (!employeeObj) {
          const errorMessage = `[Người duyệt yêu cầu kỹ thuật *] có tên [${row.techLeadName}] không tồn tại trong hệ thống`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.techLeadId = employeeObj.id
      }

      // timeTechDate: '1.Thời hạn thiết lập các yêu cầu kỹ thuật *'
      if (row.timeTechDate != null) row.timeTechDate = (row.timeTechDate + '').trim()
      if (row.timeTechDate == null || row.timeTechDate === '') {
        const errorMessage = '[1.Thời hạn thiết lập các yêu cầu kỹ thuật *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const timeTechDate = new Date(row.timeTechDate)
        if (isNaN(timeTechDate.getTime())) {
          const errorMessage = `[1.Thời hạn thiết lập các yêu cầu kỹ thuật *] là [${row.timeTechDate}] không hợp lệ, ngày phải có định dạng yyyy/mm/dd HH:mm hoặc yyyy-mm-dd HH:mm`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.timeTechDate = timeTechDate
      }

      // timePriceDate: '2.Thời hạn thiết lập các hạng mục chào giá và điều kiện thương mại *'
      if (row.timePriceDate != null) row.timePriceDate = (row.timePriceDate + '').trim()
      if (row.timePriceDate == null || row.timePriceDate === '') {
        const errorMessage = '[2.Thời hạn thiết lập các hạng mục chào giá và điều kiện thương mại *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const timePriceDate = new Date(row.timePriceDate)
        if (isNaN(timePriceDate.getTime())) {
          const errorMessage = `[2.Thời hạn thiết lập các hạng mục chào giá và điều kiện thương mại *] là [${row.timePriceDate}] không hợp lệ, ngày phải có định dạng yyyy/mm/dd HH:mm hoặc yyyy-mm-dd HH:mm`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.timePriceDate = timePriceDate
      }

      // timeCheckTechDate: '5.Thời hạn đánh giá các yêu cầu kỹ thuật *'
      if (row.timeCheckTechDate != null) row.timeCheckTechDate = (row.timeCheckTechDate + '').trim()
      if (row.timeCheckTechDate == null || row.timeCheckTechDate === '') {
        const errorMessage = '[5.Thời hạn đánh giá các yêu cầu kỹ thuật *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const timeCheckTechDate = new Date(row.timeCheckTechDate)
        if (isNaN(timeCheckTechDate.getTime())) {
          const errorMessage = `[5.Thời hạn đánh giá các yêu cầu kỹ thuật *] là [${row.timeCheckTechDate}] không hợp lệ, ngày phải có định dạng yyyy/mm/dd HH:mm hoặc yyyy-mm-dd HH:mm`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.timeCheckTechDate = timeCheckTechDate
      }

      // timeCheckPriceDate: '6.Thời hạn đánh giá kết quả chào giá và điều kiện thương mại *'
      if (row.timeCheckPriceDate != null) row.timeCheckPriceDate = (row.timeCheckPriceDate + '').trim()
      if (row.timeCheckPriceDate == null || row.timeCheckPriceDate === '') {
        const errorMessage = '[6.Thời hạn đánh giá kết quả chào giá và điều kiện thương mại *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const timeCheckPriceDate = new Date(row.timeCheckPriceDate)
        if (isNaN(timeCheckPriceDate.getTime())) {
          const errorMessage = `[6.Thời hạn đánh giá kết quả chào giá và điều kiện thương mại *] là [${row.timeCheckPriceDate}] không hợp lệ, ngày phải có định dạng yyyy/mm/dd HH:mm hoặc yyyy-mm-dd HH:mm`
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.timeCheckPriceDate = timeCheckPriceDate
      }

      // percentTech: 'Tỉ lệ phần trăm kỹ thuật *'
      if (row.percentTech != null) row.percentTech = (row.percentTech + '').trim()
      if (row.percentTech == null || row.percentTech === '') {
        const errorMessage = '[Tỉ lệ phần trăm kỹ thuật *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const percentTech = +row.percentTech
        if (isNaN(percentTech) || !isFinite(percentTech)) {
          const errorMessage = '[Tỉ lệ phần trăm kỹ thuật *] không hợp lệ'
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.percentTech = percentTech
      }

      // percentTrade: 'Tỉ lệ phần trăm ĐKTM *'
      if (row.percentTrade != null) row.percentTrade = (row.percentTrade + '').trim()
      if (row.percentTrade == null || row.percentTrade === '') {
        const errorMessage = '[Tỉ lệ phần trăm ĐKTM *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const percentTrade = +row.percentTrade
        if (isNaN(percentTrade) || !isFinite(percentTrade)) {
          const errorMessage = '[Tỉ lệ phần trăm ĐKTM *] không hợp lệ'
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.percentTrade = percentTrade
      }

      // percentPrice: 'Tỉ lệ phần trăm giá *'
      if (row.percentPrice != null) row.percentPrice = (row.percentPrice + '').trim()
      if (row.percentPrice == null || row.percentPrice === '') {
        const errorMessage = '[Tỉ lệ phần trăm giá *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      } else {
        const percentPrice = +row.percentPrice
        if (isNaN(percentPrice) || !isFinite(percentPrice)) {
          const errorMessage = '[Tỉ lệ phần trăm giá *] không hợp lệ'
          lstErr.push({ ...row, errorMessage })
          continue
        }
        row.percentPrice = percentPrice
      }

      // serviceInvite: 'Mô tả nội dung mời thầu *'
      if (row.serviceInvite != null) row.serviceInvite = (row.serviceInvite + '').trim()
      if (row.serviceInvite == null || row.serviceInvite === '') {
        const errorMessage = '[Mô tả nội dung mời thầu *] là bắt buộc không được để trống'
        lstErr.push({ ...row, errorMessage })
        continue
      }

      // isShowHomePage: 'Cho phép hiển thị thông tin mời thầu ở trang tin đấu thầu'
      if (row.isShowHomePage != null) row.isShowHomePage = (row.isShowHomePage + '').toUpperCase()
      if (row.isShowHomePage == 'TRUE' || row.isShowHomePage == 'X') {
        row.isShowHomePage = true
      } else {
        row.isShowHomePage = false
      }

      // isSendEmailInviteBid: 'Gửi thông báo mời thầu cho Doanh nghiệp'
      if (row.isSendEmailInviteBid != null) row.isSendEmailInviteBid = (row.isSendEmailInviteBid + '').toUpperCase()
      if (row.isSendEmailInviteBid == 'TRUE' || row.isSendEmailInviteBid == 'X') {
        row.isSendEmailInviteBid = true
      } else {
        row.isSendEmailInviteBid = false
      }

      // isAutoBid: 'Tự động chọn NCC thắng thầu và kết thúc thầu'
      if (row.isAutoBid != null) row.isAutoBid = (row.isAutoBid + '').toUpperCase()
      if (row.isAutoBid == 'TRUE' || row.isAutoBid == 'X') {
        row.isAutoBid = true
      } else {
        row.isAutoBid = false
      }
    }

    if (lstErr.length > 0) {
      checkResult.lstError = lstErr
      checkResult.isCheckError = true
      checkResult.message = `Có ${checkResult.lstError.length} dòng lỗi, vui lòng xem chi tiết lỗi và kiểm tra lại file!`
    }

    return checkResult
  }

  private async findLastSort(user: UserDto, bidRepo: Repository<BidEntity>, code: string) {
    const objData = await bidRepo.findOne({
      where: {
        code: Like(`%${code}%`),
        parentId: IsNull(),
        companyId: user.companyId,
        // createdAt: Between(startOfMonth, endOfMonth),
      },
      order: { code: 'DESC' },
      select: { id: true, code: true },
    })

    if (!objData) return 0

    const sortString = objData.code.slice(-3)
    return +sortString
  }

  /** Import Gói thầu */
  public async importBids(user: UserDto, data: { lstData: any[] }) {
    // Check user
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    // Check & validate data
    const checkResult = await this.checkImportBids(user, data)
    if (checkResult.lstError.length > 0) {
      return checkResult
    }

    const employeeId = user.employeeId
    // trả về thông báo số Gói thầu được import thành công, và danh sách các dòng với chi tiết lỗi từng dòng
    return await this.repo.manager.transaction(async (manager) => {
      const checkResult: any = { isCheckError: false, lstError: [], message: '', lstSupplierId: [] }
      let numRowSuccess = 0

      const bidRepo = manager.getRepository(BidEntity)
      const serviceRepo = manager.getRepository(ServiceEntity)
      const employeeRepo = manager.getRepository(EmployeeEntity)
      const bidEmployeeAccessRepo = manager.getRepository(BidEmployeeAccessEntity)
      const bidHistoryRepo = manager.getRepository(BidHistoryEntity)
      const lstEmployee = await employeeRepo.find({ where: { companyId: user.companyId, isDeleted: false } })

      // Lưu từng row
      const lstErr = []
      for (const row of data.lstData) {
        try {
          let bid = new BidEntity()
          bid.companyId = user.companyId
          bid.createdBy = user.id
          bid.name = row.name
          const dateNow = new Date()
          const objService = await serviceRepo.findOne({ where: { id: row.serviceId } })
          if (!objService) {
            throw new NotFoundException('Lĩnh vực mua hàng không còn tồn tại.')
          }
          const codeTemp = moment(dateNow).format('YYYYMM') + objService.name
          const lastSort = await this.findLastSort(user, bidRepo, codeTemp + '_')
          const sortString = ('00' + (lastSort + 1)).slice(-3)
          row.publicDate = dateNow
          row.startBidDate = new Date(row.submitEndDate)
          row.startBidDate.setDate(row.startBidDate.getDate() + 1)
          row.startBidDate.setHours(8, 0, 0, 0)

          if (row.startBidDate >= row.timeCheckTechDate) {
            throw new BadRequestException('Thời điểm mở thầu phải sớm hơn thời hạn đánh giá yêu cầu kỹ thuật, năng lực.')
          }
          if (row.timeTechDate <= dateNow) {
            throw new BadRequestException('Ngày (1) phải trễ hơn ngày hiện tại.')
          }
          if (row.timePriceDate <= dateNow) {
            throw new BadRequestException('Ngày (2) phải trễ hơn ngày hiện tại.')
          }
          if (row.timeTechDate >= row.acceptEndDate) {
            throw new BadRequestException('Ngày (1) phải sớm hơn ngày (3).')
          }
          if (row.timePriceDate >= row.acceptEndDate) {
            throw new BadRequestException('Ngày (2) phải sớm hơn ngày (3).')
          }
          if (row.acceptEndDate >= row.submitEndDate) {
            throw new BadRequestException('Ngày (3) phải sớm hơn ngày (4).')
          }
          if (row.submitEndDate >= row.timeCheckTechDate) {
            throw new BadRequestException('Ngày (4) phải sớm hơn ngày (5).')
          }
          if (row.submitEndDate >= row.timeCheckPriceDate) {
            throw new BadRequestException('Ngày (4) sớm hơn ngày (6).')
          }
          bid.code = codeTemp + '_' + sortString

          // gói thầu mới tạo, mặc định lấy theo service
          bid.fomular = objService.fomular

          bid.serviceInvite = row.serviceInvite

          bid.acceptEndDate = row.acceptEndDate

          bid.submitEndDate = row.submitEndDate

          bid.addressSubmit = row.addressSubmit || ''

          bid.companyInvite = row.companyInvite

          bid.listAddress = row.listAddress || ''

          bid.publicDate = row.publicDate

          bid.bidTypeId = row.bidTypeId

          bid.timeserving = row.timeserving

          bid.scoreDLC = row.scoreDLC

          bid.startBidDate = row.startBidDate

          bid.moneyGuarantee = row.moneyGuarantee
          bid.timeGuarantee = row.timeGuarantee
          bid.masterBidGuaranteeId = row.masterBidGuaranteeId

          bid.timeTechDate = row.timeTechDate
          bid.timePriceDate = row.timePriceDate
          bid.timeCheckTechDate = row.timeCheckTechDate
          bid.timeCheckPriceDate = row.timeCheckPriceDate
          bid.status = enumData.BidStatus.GoiThauTam.code

          bid.isShowHomePage = row.isShowHomePage
          bid.isSendEmailInviteBid = row.isSendEmailInviteBid
          bid.isAutoBid = row.isAutoBid

          bid.serviceId = row.serviceId
          bid.percentTech = row.percentTech
          bid.percentTrade = row.percentTrade
          bid.percentPrice = row.percentPrice

          bid.statusTech = enumData.BidTechStatus.DangTao.code
          bid.statusTrade = enumData.BidTradeStatus.DangTao.code
          bid.statusPrice = enumData.BidPriceStatus.DangTao.code
          bid.statusRateTech = enumData.BidTechRateStatus.ChuaTao.code
          bid.statusRateTrade = enumData.BidTradeRateStatus.ChuaTao.code
          bid.statusRatePrice = enumData.BidPriceRateStatus.ChuaTao.code

          bid.createdBy = user.id

          const bidEntity = await bidRepo.save(bid)

          // MPO
          const mpoAccess = new BidEmployeeAccessEntity()
          mpoAccess.companyId = user.companyId
          mpoAccess.createdBy = user.id
          mpoAccess.bidId = bidEntity.id
          mpoAccess.employeeId = row.mpoId
          mpoAccess.type = enumData.BidRuleType.MPO.code
          await bidEmployeeAccessRepo.save(mpoAccess)

          // MPO Lead
          const mpoLeadAccess = new BidEmployeeAccessEntity()
          mpoLeadAccess.companyId = user.companyId
          mpoLeadAccess.createdBy = user.id
          mpoLeadAccess.bidId = bidEntity.id
          mpoLeadAccess.employeeId = row.mpoLeadId
          mpoLeadAccess.type = enumData.BidRuleType.MPOLeader.code
          await bidEmployeeAccessRepo.save(mpoLeadAccess)

          // Tech
          const techAccess = new BidEmployeeAccessEntity()
          techAccess.companyId = user.companyId
          techAccess.createdBy = user.id
          techAccess.bidId = bidEntity.id
          techAccess.employeeId = row.techId
          techAccess.type = enumData.BidRuleType.Tech.code
          await bidEmployeeAccessRepo.save(techAccess)

          // Tech Lead
          const techLeadAccess = new BidEmployeeAccessEntity()
          techLeadAccess.companyId = user.companyId
          techLeadAccess.createdBy = user.id
          techLeadAccess.bidId = bidEntity.id
          techLeadAccess.employeeId = row.techLeadId
          techLeadAccess.type = enumData.BidRuleType.TechLeader.code
          await bidEmployeeAccessRepo.save(techLeadAccess)

          // Member
          const lstMemberName = row.anotherRoleNames.split(',').map((c: string) => c.trim())

          for (const memberName of lstMemberName) {
            const employee = lstEmployee.find((c) => c.name == memberName)
            if (employee) {
              const anotherAccess = new BidEmployeeAccessEntity()
              anotherAccess.companyId = user.companyId
              anotherAccess.createdBy = user.id
              anotherAccess.bidId = bidEntity.id
              anotherAccess.employeeId = employee.id
              anotherAccess.type = enumData.BidRuleType.Memmber.code
              await bidEmployeeAccessRepo.save(anotherAccess)
            }
          }

          // Other
          if (row.otherRoleNames) {
            const lstOtherName = row.otherRoleNames.split(',').map((c: string) => c.trim())

            for (const otherName of lstOtherName) {
              const employee = lstEmployee.find((c) => c.name == otherName)
              if (employee) {
                const anotherAccess = new BidEmployeeAccessEntity()
                anotherAccess.companyId = user.companyId
                anotherAccess.createdBy = user.id
                anotherAccess.bidId = bidEntity.id
                anotherAccess.employeeId = employee.id
                anotherAccess.type = enumData.BidRuleType.Other.code
                await bidEmployeeAccessRepo.save(anotherAccess)
              }
            }
          }

          // Bid History
          const bidHistory = new BidHistoryEntity()
          bidHistory.companyId = user.companyId
          bidHistory.createdBy = user.id
          bidHistory.bidId = bidEntity.id
          bidHistory.employeeId = employeeId
          bidHistory.status = enumData.BidHistoryStatus.TaoGoiThauExcel.code
          await bidHistoryRepo.save(bidHistory)

          numRowSuccess++
        } catch (error) {
          lstErr.push({ ...row, errorMessage: JSON.stringify(error) })
        }
      }

      if (lstErr.length > 0) {
        checkResult.lstError = lstErr
        checkResult.message = `Import thành công ${numRowSuccess}/${data.lstData.length} LVMH!`
      } else {
        checkResult.message = `Import thành công ${data.lstData.length} LVMH!`
      }

      return checkResult
    })
  }

  /** Kiểm tra quyền sửa thông tin gói thầu */
  async checkPermissionMpoDelete(user: UserDto, bidId: string) {
    let result = false
    let message = 'Gói thầu không còn tồn tại'
    const bid = await this.repo.findOne({
      where: [{ id: bidId, parentId: IsNull(), isDeleted: false }, { childs: { id: bidId, isDeleted: false } }],
    })
    if (bid) {
      result = await this.bidEmployeeAccessRepo.isMPO_MPOLeader(user, bid.id)
      if (!result) {
        message = 'Bạn không có quyền xóa gói thầu.'
      }
    }

    return { hasPermission: result, message }
  }

  /** Yêu cầu hủy gói thầu */
  async requestDeleteBid(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    // const objPermission = await this.checkPermissionMpoDelete(user, bidId)
    // if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    const bid = await this.repo.findOne({ where: { id: bidId } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

    let isTempBid = bid.status == enumData.BidStatus.GoiThauTam.code
    // Nếu gói thầu tạm thì xóa trực tiếp mà không cần tạo yêu cầu xóa và đợi duyệt
    if (isTempBid) {
      await this.confirmDeleteBid(bidId, user)
      // } else {
      //   await this.repo.update(bidId, { isRequestDelete: true, updatedBy: user.id })

      // Bid History
      const bidHistory = new BidHistoryEntity()
      bidHistory.companyId = user.companyId
      bidHistory.createdBy = user.id
      bidHistory.bidId = bidId
      bidHistory.employeeId = user.employeeId
      bidHistory.status = enumData.BidHistoryStatus.YeuCauHuyGoiThau.code
      bidHistory.save()
    }

    // if (!isTempBid) this.emailService.GuiMPOLeadHuyGoiThau(bidId)

    return { message: 'Yêu cầu xóa gói thầu thành công.' }
  }

  async confirmDeleteBid(bidId: string, user: UserDto) {
    await this.repo.manager.transaction(async (manager) => {
      const bidRepo = manager.getRepository(BidEntity)
      const bidHistoryRepo = manager.getRepository(BidHistoryEntity)
      const prRepo = manager.getRepository(PrEntity)
      const prItemRepo = manager.getRepository(PrItemEntity)

      const bid = await bidRepo.findOne({
        where: { id: bidId },
        select: { id: true, status: true, isRequestDelete: true, prId: true },
      })
      if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)

      // if (bid.status !== enumData.BidStatus.GoiThauTam.code && !bid.isRequestDelete) {
      //   throw new Error('Chỉ được xóa gói thầu nếu là gói thầu tạm hoặc có yêu cầu hủy gói thầu từ Nhân viên phụ trách mua hàng')
      // }

      await bidRepo.update(bidId, { isDeleted: true, updatedBy: user.id })

      // Cập nhật lại Pr nếu gói thầu tạo từ PR
      if (bid.prId) {
        const lstBidItem = await bidRepo.find({
          where: { parentId: bidId, isDeleted: false },
          select: { id: true, prItemId: true, quantityItem: true },
        })
        for (const bidItem of lstBidItem) {
          // Nếu DS ITEM từ PR thì update lại quantityBid cho PR
          if (bidItem.prItemId) {
            const prItem = await prItemRepo.findOne({
              where: { id: bidItem.prItemId, isDeleted: false },
              select: { id: true, quantityBid: true },
            })
            if (!prItem) throw new Error('prItem không còn tồn tại!')

            prItem.quantityBid -= bidItem.quantityItem
            await prItemRepo.update(prItem.id, { quantityBid: prItem.quantityBid, updatedBy: user.id })
          }
        }

        const pr = await prRepo.findOne({ where: { id: bid.prId }, select: { id: true, isAllowBid: true } })
        if (pr && !pr.isAllowBid) await prRepo.update(pr.id, { isAllowBid: true, updatedBy: user.id })
      }

      // Bid History
      const bidHistory = new BidHistoryEntity()
      bidHistory.companyId = user.companyId
      bidHistory.createdBy = user.id
      bidHistory.bidId = bidId
      bidHistory.employeeId = user.employeeId
      bidHistory.status = enumData.BidHistoryStatus.XacNhanHuyGoiThau.code
      await bidHistoryRepo.save(bidHistory)
    })
  }

  /** Xác nhận hủy gói thầu */
  async deleteBid(user: UserDto, bidId: string) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const objPermission = await this.checkPermissionMpoDelete(user, bidId)
    if (!objPermission.hasPermission) throw new MethodNotAllowedException(objPermission.message)

    await this.confirmDeleteBid(bidId, user)

    //send mail cho các Doanh nghiệp khi MPOLeader phê duyệt hủy gói thầu
    await this.emailService.ThongBaoHuyGoiThauNCC(bidId)

    return { message: 'Xóa gói thầu thành công.' }
  }

  /** Kiểm tra quyền sửa template gói thầu, nếu đã xác nhận tham gia thì không được sửa nữa (trừ trường hợp hiệu chỉnh giá) */
  async checkPermissionMpoEditTemplate(user: UserDto, bidId: string) {
    const bid = await this.repo.findOne({ where: { id: bidId } })
    if (bid && bid.statusResetPrice == enumData.BidResetPriceStatus.DangTao.code) {
      return true
    }

    let result = true
    const lstBidSupplierStatus = [
      enumData.BidSupplierStatus.DaXacNhanThamGiaThau.code,
      enumData.BidSupplierStatus.DaHoanThanhBoSungHoSo.code,
      enumData.BidSupplierStatus.DangDanhGia.code,
      enumData.BidSupplierStatus.DaDanhGia.code,
    ]

    const lstBidSupplier = await this.bidSupplierRepo.find({
      where: { bidId, status: In(lstBidSupplierStatus) },
      select: { id: true },
    })

    if (lstBidSupplier.length > 0) {
      result = false
    }

    return result
  }

  //#endregion
}
