import { MigrationInterface, QueryRunner } from "typeorm";

export class RevertNameColumPoPr1753064103947 implements MigrationInterface {
    name = 'RevertNameColumPoPr1753064103947'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "po_pr" DROP CONSTRAINT "FK_667faf8aa18019a0be5969f00a3"`);
        await queryRunner.query(`EXEC sp_rename "ktg-dev.dbo.po_pr.prId", "prIds"`);
        await queryRunner.query(`ALTER TABLE "po_pr" ADD CONSTRAINT "FK_529f0b9999d77fe7a93eba692f1" FOREIGN KEY ("prIds") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "po_pr" DROP CONSTRAINT "FK_529f0b9999d77fe7a93eba692f1"`);
        await queryRunner.query(`EXEC sp_rename "ktg-dev.dbo.po_pr.prIds", "prId"`);
        await queryRunner.query(`ALTER TABLE "po_pr" ADD CONSTRAINT "FK_667faf8aa18019a0be5969f00a3" FOREIGN KEY ("prId") REFERENCES "pr"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
