import { Module } from '@nestjs/common'
import { TypeOrmExModule } from '../../typeorm'
import {
  BusinessTemplateGroupPlanRepository,
  BusinessTemplatePlanRepository,
  BusinessTemplatePlanSetupRepository,
  BusinessTemplatePlanTypeRepository,
} from '../../repositories/businessTemplatePlan.repository'
import { BusinessTemplatePlanController } from './businessTemplatePlan.controller'
import { BusinessTemplatePlanService } from './businessTemplatePlan.service'
import { RfqModule } from '../rfq/rfq.module'
import { FlowApproveModule } from '../flowApprove/flowApprove.module'
import { CostRepository } from '../../repositories'

@Module({
  imports: [
    TypeOrmExModule.forCustomRepository([
      BusinessTemplatePlanRepository,
      BusinessTemplatePlanSetupRepository,
      BusinessTemplatePlanTypeRepository,
      BusinessTemplateGroupPlanRepository,
      CostRepository,
    ]),
    RfqModule,

    FlowApproveModule,
  ],
  controllers: [BusinessTemplatePlanController],
  providers: [BusinessTemplatePlanService],
  exports: [BusinessTemplatePlanService],
})
export class BusinessTemplatePlaneModule {}
