import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddColumnAppendix1753245022917 implements MigrationInterface {
  name = 'AddColumnAppendix1753245022917'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "costCenterCode" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "assetDesc" nvarchar(max)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "ioName" nvarchar(max)`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" ADD "iotype" nvarchar(max)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "po_item" DROP COLUMN "totalPrice"`)

    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "iotype"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "ioName"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "assetDesc"`)
    await queryRunner.query(`ALTER TABLE "contract_appendix_item" DROP COLUMN "costCenterCode"`)
  }
}
