import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm'
import { BaseEntity } from './base.entity'
import { PlantEntity } from './plant.entity'
import { BusinessTemplatePlanTypeEntity } from './businessTemplatePlanType.entity'
import { BusinessTemplatePlanEntity } from './businessTemplatePlan.entity'

@Entity('business_template_group_plan')
export class BusinessTemplateGroupPlanEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: true,
  })
  status: string

  /** Mã */
  @Column({
    type: 'varchar',
    nullable: true,
  })
  title: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  code: string

  /*  plantId*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  plantId: string

  @Column({
    type: 'varchar',
    nullable: true,
    length: 'max',
    transformer: {
      to(value) {
        if (Array.isArray(value) && value) {
          return value.join(',')
        }
        return value
        // Nếu không phải mảng, trả về giá trị gốc
      },
      from(value) {
        if (typeof value === 'string' && value) {
          return value
            .split(',')
            .map((item) => item.trim())
            .filter((item) => item) // Tách chuỗi thành mảng và loại bỏ khoảng trắng
        }
        return []
      },
    },
  })
  rfqCompactId: string[]
  @ManyToOne(() => PlantEntity, (c) => c.businessTemplateGroupPlan)
  @JoinColumn({ name: 'plantId', referencedColumnName: 'id' })
  plant: Promise<PlantEntity>

  /*  Loại template*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  businessTemplatePlanTypeId: string
  @ManyToOne(() => BusinessTemplatePlanTypeEntity, (c) => c.businessTemplateGroupPlan)
  @JoinColumn({ name: 'businessTemplatePlanTypeId', referencedColumnName: 'id' })
  businessTemplatePlanType: Promise<BusinessTemplatePlanTypeEntity>

  @OneToMany(() => BusinessTemplatePlanEntity, (p) => p.businessTemplateGroupPlan)
  businessTemplatePlan: Promise<BusinessTemplatePlanEntity[]>
}
