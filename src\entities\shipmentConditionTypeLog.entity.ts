import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm'
import { ShipmentConditionTypeEntity } from './shipmentCondictionType.entity'
import { ShipmentConditionTypeTemplateEntity } from './shipmentCondictionTypeTemplate.entity'
import { BaseEntity } from './base.entity'

@Entity('shipment_condition_type_log')
export class ShipmentConditionTypeLogEntity extends BaseEntity {
  /*condition type tempplate*/
  @Column({
    type: 'varchar',
    nullable: true,
  })
  shipmentConditionTypeTemplateId: string
  @ManyToOne(() => ShipmentConditionTypeTemplateEntity, (p) => p.shipmentConditionTypeLog)
  @JoinColumn({ name: 'shipmentConditionTypeTemplateId', referencedColumnName: 'id' })
  shipmentConditionTypeTemplate: Promise<ShipmentConditionTypeTemplateEntity>
  /* date from */
  @Column({
    nullable: true,
    type: 'datetime',
  })
  dateFrom: Date

  /* date to*/
  @Column({
    nullable: true,
    type: 'datetime',
  })
  dateTo: Date

  /* giá */
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  price: number
  /* giá quý */
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  priceQuarter1: number
  /* giá quý */
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  priceQuarter2: number
  /* giá quý */
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  priceQuarter3: number
  /* giá quý */
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  priceQuarter4: number
  /* giá từ giá năm */
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  formatPrice: number
  /* mã condition type*/
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string
  /* name condition type */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: false,
  })
  name: string
}
