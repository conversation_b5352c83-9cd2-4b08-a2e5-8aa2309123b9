import { MigrationInterface, QueryRunner } from 'typeorm'

export class BusinessTemplatePlan1753418012866 implements MigrationInterface {
  name = 'BusinessTemplatePlan1753418012866'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_plan" ADD "rfqCompactId" varchar(max)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "business_template_plan" DROP COLUMN "rfqCompactId"`)
  }
}
