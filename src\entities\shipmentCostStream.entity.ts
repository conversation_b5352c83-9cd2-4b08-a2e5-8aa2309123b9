import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany } from 'typeorm'

/** Thông tin shipment cost type*/
@Entity('shipment_cost_stream')
export class ShipmentCostStreamEntity extends BaseEntity {
  /** Mã */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  /** Trạng thái */
  @Column({
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status: string

  /** Tên */
  @Column({
    type: 'varchar',
    length: 'max',
    nullable: true,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 4000,
    nullable: true,
  })
  description: string

  @Column({
    nullable: true,
  })
  price: number
}
