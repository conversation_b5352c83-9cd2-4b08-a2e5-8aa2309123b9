import { MigrationInterface, QueryRunner } from 'typeorm'

export class CreateItem1753236975094 implements MigrationInterface {
  name = 'CreateItem1753236975094'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "bid_price" ADD "itemNo" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "bid_price" ADD "materialCode" varchar(255)`)
    await queryRunner.query(`ALTER TABLE "bid_price" ADD "category" varchar(1)`)
    await queryRunner.query(`ALTER TABLE "bid_price" ADD "materialId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "bid_price" ADD "materialGroupId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "bid_price" ADD "orderCode" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "bid_price" ADD "orderName" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "bid_price" ADD "assetCode" nvarchar(max)`)
    await queryRunner.query(`ALTER TABLE "bid_price" ADD "assetDesc" nvarchar(max)`)
    await queryRunner.query(`ALTER TABLE "bid_price" ADD "plantId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "bid_price" ADD "sloc" nvarchar(max)`)
    await queryRunner.query(`ALTER TABLE "bid_price" ADD "quantity" float CONSTRAINT "DF_1a05d7c36224a56d4ec2c7506fc" DEFAULT 0`)
    await queryRunner.query(`ALTER TABLE "bid_price" ADD "deliveryDate" datetime`)
    await queryRunner.query(`ALTER TABLE "bid_price" ADD "shortText" varchar(1000)`)
    await queryRunner.query(`ALTER TABLE "bid_price" ADD "externalMaterialGroupId" uniqueidentifier`)
    await queryRunner.query(`ALTER TABLE "bid_price" ADD "uomId" uniqueidentifier`)
    await queryRunner.query(
      `ALTER TABLE "bid_price" ADD CONSTRAINT "FK_b09cbeaa20255713d5742696ce2" FOREIGN KEY ("materialId") REFERENCES "material"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "bid_price" ADD CONSTRAINT "FK_042921771e0b6b2451dd8ef0b18" FOREIGN KEY ("materialGroupId") REFERENCES "material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "bid_price" ADD CONSTRAINT "FK_697eedc2edb934f674eac040cf3" FOREIGN KEY ("plantId") REFERENCES "plant"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "bid_price" ADD CONSTRAINT "FK_ea4ce47a3412696664923912648" FOREIGN KEY ("externalMaterialGroupId") REFERENCES "external_material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
    await queryRunner.query(
      `ALTER TABLE "bid_price" ADD CONSTRAINT "FK_693e5f5fe78569dbd23f2eec67d" FOREIGN KEY ("uomId") REFERENCES "uom"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "bid_price" DROP CONSTRAINT "FK_693e5f5fe78569dbd23f2eec67d"`)
    await queryRunner.query(`ALTER TABLE "bid_price" DROP CONSTRAINT "FK_ea4ce47a3412696664923912648"`)
    await queryRunner.query(`ALTER TABLE "bid_price" DROP CONSTRAINT "FK_697eedc2edb934f674eac040cf3"`)
    await queryRunner.query(`ALTER TABLE "bid_price" DROP CONSTRAINT "FK_042921771e0b6b2451dd8ef0b18"`)
    await queryRunner.query(`ALTER TABLE "bid_price" DROP CONSTRAINT "FK_b09cbeaa20255713d5742696ce2"`)
    await queryRunner.query(`ALTER TABLE "bid_price" DROP COLUMN "uomId"`)
    await queryRunner.query(`ALTER TABLE "bid_price" DROP COLUMN "externalMaterialGroupId"`)
    await queryRunner.query(`ALTER TABLE "bid_price" DROP COLUMN "shortText"`)
    await queryRunner.query(`ALTER TABLE "bid_price" DROP COLUMN "deliveryDate"`)
    await queryRunner.query(`ALTER TABLE "bid_price" DROP CONSTRAINT "DF_1a05d7c36224a56d4ec2c7506fc"`)
    await queryRunner.query(`ALTER TABLE "bid_price" DROP COLUMN "quantity"`)
    await queryRunner.query(`ALTER TABLE "bid_price" DROP COLUMN "sloc"`)
    await queryRunner.query(`ALTER TABLE "bid_price" DROP COLUMN "plantId"`)
    await queryRunner.query(`ALTER TABLE "bid_price" DROP COLUMN "assetDesc"`)
    await queryRunner.query(`ALTER TABLE "bid_price" DROP COLUMN "assetCode"`)
    await queryRunner.query(`ALTER TABLE "bid_price" DROP COLUMN "orderName"`)
    await queryRunner.query(`ALTER TABLE "bid_price" DROP COLUMN "orderCode"`)
    await queryRunner.query(`ALTER TABLE "bid_price" DROP COLUMN "materialGroupId"`)
    await queryRunner.query(`ALTER TABLE "bid_price" DROP COLUMN "materialId"`)
    await queryRunner.query(`ALTER TABLE "bid_price" DROP COLUMN "category"`)
    await queryRunner.query(`ALTER TABLE "bid_price" DROP COLUMN "materialCode"`)
    await queryRunner.query(`ALTER TABLE "bid_price" DROP COLUMN "itemNo"`)
  }
}
