import { MigrationInterface, QueryRunner } from 'typeorm'

export class UpdateTemplate1753165054047 implements MigrationInterface {
  name = 'UpdateTemplate1753165054047'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" ADD "quantity" int`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" ADD "uomCodeQuantity" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" ADD "uomIdQuantity" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" ADD "volume" float`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" ADD "uomCodeVolume" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" ADD "uomIdVolume" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" ADD "weight" float`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" ADD "uomCodeWeight" varchar(50)`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" ADD "uomIdWeight" varchar(50)`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" DROP COLUMN "uomIdWeight"`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" DROP COLUMN "uomCodeWeight"`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" DROP COLUMN "weight"`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" DROP COLUMN "uomIdVolume"`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" DROP COLUMN "uomCodeVolume"`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" DROP COLUMN "volume"`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" DROP COLUMN "uomIdQuantity"`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" DROP COLUMN "uomCodeQuantity"`)
    await queryRunner.query(`ALTER TABLE "shipment_plan_number" DROP COLUMN "quantity"`)
  }
}
