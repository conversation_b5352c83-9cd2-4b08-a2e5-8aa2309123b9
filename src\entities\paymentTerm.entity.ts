import { ContractAppendixEntity, ContractEntity, MaterialGroupEntity, RfqEntity, SupplierNumberRequestApproveEntity, SupplierServiceEntity } from '.'
import { BaseEntity } from './base.entity'
import { Entity, Column, OneToMany } from 'typeorm'
import { RoleFiSupplierEntity } from './roleFiSupplier.entity'
import { PaymentTermConversionEntity } from './paymentTermConversion.entity'
import { BusinessTemplatePlanEntity } from './businessTemplatePlan.entity'

@Entity('payment_term')
export class PaymentTermEntity extends BaseEntity {
  @Column({
    type: 'nvarchar',
    length: 500,
    nullable: false,
  })
  name: string

  @Column({
    type: 'varchar',
    length: 50,
    nullable: false,
  })
  code: string

  @Column({
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  description: string

  @OneToMany(() => SupplierNumberRequestApproveEntity, (p) => p.paymentTerm)
  supplierNumberRequestApproves: Promise<SupplierNumberRequestApproveEntity[]>

  @OneToMany(() => BusinessTemplatePlanEntity, (p) => p.paymentTerm)
  businessTemplatePlans: Promise<BusinessTemplatePlanEntity[]>

  @OneToMany(() => RoleFiSupplierEntity, (p) => p.paymentTerm)
  roleFiSuppliers: Promise<RoleFiSupplierEntity[]>

  @OneToMany(() => SupplierServiceEntity, (p) => p.paymentTerm)
  supplierServices: Promise<SupplierServiceEntity[]>

  @OneToMany(() => MaterialGroupEntity, (p) => p.paymentTerm)
  materialGroups: Promise<MaterialGroupEntity[]>

  @OneToMany(() => ContractEntity, (p) => p.paymentTerm)
  contracts: Promise<ContractEntity[]>

  @OneToMany(() => RfqEntity, (p) => p.paymentTerm)
  rfqs: Promise<RfqEntity[]>

  @OneToMany(() => ContractAppendixEntity, (p) => p.paymentTerm)
  appendixs: Promise<ContractAppendixEntity[]>

  @OneToMany(() => PaymentTermConversionEntity, (p) => p.paymentTerm)
  paymentTermConversions: Promise<PaymentTermConversionEntity[]>
}
