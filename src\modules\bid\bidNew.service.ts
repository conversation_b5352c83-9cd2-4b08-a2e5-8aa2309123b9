import { BadRequestException, Injectable, NotAcceptableException } from '@nestjs/common'
import { BidRepository, BidEmployeeAccessRepository } from '../../repositories'
import { EmailService } from '../email/email.service'
import { FlowApproveService } from '../flowApprove/flowApprove.service'
import { PaginationDto, UserDto } from '../../dto'
import { BidCreateDto, BidUpdateDto } from './dto'
import * as moment from 'moment'
import { Between, In, IsNull, LessThanOrEqual, Like, MoreThanOrEqual, Repository } from 'typeorm'
import { ERROR_YOU_DO_NOT_HAVE_PERMISSION, enumData, CREATE_SUCCESS, UPDATE_SUCCESS, ERROR_NOT_FOUND_DATA } from '../../constants'
import { BidEntity, BidEmployeeAccessEntity, BidH<PERSON>oryEntity, MediaFileEntity, BidPriceEntity, BidSupplierEntity } from '../../entities'
import { coreHelper } from '../../helpers'
import { v4 as uuidv4 } from 'uuid'
import { RoleDataPermission } from '../../constants/permission'
@Injectable()
export class BidNewService {
  constructor(
    private readonly repo: BidRepository,
    private readonly emailService: EmailService,
    private readonly bidEmployeeAccessRepo: BidEmployeeAccessRepository,
    private readonly flowService: FlowApproveService,
  ) {}

  private async findLastSort(user: UserDto, bidRepo: Repository<BidEntity>, code: string) {
    const objData = await bidRepo.findOne({
      where: {
        code: Like(`%${code}%`),
        parentId: IsNull(),
        companyId: user.companyId,
      },
      order: { code: 'DESC' },
      select: { id: true, code: true },
    })

    if (!objData) return 0

    const sortString = objData.code.slice(-3)
    return +sortString
  }

  async createBidNew(user: UserDto, data: BidCreateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    const dateNow = new Date()
    if (data.lstDetail.length == 0) throw new BadRequestException('Vui lòng thiết lập danh sách Item.')

    const codeTemp = moment(dateNow).format('YYYYMM')
    const lastSort = await this.findLastSort(user, this.repo, codeTemp + '_')
    const sortString = ('00' + (lastSort + 1)).slice(-3)
    data.code = codeTemp + '_' + sortString
    data.publicDate = dateNow
    data.startBidDate = new Date(data.submitEndDate)
    data.startBidDate.setDate(data.startBidDate.getDate() + 1)
    data.startBidDate.setHours(8, 0, 0, 0)
    if (data.startBidDate >= data.timeCheckTechDate && !data.isSurvey) {
      throw new BadRequestException('Thời điểm mở thầu phải sớm hơn thời hạn đánh giá yêu cầu kỹ thuật, năng lực.')
    }

    await this.repo.manager.transaction(async (manager) => {
      const bidRepo = manager.getRepository(BidEntity)
      const bidEmployeeAccessRepo = manager.getRepository(BidEmployeeAccessEntity)
      const bidHistoryRepo = manager.getRepository(BidHistoryEntity)
      const mediaFileRepo = manager.getRepository(MediaFileEntity)
      const bidPriceRepo = manager.getRepository(BidPriceEntity)
      const bidSupplierRepo = manager.getRepository(BidSupplierEntity)

      let bidId = null

      const bid = new BidEntity()
      bid.id = uuidv4()
      bid.companyId = data.companyId
      bid.createdBy = user.id
      bid.code = data.code
      bid.name = data.name

      bid.prId = data.prId
      bid.isSurvey = data.isSurvey
      bid.exMatGroupId = data.exMatGroupId
      bid.serviceInvite = data.serviceInvite
      bid.skipApprove = data.skipApprove
      bid.shipmentId = data.shipmentId
      bid.acceptEndDate = data.acceptEndDate
      bid.isCompleteAll = data.isCompleteAll
      bid.biddingTypeCode = data.biddingTypeCode
      bid.plantId = data.plantId
      bid.pmOrder = data.pmOrder
      bid.businessPlantId = data.businessPlanId

      bid.isLoadFromBusinessPlan = data.isLoadFromBusinessPlan
      bid.refType = data.refType
      bid.isGetFromPr = data.isGetFromPr
      bid.shipmentId = data.shipmentId
      bid.submitEndDate = data.submitEndDate
      bid.addressSubmit = data.addressSubmit ? data.addressSubmit : ''
      bid.companyInvite = data.companyInvite
      bid.companyInviteId = data.companyInviteId
      bid.serviceInvite = data.serviceInvite
      bid.listAddress = data.listAddress ? data.listAddress : ''
      bid.publicDate = data.publicDate
      bid.bidTypeId = data.bidTypeId
      bid.timeserving = data.timeserving
      bid.scoreDLC = data.scoreDLC
      bid.startBidDate = data.startBidDate
      bid.moneyGuarantee = data.moneyGuarantee
      bid.timeGuarantee = data.timeGuarantee
      bid.masterBidGuaranteeId = data.masterBidGuaranteeId
      bid.timeTechDate = data.timeTechDate
      bid.timePriceDate = data.timePriceDate
      bid.timeCheckTechDate = data.timeCheckTechDate
      bid.timeCheckPriceDate = data.timeCheckPriceDate
      bid.status = enumData.BidStatusNew.N.code

      bid.isShowHomePage = data.isShowHomePage
      bid.isSendEmailInviteBid = data.isSendEmailInviteBid
      bid.isAutoBid = data.isAutoBid
      bid.fileDrawing = data.fileDrawing
      bid.fileJD = data.fileJD
      bid.fileKPI = data.fileKPI
      bid.fileRule = data.fileRule
      bid.fileDocument = data.fileDocument
      bid.fileAnother = data.fileAnother

      bid.statusTech = enumData.BidTechStatus.DangTao.code
      bid.statusTrade = enumData.BidTradeStatus.DangTao.code
      bid.statusPrice = enumData.BidPriceStatus.DangTao.code
      bid.statusRateTech = enumData.BidTechRateStatus.ChuaTao.code
      bid.statusRateTrade = enumData.BidTradeRateStatus.ChuaTao.code
      bid.statusRatePrice = enumData.BidPriceRateStatus.ChuaTao.code
      bid.status = enumData.BidStatusNew.N.code
      bid.reference = data.reference
      bid.lstIncotermId = data.lstIncotermId
      bid.lstPaymentTermId = data.lstPaymentTermId

      const bidEntity = await bidRepo.save(bid)
      bidId = bidEntity.id

      //#region "Language"
      const createLanData: any = bidEntity
      createLanData.nameEN = data.nameEN
      const languageObj = await coreHelper.checkENValue([createLanData], BidEntity.name)

      await this.repo.saveOrUpdateTranslate(languageObj)

      // MPO
      const mpoAccess = new BidEmployeeAccessEntity()
      mpoAccess.companyId = data.companyId
      mpoAccess.createdBy = user.id
      mpoAccess.bidId = bidEntity.id
      mpoAccess.employeeId = data.mpoId
      mpoAccess.type = enumData.BidRuleType.MPO.code
      await bidEmployeeAccessRepo.save(mpoAccess)

      data.techMemberId = data.techMemberId || []

      for (const id of data.techMemberId) {
        const techAccess = new BidEmployeeAccessEntity()
        techAccess.companyId = user.companyId
        techAccess.isMember = true
        techAccess.createdBy = user.id
        techAccess.bidId = bidEntity.id
        techAccess.employeeId = id
        techAccess.type = enumData.BidRuleType.Tech.code
        await bidEmployeeAccessRepo.save(techAccess)
      }
      // Member
      data.anotherRoleIds = data.anotherRoleIds || []
      for (const item of data.anotherRoleIds) {
        const anotherAccess = new BidEmployeeAccessEntity()
        anotherAccess.companyId = user.companyId
        anotherAccess.createdBy = user.id
        anotherAccess.bidId = bidEntity.id
        anotherAccess.employeeId = item
        anotherAccess.type = enumData.BidRuleType.Memmber.code
        await bidEmployeeAccessRepo.save(anotherAccess)
      }

      // Other
      data.otherRoleIds = data.otherRoleIds || []
      for (const item of data.otherRoleIds) {
        const otherAccess = new BidEmployeeAccessEntity()
        otherAccess.companyId = data.companyId
        otherAccess.createdBy = user.id
        otherAccess.bidId = bidEntity.id
        otherAccess.employeeId = item
        otherAccess.type = enumData.BidRuleType.Other.code
        await bidEmployeeAccessRepo.insert(otherAccess)
      }
      //#endregion

      // Bid History
      const bidHistory = new BidHistoryEntity()
      bidHistory.companyId = data.companyId
      bidHistory.createdBy = user.id
      bidHistory.bidId = bidEntity.id
      bidHistory.employeeId = user.employeeId
      bidHistory.status = enumData.BidHistoryStatus.TaoGoiThau.code
      await bidHistoryRepo.insert(bidHistory)

      if (data.lstMediaFile && data.lstMediaFile.length > 0) {
        for (const file of data.lstMediaFile) {
          const newImg = new MediaFileEntity()
          newImg.bidId = bidEntity.id
          newImg.fileUrl = file.fileUrl
          newImg.fileName = file.fileName
          newImg.createdAt = new Date()
          newImg.createdBy = user.id
          await mediaFileRepo.insert(newImg)
        }
      }

      for (const supplierId of data.lstSupplier) {
        const newBidSupplier = new BidSupplierEntity()
        newBidSupplier.companyId = data.companyId
        newBidSupplier.materialGroupId = supplierId.materialGroupId
        newBidSupplier.createdBy = user.id
        newBidSupplier.createdAt = new Date()
        newBidSupplier.supplierId = supplierId?.supplierId
        newBidSupplier.bidId = bidEntity.id
        newBidSupplier.status = enumData.BidSupplierStatus.DaDuocChon.code
        newBidSupplier.statusFile = enumData.BidSupplierFileStatus.ChuaKiemTra.code
        newBidSupplier.statusTech = enumData.BidSupplierTechStatus.ChuaXacNhan.code
        newBidSupplier.statusTrade = enumData.BidSupplierTradeStatus.ChuaXacNhan.code
        newBidSupplier.statusPrice = enumData.BidSupplierPriceStatus.ChuaXacNhan.code
        newBidSupplier.scoreTech = newBidSupplier.scoreTrade = newBidSupplier.scorePrice = 0
        await bidSupplierRepo.insert(newBidSupplier)
      }

      if (data.lstDetail && data.lstDetail.length > 0) {
        for (let item of data.lstDetail) {
          const detail = new BidPriceEntity()
          detail.bidId = bidEntity.id
          detail.itemNo = item.itemNo
          detail.assetCode = item.assetCode
          detail.category = item.category
          detail.materialId = item.materialId
          detail.materialGroupId = item.materialGroupId
          detail.shortText = item.shortText
          detail.deliveryDate = item.deliveryDate
          detail.orderCode = item.orderCode
          detail.plantId = item.plantId
          detail.quantity = item.quantity
          detail.assetDesc = item.assetDesc
          detail.sloc = item.sloc

          detail.orderName = item.orderName
          detail.createdAt = new Date()
          detail.createdBy = user.id
          detail.externalMaterialGroupId = item.externalMaterialGroupId
          detail.uomId = item.unitId
          await bidPriceRepo.insert(detail)
        }
      }

      await this.flowService.setRoleRule(user, {
        targetId: bidEntity.id,
        target: bidEntity,
        entityName: BidEntity.name,
        flowType: enumData.FlowCode.BID.code,
      })
    })
    return { message: CREATE_SUCCESS }
  }

  async paginationSurvey(user: UserDto, data: PaginationDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)
    let whereCon: any = { parentId: IsNull(), isDeleted: false, isSurvey: false }

    if (data.where.serviceId) whereCon.childs = { serviceId: data.where.serviceId }
    if (data.where.status) whereCon.status = data.where.status
    if (data.where.statusTech) whereCon.statusTech = data.where.statusTech
    if (data.where.statusTrade) whereCon.statusTrade = data.where.statusTrade
    if (data.where.statusPrice) whereCon.statusPrice = data.where.statusPrice
    if (data.where.statusChooseSupplier) whereCon.statusChooseSupplier = data.where.statusChooseSupplier
    if (data.where.statusRateTech) whereCon.statusRateTech = data.where.statusRateTech
    if (data.where.statusRateTrade) whereCon.statusRateTrade = data.where.statusRateTrade
    if (data.where.statusRatePrice) whereCon.statusRatePrice = data.where.statusRatePrice

    if (data.where.type) whereCon.type = data.where.type

    if (data.where.status) whereCon.status = data.where.status
    if (data.where.statusTech) whereCon.statusTech = data.where.statusTech // yeu cau ky thuat
    if (data.where.statusPrice) whereCon.statusPrice = data.where.statusPrice // bang gia, co cau gia
    if (data.where.statusTrade) whereCon.statusTrade = data.where.statusTrade // dieu kien thuong mai
    if (data.where.statusRateTech) whereCon.statusRateTech = data.where.statusRateTech
    if (data.where.statusRateTrade) whereCon.statusRateTrade = data.where.statusRateTrade
    if (data.where.statusRatePrice) whereCon.statusRatePrice = data.where.statusRatePrice
    if (data.where.statusChooseSupplier) whereCon.statusChooseSupplier = data.where.statusChooseSupplier

    if (data.where.status?.length > 0) whereCon.status = data.where.status

    if (data.where.isSurvey) {
      whereCon.isSurvey = true
    }
    if (data.where.dateFrom && data.where.dateTo) {
      whereCon.publicDate = Between(new Date(data.where.dateFrom), new Date(data.where.dateTo))
    } else if (data.where.dateFrom) {
      whereCon.publicDate = MoreThanOrEqual(new Date(data.where.dateFrom))
    } else if (data.where.dateTo) {
      whereCon.publicDate = LessThanOrEqual(new Date(data.where.dateTo))
    }

    // Tìm theo mã số hoặc tên gói thầu
    if (data.where.name) {
      whereCon = [
        { ...whereCon, code: Like(`%${data.where.name}%`) },
        { ...whereCon, name: Like(`%${data.where.name}%`) },
      ]
    }

    if (data.where.listTargetId) whereCon.id = In(data.where.listTargetId)
    const res: any[] = await this.repo.findAndCount(
      {
        where: whereCon,
        skip: data.skip,
        take: data.take,
        order: { createdAt: 'DESC', code: 'DESC' },
      },
      user,
      RoleDataPermission.BidNewSurvey.code,
    )
    if (res[0].length == 0) return res
    const lstId = res[0].map((c) => c.id)
    const lstEmployeeAccess: any[] = await this.bidEmployeeAccessRepo.find({
      where: { bidId: In(lstId), isDeleted: false },

      relations: { employee: true },
      select: {
        id: true,
        bidId: true,
        type: true,
        employeeId: true,
        employee: {
          id: true,
          name: true,
        },
      },
    })

    const dicStatus: any = {}
    {
      const lstStatus = coreHelper.convertObjToArray(enumData.BidStatusNew)
      lstStatus.forEach((c) => (dicStatus[c.code] = c))
    }

    for (const item of res[0]) {
      item.statusColor = enumData.RequestStatus[item.status]?.color
      item.statusBgColor = enumData.RequestStatus[item.status]?.bgColor
      item.statusBorderColor = enumData.RequestStatus[item.status]?.borderColor
      item.statusName = enumData.RequestStatus[item.status]?.name
      item.permission = await this.repo.getRowRole(user, item, RoleDataPermission.BidNewSurvey.code)
      // Kiểm tra quyền duyệt
      item.objPermissionApprove = await this.flowService.checkCanApproveByType(user, {
        lsType: [enumData.FlowCode.BID.code],
      })

      const revertOption = await this.flowService.checkCanRemoveApprove(user, {
        targetId: item.id,
        entityName: BidEntity.name,
        type: enumData.FlowCode.BID.code,
      })

      item.canRevert = revertOption.canRevert
      // Kiểm tra trạng thái phê duyệt
      const { canApprove, approvalProgress, level, maxLevel } = await this.flowService.getRoleApprove(user, {
        targetId: item.id,
        entityName: BidEntity.name,
        type: enumData.FlowCode.BID.code,
      })
      Object.assign(item, { approvalProgress, canApprove, level, maxLevel })

      item.isCanApprove = Object.values(item.objPermissionApprove).some((value) => value)
    }

    return res
  }

  async updateDataNew(user: UserDto, data: BidUpdateDto) {
    if (!user.employeeId) throw new NotAcceptableException(ERROR_YOU_DO_NOT_HAVE_PERMISSION)

    if (data.lstDetail.length == 0) throw new BadRequestException('Vui lòng thiết lập danh sách Item.')
    data.startBidDate = new Date(data.submitEndDate)
    data.startBidDate.setDate(data.startBidDate.getDate() + 1)
    data.startBidDate.setHours(8, 0, 0, 0)
    if (data.startBidDate >= data.timeCheckTechDate) {
      throw new BadRequestException('Thời điểm mở thầu phải sớm hơn thời hạn đánh giá yêu cầu kỹ thuật, năng lực.')
    }
    const bid = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
    if (!bid) throw new Error(ERROR_NOT_FOUND_DATA)
    await this.repo.manager.transaction(async (trans) => {
      const bidRepo = trans.getRepository(BidEntity)
      const bidHistoryRepo = trans.getRepository(BidHistoryEntity)
      const bidEmployeeAccessRepo = trans.getRepository(BidEmployeeAccessEntity)
      const mediaFileRepo = trans.getRepository(MediaFileEntity)
      const bidPriceRepo = trans.getRepository(BidPriceEntity)
      const bidSupplierRepo = trans.getRepository(BidSupplierEntity)

      bid.isShowHomePage = data.isShowHomePage
      bid.isSendEmailInviteBid = data.isSendEmailInviteBid
      bid.isAutoBid = data.isAutoBid
      bid.name = data.name
      bid.serviceInvite = data.serviceInvite
      bid.acceptEndDate = data.acceptEndDate
      bid.biddingTypeCode = data.biddingTypeCode
      bid.shipmentId = data.shipmentId
      bid.isLoadFromBusinessPlan = data.isLoadFromBusinessPlan
      bid.submitEndDate = data.submitEndDate
      bid.addressSubmit = data.addressSubmit ?? ''
      bid.companyInvite = data.companyInvite
      bid.isGetFromPr = data.isGetFromPr
      bid.companyId = data.companyId
      bid.listAddress = data.listAddress ?? ''
      bid.plantId = data.plantId
      bid.publicDate = data.publicDate
      bid.pmOrder = data.pmOrder
      bid.businessPlantId = data.businessPlanId
      bid.refType = data.refType
      bid.bidTypeId = data.bidTypeId
      bid.timeserving = data.timeserving
      bid.scoreDLC = data.scoreDLC
      bid.startBidDate = data.startBidDate
      bid.moneyGuarantee = data.moneyGuarantee
      bid.timeGuarantee = data.timeGuarantee
      bid.masterBidGuaranteeId = data.masterBidGuaranteeId
      bid.timeTechDate = data.timeTechDate
      bid.timePriceDate = data.timePriceDate
      bid.timeCheckTechDate = data.timeCheckTechDate
      bid.timeCheckPriceDate = data.timeCheckPriceDate
      bid.serviceInvite = data.serviceInvite
      bid.fileDrawing = data.fileDrawing
      bid.fileJD = data.fileJD
      bid.fileKPI = data.fileKPI
      bid.fileRule = data.fileRule
      bid.fileDocument = data.fileDocument
      bid.fileAnother = data.fileAnother
      bid.updatedBy = user.id
      bid.reference = data.reference
      bid.lstIncotermId = data.lstIncotermId
      bid.lstPaymentTermId = data.lstPaymentTermId
      const bidEntity = await this.repo.save(bid, {}, trans)

      await mediaFileRepo.delete({ bidId: bid.id })
      await bidEmployeeAccessRepo.delete({ bidId: bid.id })
      if (data.lstMediaFile && data.lstMediaFile.length > 0) {
        for (const file of data.lstMediaFile) {
          const newImg = new MediaFileEntity()
          newImg.bidId = bidEntity.id
          newImg.fileUrl = file.fileUrl
          newImg.fileName = file.fileName
          newImg.createdAt = new Date()
          newImg.createdBy = user.id
          await mediaFileRepo.insert(newImg)
        }
      }

      // MPO
      const mpoAccess = new BidEmployeeAccessEntity()
      mpoAccess.companyId = data.companyId
      mpoAccess.createdBy = user.id
      mpoAccess.bidId = bidEntity.id
      mpoAccess.employeeId = data.mpoId
      mpoAccess.type = enumData.BidRuleType.MPO.code
      await bidEmployeeAccessRepo.save(mpoAccess)

      data.techMemberId = data.techMemberId || []

      for (const id of data.techMemberId) {
        const techAccess = new BidEmployeeAccessEntity()
        techAccess.companyId = user.companyId
        techAccess.isMember = true
        techAccess.createdBy = user.id
        techAccess.bidId = bidEntity.id
        techAccess.employeeId = id
        techAccess.type = enumData.BidRuleType.Tech.code
        await bidEmployeeAccessRepo.save(techAccess)
      }
      // Member
      data.anotherRoleIds = data.anotherRoleIds || []
      for (const item of data.anotherRoleIds) {
        const anotherAccess = new BidEmployeeAccessEntity()
        anotherAccess.companyId = user.companyId
        anotherAccess.createdBy = user.id
        anotherAccess.bidId = bidEntity.id
        anotherAccess.employeeId = item
        anotherAccess.type = enumData.BidRuleType.Memmber.code
        await bidEmployeeAccessRepo.save(anotherAccess)
      }

      // Other
      data.otherRoleIds = data.otherRoleIds || []
      for (const item of data.otherRoleIds) {
        const otherAccess = new BidEmployeeAccessEntity()
        otherAccess.companyId = data.companyId
        otherAccess.createdBy = user.id
        otherAccess.bidId = bidEntity.id
        otherAccess.employeeId = item
        otherAccess.type = enumData.BidRuleType.Other.code
        await bidEmployeeAccessRepo.insert(otherAccess)
      }
      //#endregion

      //#region "Language"
      const createLanData: any = bidEntity
      createLanData.nameEN = data.nameEN
      const languageObj = await coreHelper.checkENValue([createLanData], BidEntity.name)
      await this.repo.saveOrUpdateTranslate(languageObj)
      //#endregion "Language"

      await bidSupplierRepo.delete({ bidId: bid.id })

      for (const supplierId of data.lstSupplier) {
        const newBidSupplier = new BidSupplierEntity()
        newBidSupplier.companyId = data.companyId
        newBidSupplier.materialGroupId = supplierId.materialGroupId
        newBidSupplier.createdBy = user.id
        newBidSupplier.createdAt = new Date()
        newBidSupplier.supplierId = supplierId?.supplierId
        newBidSupplier.bidId = bidEntity.id
        newBidSupplier.status = enumData.BidSupplierStatus.DaDuocChon.code
        newBidSupplier.statusFile = enumData.BidSupplierFileStatus.ChuaKiemTra.code
        newBidSupplier.statusTech = enumData.BidSupplierTechStatus.ChuaXacNhan.code
        newBidSupplier.statusTrade = enumData.BidSupplierTradeStatus.ChuaXacNhan.code
        newBidSupplier.statusPrice = enumData.BidSupplierPriceStatus.ChuaXacNhan.code
        newBidSupplier.scoreTech = newBidSupplier.scoreTrade = newBidSupplier.scorePrice = 0
        await bidSupplierRepo.insert(newBidSupplier)
      }

      await bidPriceRepo.delete({ bidId: bid.id })

      if (data.lstDetail && data.lstDetail.length > 0) {
        for (let item of data.lstDetail) {
          const detail = new BidPriceEntity()
          detail.bidId = bidEntity.id
          detail.itemNo = item.itemNo
          detail.assetCode = item.assetCode
          detail.category = item.category
          detail.materialId = item.materialId
          detail.materialGroupId = item.materialGroupId
          detail.shortText = item.shortText
          detail.deliveryDate = item.deliveryDate
          detail.orderCode = item.orderCode
          detail.plantId = item.plantId
          detail.quantity = item.quantity
          detail.assetDesc = item.assetDesc
          detail.sloc = item.sloc
          detail.orderName = item.orderName
          detail.createdAt = new Date()
          detail.createdBy = user.id
          detail.externalMaterialGroupId = item.externalMaterialGroupId
          detail.uomId = item.unitId
          await bidPriceRepo.insert(detail)
        }
      }

      // Bid History
      const bidHistory = new BidHistoryEntity()
      bidHistory.companyId = data.companyId
      bidHistory.createdBy = user.id
      bidHistory.bidId = bid.id
      bidHistory.employeeId = user.employeeId
      bidHistory.status = enumData.BidHistoryStatus.SuaTaoGoiThau.code
      await bidHistoryRepo.save(bidHistory)
    })

    return { message: UPDATE_SUCCESS }
  }
}
