import { IsString, Is<PERSON>ptional, IsArray, ValidateNested } from 'class-validator'
import { Type } from 'class-transformer'
import { BusinessPlanDto } from './BusinessTemplatePlanSave.dto'

export class BusinessPlanUpdateDto extends BusinessPlanDto {
  @IsOptional()
  @IsString()
  id?: string // Thêm id cho update
}

export class BusinessTemplatePlanUpdateDto {
  @IsString()
  id: string

  @IsString()
  plantId: string

  @IsOptional()
  title?: string

  @IsString()
  businessTemplatePlanTypeId: string

  @IsString()
  purchasingOrgId: string

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BusinessPlanUpdateDto)
  lstBusinessPlan: BusinessPlanUpdateDto[]
}
