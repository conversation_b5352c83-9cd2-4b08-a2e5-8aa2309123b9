import { Injectable } from '@nestjs/common'
import { Like, In } from 'typeorm'
import { ShipmentFeeConditionsListCreateDto, ShipmentFeeConditionsListUpdateDto } from '../../dto/shipmentFeeConditionsList.dto'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import {
  ShipmentFeeConditionsListRepository,
  ShipmentFeeConditionsRepository,
  ShipmentFeeConditionsToListRepository,
} from '../../repositories/shipmentTemplate.repository'
import { GetShipmentFeeConditionsListByPlanDto } from './dto/getByShipmentPlan.dto'
import { ShipmentPlanNumberRepository } from '../../repositories/shipmentPlan.repository'

@Injectable()
export class ShipmentFeeConditionsListService {
  constructor(
    private readonly repo: ShipmentFeeConditionsListRepository,
    private readonly shipmentFeeConditionsRepo: ShipmentFeeConditionsRepository,
    private readonly shipmentFeeConditionsToListRepo: ShipmentFeeConditionsToListRepository,
    private readonly shipmentPlanNumberRepo: ShipmentPlanNumberRepository,
  ) {}

  async create(user: UserDto, data: ShipmentFeeConditionsListCreateDto) {
    const countCode = (await this.repo.count()) + 1

    const entity = this.repo.create(data)
    entity.code = 'FCL' + countCode
    entity.createdBy = user.id
    entity.companyId = user.companyId
    return await this.repo.save(entity)
  }

  async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { isDeleted: false }
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    let res: any = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'ASC' },
    })
    return res
  }

  async findOne(data: FilterOneDto) {
    const existingEntity = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
    if (!existingEntity) {
      throw new Error(`Không tìm thấy điều kiện thiết lập phí list với ID: ${data.id}`)
    }
    return await this.repo.findOne({ where: { id: data.id } })
  }

  async update(user: UserDto, data: ShipmentFeeConditionsListUpdateDto) {
    const existingEntity = await this.repo.findOne({ where: { id: data.id } })
    if (!existingEntity) {
      throw new Error(`Không tìm thấy điều kiện thiết lập phí list với ID: ${data.id}`)
    }
    const dataToUpdate = this.repo.create(data)
    dataToUpdate.updatedBy = user.id
    await this.repo.update(data.id, dataToUpdate)
    return { message: 'Cập nhật thành công' }
  }

  async remove(user: UserDto, data: FilterOneDto) {
    const existingEntity = await this.repo.findOne({ where: { id: data.id } })
    if (!existingEntity) {
      throw new Error(`Không tìm thấy điều kiện thiết lập phí list với ID: ${data.id}`)
    }
    await this.repo.update(data.id, { isDeleted: true, updatedBy: user.id })
    return { message: 'Xóa thành công' }
  }

  /**
   * Lấy tất cả options cho FC2, FC3, FC6, FC8 (POD, POL, HU, Gross Weight)
   * @param user UserDto
   * @returns Promise<object>
   */
  async getAllOptions(user: UserDto) {
    const allowedCodes = ['FC2', 'FC3', 'FC6', 'FC8']

    // Lấy tất cả shipmentFeeConditions với codes được phép
    const shipmentFeeConditions = await this.shipmentFeeConditionsRepo.find({
      where: {
        code: In(allowedCodes),
        isDeleted: false,
      },
      select: {
        id: true,
        code: true,
      },
    })

    if (shipmentFeeConditions.length === 0) {
      return {
        FC2: [], // POD
        FC3: [], // POL
        FC6: [], // HU
        FC8: [], // Gross Weight
      }
    }

    const shipmentFeeConditionIds = shipmentFeeConditions.map((condition) => condition.id)

    // Lấy tất cả ShipmentFeeConditionsListEntity thông qua quan hệ nhiều-nhiều
    const shipmentFeeConditionsToList = await this.shipmentFeeConditionsToListRepo.find({
      where: {
        shipmentFeeConditionsId: In(shipmentFeeConditionIds),
        isDeleted: false,
      },
      relations: {
        shipmentFeeConditionsList: true,
      },
    })

    // Khởi tạo result object với các key tương ứng
    const result = {
      FC2: [], // POD
      FC3: [], // POL
      FC6: [], // HU
      FC8: [], // Gross Weight
    }

    // Group dữ liệu theo code
    for (const toListEntity of shipmentFeeConditionsToList) {
      const feeConditionsList = await toListEntity.shipmentFeeConditionsList
      if (feeConditionsList && !feeConditionsList.isDeleted) {
        // Tìm code tương ứng với shipmentFeeConditionsId
        const condition = shipmentFeeConditions.find((c) => c.id === toListEntity.shipmentFeeConditionsId)
        if (condition && result[condition.code]) {
          // Kiểm tra trùng lặp trước khi thêm
          const exists = result[condition.code].some((item) => item.id === feeConditionsList.id)
          if (!exists) {
            result[condition.code].push(feeConditionsList)
          }
        }
      }
    }

    return result
  }

  async getOptions(user: UserDto, data: GetShipmentFeeConditionsListByPlanDto) {
    const shipmentPlanNumber = await this.shipmentPlanNumberRepo.findOne({
      where: {
        shipmentPlanId: data.shipmentPlanId,
        isDeleted: false,
      },
      select: {
        id: true,
        shipmentFeeConditionTypeCompactCode: true,
        shipmentFeeConditionTypeCompactId: true,
      },
    })

    if (!shipmentPlanNumber) {
      return []
    }
    const allCompactCodes = shipmentPlanNumber.shipmentFeeConditionTypeCompactCode
    const allowedCodes = ['FC2', 'FC3', 'FC6', 'FC8']

    if (!allCompactCodes || !Array.isArray(allCompactCodes) || allCompactCodes.length === 0) {
      return {
        POL: [],
        POD: [],
        HU: [],
        GrossWeight: [],
      }
    }

    const compactCodes = allCompactCodes.filter((code) => allowedCodes.includes(code))

    if (compactCodes.length === 0) {
      return {
        POL: [],
        POD: [],
        HU: [],
        GrossWeight: [],
      }
    }

    const shipmentFeeConditions = await this.shipmentFeeConditionsRepo.find({
      where: {
        code: In(compactCodes),
        isDeleted: false,
      },
      select: {
        id: true,
        code: true,
        name: true,
      },
    })

    const shipmentFeeConditionIds = shipmentFeeConditions.map((condition) => condition.id)

    if (shipmentFeeConditionIds.length === 0) {
      return {
        POL: [],
        POD: [],
        HU: [],
        GrossWeight: [],
      }
    }
    const shipmentFeeConditionsToList = await this.shipmentFeeConditionsToListRepo.find({
      where: {
        shipmentFeeConditionsId: In(shipmentFeeConditionIds),
        isDeleted: false,
      },
      relations: {
        shipmentFeeConditionsList: true,
      },
    })

    const result = {
      POL: [],
      POD: [],
      HU: [],
      GrossWeight: [],
    }

    for (const toListEntity of shipmentFeeConditionsToList) {
      const feeConditionsList = await toListEntity.shipmentFeeConditionsList
      if (feeConditionsList && !feeConditionsList.isDeleted) {
        const condition = shipmentFeeConditions.find((c) => c.id === toListEntity.shipmentFeeConditionsId)
        if (condition && result[condition.name]) {
          const exists = result[condition.name].some((item) => item.id === feeConditionsList.id)
          if (!exists) {
            result[condition.name].push(feeConditionsList)
          }
        }
      }
    }

    return result
  }
}
