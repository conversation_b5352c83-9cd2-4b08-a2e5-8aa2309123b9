import { Injectable } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Like, Repository, In } from 'typeorm'
import { ShipmentFeeConditionsListEntity } from '../../entities/shipmentFeeConditionsList.entity'
import { ShipmentFeeConditionsListCreateDto, ShipmentFeeConditionsListUpdateDto } from '../../dto/shipmentFeeConditionsList.dto'
import { FilterOneDto, PaginationDto, UserDto } from '../../dto'
import {
  ShipmentFeeConditionsListRepository,
  ShipmentFeeConditionsRepository,
  ShipmentFeeConditionsToListRepository,
} from '../../repositories/shipmentTemplate.repository'
import { GetShipmentFeeConditionsListByPlanDto } from './dto/getByShipmentPlan.dto'
import { ShipmentPlanNumberRepository } from '../../repositories/shipmentPlan.repository'

@Injectable()
export class ShipmentFeeConditionsListService {
  constructor(
    private readonly repo: ShipmentFeeConditionsListRepository,
    private readonly shipmentFeeConditionsRepository: ShipmentFeeConditionsRepository,
    private readonly shipmentFeeConditionsToListRepository: ShipmentFeeConditionsToListRepository,
    private readonly shipmentPlanNumberRepository: ShipmentPlanNumberRepository,
  ) {}

  async create(user: UserDto, data: ShipmentFeeConditionsListCreateDto) {
    const countCode = (await this.repo.count()) + 1

    const entity = this.repo.create(data)
    entity.code = 'FCL' + countCode
    entity.createdBy = user.id
    entity.companyId = user.companyId
    return await this.repo.save(entity)
  }

  async pagination(user: UserDto, data: PaginationDto) {
    const whereCon: any = { isDeleted: false }
    if (data.where.name) whereCon.name = Like(`%${data.where.name}%`)
    if (data.where.code) whereCon.code = Like(`%${data.where.code}%`)
    if (data.where.isDeleted != undefined) whereCon.isDeleted = data.where.isDeleted

    let res: any = await this.repo.findAndCount({
      where: whereCon,
      skip: data.skip,
      take: data.take,
      order: { createdAt: 'ASC' },
    })
    return res
  }

  async findOne(data: FilterOneDto) {
    const existingEntity = await this.repo.findOne({ where: { id: data.id, isDeleted: false } })
    if (!existingEntity) {
      throw new Error(`Không tìm thấy điều kiện thiết lập phí list với ID: ${data.id}`)
    }
    return await this.repo.findOne({ where: { id: data.id } })
  }

  async update(user: UserDto, data: ShipmentFeeConditionsListUpdateDto) {
    const existingEntity = await this.repo.findOne({ where: { id: data.id } })
    if (!existingEntity) {
      throw new Error(`Không tìm thấy điều kiện thiết lập phí list với ID: ${data.id}`)
    }
    const dataToUpdate = this.repo.create(data)
    dataToUpdate.updatedBy = user.id
    await this.repo.update(data.id, dataToUpdate)
    return { message: 'Cập nhật thành công' }
  }

  async remove(user: UserDto, data: FilterOneDto) {
    const existingEntity = await this.repo.findOne({ where: { id: data.id } })
    if (!existingEntity) {
      throw new Error(`Không tìm thấy điều kiện thiết lập phí list với ID: ${data.id}`)
    }
    await this.repo.update(data.id, { isDeleted: true, updatedBy: user.id })
    return { message: 'Xóa thành công' }
  }

  /**
   * Lấy danh sách ShipmentFeeConditionsListEntity từ shipmentPlanId
   * Chỉ lấy một ShipmentPlanNumber đầu tiên tìm thấy và filter chỉ lấy codes: FC2, FC3, FC6, FC8
   * @param user UserDto
   * @param data GetShipmentFeeConditionsListByPlanDto
   * @returns Promise<ShipmentFeeConditionsListEntity[]>
   */
  async getByShipmentPlanId(user: UserDto, data: GetShipmentFeeConditionsListByPlanDto) {
    const shipmentPlanNumber = await this.shipmentPlanNumberRepository.findOne({
      where: {
        shipmentPlanId: data.shipmentPlanId,
        isDeleted: false,
      },
      select: {
        id: true,
        shipmentFeeConditionTypeCompactCode: true,
        shipmentFeeConditionTypeCompactId: true,
      },
    })

    if (!shipmentPlanNumber) {
      return []
    }

    // Lấy shipmentFeeConditionTypeCompactCode từ shipmentPlanNumber và filter lấy FC2, FC3, FC6, FC8 tương ứng POD, POL, HU, Gross Weight
    const allCompactCodes = shipmentPlanNumber.shipmentFeeConditionTypeCompactCode
    const allowedCodes = ['FC2, FC3, FC6, FC8']

    if (!allCompactCodes || !Array.isArray(allCompactCodes) || allCompactCodes.length === 0) {
      return []
    }

    // Filter chỉ lấy những codes được phép
    const compactCodes = allCompactCodes.filter((code) => allowedCodes.includes(code))

    if (compactCodes.length === 0) {
      return []
    }

    const shipmentFeeConditions = await this.shipmentFeeConditionsRepository.find({
      where: {
        code: In(compactCodes),
        isDeleted: false,
      },
      select: {
        id: true,
        code: true,
      },
    })

    const shipmentFeeConditionIds = shipmentFeeConditions.map((condition) => condition.id)

    if (shipmentFeeConditionIds.length === 0) {
      return []
    }
    const shipmentFeeConditionsToList = await this.shipmentFeeConditionsToListRepository.find({
      where: {
        shipmentFeeConditionsId: In(shipmentFeeConditionIds),
        isDeleted: false,
      },
      relations: {
        shipmentFeeConditionsList: true,
      },
    })

    const result = []
    const addedIds = new Set<string>()

    for (const toListEntity of shipmentFeeConditionsToList) {
      const feeConditionsList = await toListEntity.shipmentFeeConditionsList
      if (feeConditionsList && !feeConditionsList.isDeleted && !addedIds.has(feeConditionsList.id)) {
        result.push(feeConditionsList)
        addedIds.add(feeConditionsList.id)
      }
    }

    return result
  }
}
