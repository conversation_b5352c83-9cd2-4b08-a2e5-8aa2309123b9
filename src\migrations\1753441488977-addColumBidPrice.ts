import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddColumBidPrice1753441488977 implements MigrationInterface {
  name = 'AddColumBidPrice1753441488977'

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "bid_supplier" ADD "materialGroupId" uniqueidentifier`)
    await queryRunner.query(
      `ALTER TABLE "bid_supplier" ADD CONSTRAINT "FK_8d3f957b24b9cf8e7272a7c46a4" FOREIGN KEY ("materialGroupId") REFERENCES "material_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    )
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "bid_supplier" DROP CONSTRAINT "FK_8d3f957b24b9cf8e7272a7c46a4"`)
    await queryRunner.query(`ALTER TABLE "bid_supplier" DROP COLUMN "materialGroupId"`)
  }
}
